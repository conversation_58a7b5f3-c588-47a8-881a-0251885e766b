import{_ as A}from"./AppLayout.vue_vue_type_script_setup_true_lang-DwGvFXKf.js";import{r as y,o as j,d as n,e as a,f as d,u as c,m as B,g as u,i as t,P as p,t as h,F as w,p as H,y as L,a as v}from"./vendor-BhKTHoN5.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const D={class:"py-12"},N={class:"max-w-4xl mx-auto sm:px-6 lg:px-8"},S={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6"},T={class:"p-6 bg-white border-b border-gray-200"},E={class:"flex items-center justify-between"},F={key:0,class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},q={key:1,class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},z={class:"text-center py-8"},I={class:"mt-1 text-sm text-gray-500"},R={key:2,class:"bg-white rounded-lg shadow-sm border border-gray-200"},V={class:"p-6"},X={class:"space-y-4"},$={class:"flex items-start justify-between"},P={class:"flex-1"},Y={class:"text-lg font-medium text-gray-900 mb-1"},K={class:"flex items-center space-x-4 text-sm text-gray-500"},O={key:3,class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},W={class:"text-center py-8"},et={__name:"ChatHistory",setup(G){const b=[{title:"Chat History",href:"/chat-history"}],m=y([]),g=y(!1),l=y(null),x=async()=>{var r;g.value=!0,l.value=null;try{try{await v.post("/web-api/chat/update-titles",{},{headers:{Accept:"application/json","Content-Type":"application/json","X-CSRF-TOKEN":((r=document.querySelector('meta[name="csrf-token"]'))==null?void 0:r.getAttribute("content"))||"","X-Requested-With":"XMLHttpRequest"},withCredentials:!0})}catch(i){console.log("Title update failed (non-critical):",i)}const s=(await v.get("/web-api/chat/history")).data;let o=[];Array.isArray(s)?o=s:s.conversations&&Array.isArray(s.conversations)?o=s.conversations:s.data&&Array.isArray(s.data)&&(o=s.data);const M=o.filter(i=>!i.messages||!Array.isArray(i.messages)?!1:i.messages.some(f=>f&&f.content&&f.content.trim().length>0));m.value=M}catch(e){console.error("Error fetching chat history:",e),l.value="Failed to load chat history"}finally{g.value=!1}},_=r=>{if(r.title&&r.title.trim())return r.title;if(r.messages&&r.messages.length>0){const e=r.messages.find(s=>s.content&&s.content.trim());if(e)return e.content.length>50?e.content.substring(0,50)+"...":e.content}return"New Chat"},k=r=>{const e=new Date(r),o=Math.floor((new Date-e)/(1e3*60*60*24));return o===0?"Today":o===1?"Yesterday":o<7?`${o} days ago`:e.toLocaleDateString()},C=r=>r.messages?r.messages.filter(e=>e.content&&e.content.trim()).length:0;return j(()=>{x()}),(r,e)=>(a(),n(w,null,[d(c(B),{title:"Chat History - Medroid"}),d(A,{breadcrumbs:b},{default:u(()=>[t("div",D,[t("div",N,[t("div",S,[t("div",T,[t("div",E,[e[1]||(e[1]=t("div",null,[t("h1",{class:"text-2xl font-bold text-gray-900"},"Chat History"),t("p",{class:"text-gray-600 mt-1"},"View your previous conversations with Medroid AI")],-1)),d(c(p),{href:"/chat",class:"flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-teal-600 rounded-lg hover:bg-teal-700 transition-colors duration-200"},{default:u(()=>e[0]||(e[0]=[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),t("span",null,"New Chat",-1)])),_:1})])])]),g.value?(a(),n("div",F,e[2]||(e[2]=[t("div",{class:"flex items-center justify-center py-8"},[t("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600"}),t("span",{class:"ml-2 text-gray-600"},"Loading chat history...")],-1)]))):l.value?(a(),n("div",q,[t("div",z,[e[3]||(e[3]=t("svg",{class:"mx-auto h-12 w-12 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})],-1)),e[4]||(e[4]=t("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"Error loading chat history",-1)),t("p",I,h(l.value),1),t("button",{onClick:x,class:"mt-4 px-4 py-2 text-sm font-medium text-white bg-teal-600 rounded-lg hover:bg-teal-700"}," Try Again ")])])):m.value.length>0?(a(),n("div",R,[t("div",V,[t("div",X,[(a(!0),n(w,null,H(m.value,s=>(a(),L(c(p),{key:s._id||s.id,href:`/chat?conversation=${s._id||s.id}`,class:"block p-4 border border-gray-200 rounded-lg hover:border-teal-500 hover:bg-teal-50 transition-colors duration-200"},{default:u(()=>[t("div",$,[t("div",P,[t("h3",Y,h(_(s)),1),t("div",K,[t("span",null,h(C(s))+" messages",1),t("span",null,h(k(s.updated_at||s.createdAt)),1)])]),e[5]||(e[5]=t("div",{class:"flex items-center text-teal-600"},[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})])],-1))])]),_:2},1032,["href"]))),128))])])])):(a(),n("div",O,[t("div",W,[e[7]||(e[7]=t("div",{class:"w-20 h-20 bg-teal-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg"},[t("svg",{class:"w-10 h-10 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})])],-1)),e[8]||(e[8]=t("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"No chat history yet",-1)),e[9]||(e[9]=t("p",{class:"text-gray-600 mb-6"},"Start a conversation with Medroid AI to see your chat history here.",-1)),d(c(p),{href:"/chat",class:"inline-flex items-center space-x-2 px-6 py-3 text-sm font-medium text-white bg-teal-600 rounded-lg hover:bg-teal-700 transition-colors duration-200"},{default:u(()=>e[6]||(e[6]=[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})],-1),t("span",null,"Start Your First Chat",-1)])),_:1})])]))])])]),_:1})],64))}};export{et as default};
