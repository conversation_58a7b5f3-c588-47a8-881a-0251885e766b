import{r as p,o as B,d as n,e as d,f as y,u as N,m as S,g as D,i as e,n as U,x as $,t as a,F as v,p as f,A as w,j as z,l as m,v as b,q as L,s as q}from"./vendor-BhKTHoN5.js";import{_ as H}from"./AppLayout.vue_vue_type_script_setup_true_lang-DwGvFXKf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const F={class:"p-6"},R={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6"},W={class:"bg-white rounded-lg shadow p-6"},I={class:"flex items-center"},P={class:"ml-4"},G={class:"text-2xl font-bold text-gray-900"},J={class:"bg-white rounded-lg shadow p-6"},K={class:"flex items-center"},O={class:"ml-4"},Q={class:"text-2xl font-bold text-gray-900"},X={class:"bg-white rounded-lg shadow p-6"},Y={class:"flex items-center"},Z={class:"ml-4"},ee={class:"text-2xl font-bold text-gray-900"},te={class:"bg-white rounded-lg shadow p-6"},se={class:"flex items-center"},le={class:"ml-4"},oe={class:"text-2xl font-bold text-gray-900"},ae={class:"bg-white rounded-lg shadow"},re={key:0,class:"p-6"},ie={key:1,class:"p-6 text-center text-gray-500"},ne={key:2,class:"overflow-x-auto"},de={class:"min-w-full divide-y divide-gray-200"},ue={class:"bg-white divide-y divide-gray-200"},ce={class:"px-6 py-4 whitespace-nowrap"},pe={class:"text-sm font-medium text-gray-900"},me={class:"px-6 py-4 whitespace-nowrap"},xe={class:"text-sm text-gray-500"},ge={class:"px-6 py-4 whitespace-nowrap"},ve={class:"px-6 py-4 whitespace-nowrap"},be=["onClick"],ye={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},fe={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},we=["onClick"],he=["onClick"],_e={key:0,class:"fixed inset-0 bg-white bg-opacity-20 backdrop-blur-sm overflow-y-auto h-full w-full z-50"},ke={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white"},Te={class:"mt-3"},Ce={class:"text-lg font-medium text-gray-900 mb-4"},Ee=["value"],je={class:"flex items-center"},Me={class:"flex justify-end space-x-3 pt-4"},Ve={type:"submit",class:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"},$e={__name:"EmailTemplates",setup(Ae){const h=[{title:"Dashboard",href:"/dashboard"},{title:"Email Templates",href:"/email-templates"}],x=p(!1),i=p([]),c=p(!1),u=p(null),o=p({name:"",subject:"",body:"",type:"notification",is_active:!0}),_=[{value:"notification",label:"Notification"},{value:"appointment",label:"Appointment"},{value:"reminder",label:"Reminder"},{value:"welcome",label:"Welcome"},{value:"referral",label:"Referral"},{value:"payment",label:"Payment"}],g=async()=>{x.value=!0;try{const t=(await window.axios.get("/email-templates-list")).data||[];i.value=t.map(s=>({...s,type:V(s.slug||s.name)}))}catch(l){console.error("Error fetching email templates:",l),i.value=[]}finally{x.value=!1}},k=()=>{u.value=null,o.value={name:"",subject:"",body:"",type:"notification",is_active:!0},c.value=!0},T=l=>{u.value=l,o.value={...l,body:l.content||l.body||""},c.value=!0},C=async()=>{var l,t;try{const s={...o.value,content:o.value.body||o.value.content};delete s.body,u.value?(await window.axios.put(`/email-templates/${u.value.id}`,s),alert("Template updated successfully!")):(await window.axios.post("/email-templates",s),alert("Template created successfully!")),c.value=!1,await g()}catch(s){console.error("Error saving template:",s),alert("Error saving template: "+(((t=(l=s.response)==null?void 0:l.data)==null?void 0:t.message)||s.message))}},E=async l=>{var t,s;if(confirm("Are you sure you want to delete this template?"))try{await window.axios.delete(`/email-templates/${l}`),alert("Template deleted successfully!"),await g()}catch(r){console.error("Error deleting template:",r),alert("Error deleting template: "+(((s=(t=r.response)==null?void 0:t.data)==null?void 0:s.message)||r.message))}},j=async l=>{var t,s;try{await window.axios.put(`/email-templates/${l.id}`,{is_active:!l.is_active}),l.is_active=!l.is_active,alert("Template status updated successfully!")}catch(r){console.error("Error toggling template status:",r),alert("Error updating template status: "+(((s=(t=r.response)==null?void 0:t.data)==null?void 0:s.message)||r.message))}};B(()=>{g()});const M=l=>new Date(l).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),V=l=>l.includes("appointment")?"appointment":l.includes("reminder")?"reminder":l.includes("password")||l.includes("registration")?"welcome":l.includes("referral")?"referral":l.includes("payment")?"payment":"notification",A=l=>({notification:"bg-blue-100 text-blue-800",appointment:"bg-green-100 text-green-800",reminder:"bg-yellow-100 text-yellow-800",welcome:"bg-purple-100 text-purple-800",referral:"bg-pink-100 text-pink-800",payment:"bg-indigo-100 text-indigo-800"})[l]||"bg-gray-100 text-gray-800";return(l,t)=>(d(),n(v,null,[y(N(S),{title:"Email Templates"}),y(H,{breadcrumbs:h},{default:D(()=>[e("div",F,[e("div",{class:"mb-6 flex justify-between items-center"},[t[7]||(t[7]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Email Templates"),e("p",{class:"text-gray-600"},"Manage email templates for notifications and communications")],-1)),e("button",{onClick:k,class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"},t[6]||(t[6]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4v16m8-8H4"})],-1),$(" Create Template ")]))]),e("div",R,[e("div",W,[e("div",I,[t[9]||(t[9]=e("div",{class:"p-2 bg-blue-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})])],-1)),e("div",P,[t[8]||(t[8]=e("p",{class:"text-sm font-medium text-gray-600"},"Total Templates",-1)),e("p",G,a(i.value.length),1)])])]),e("div",J,[e("div",K,[t[11]||(t[11]=e("div",{class:"p-2 bg-green-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",O,[t[10]||(t[10]=e("p",{class:"text-sm font-medium text-gray-600"},"Active Templates",-1)),e("p",Q,a(i.value.filter(s=>s.is_active).length),1)])])]),e("div",X,[e("div",Y,[t[13]||(t[13]=e("div",{class:"p-2 bg-yellow-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",Z,[t[12]||(t[12]=e("p",{class:"text-sm font-medium text-gray-600"},"Appointment Templates",-1)),e("p",ee,a(i.value.filter(s=>s.type==="appointment").length),1)])])]),e("div",te,[e("div",se,[t[15]||(t[15]=e("div",{class:"p-2 bg-purple-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"})])],-1)),e("div",le,[t[14]||(t[14]=e("p",{class:"text-sm font-medium text-gray-600"},"Welcome Templates",-1)),e("p",oe,a(i.value.filter(s=>s.type==="welcome").length),1)])])])]),e("div",ae,[t[18]||(t[18]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h2",{class:"text-lg font-medium text-gray-900"},"Email Templates")],-1)),x.value?(d(),n("div",re,t[16]||(t[16]=[e("div",{class:"animate-pulse"},[e("div",{class:"h-4 bg-gray-200 rounded w-3/4 mb-4"}),e("div",{class:"h-4 bg-gray-200 rounded w-1/2 mb-4"}),e("div",{class:"h-4 bg-gray-200 rounded w-5/6"})],-1)]))):i.value.length===0?(d(),n("div",ie," No email templates found. Create your first template to get started. ")):(d(),n("div",ne,[e("table",de,[t[17]||(t[17]=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Name"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Subject"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Type"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Status"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Last Updated"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Actions")])],-1)),e("tbody",ue,[(d(!0),n(v,null,f(i.value,s=>(d(),n("tr",{key:s.id},[e("td",ce,[e("div",pe,a(s.name),1)]),e("td",me,[e("div",xe,a(s.subject),1)]),e("td",ge,[e("span",{class:w([A(s.type),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},a(s.type),3)]),e("td",ve,[e("button",{onClick:r=>j(s),class:w([s.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800","inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},a(s.is_active?"Active":"Inactive"),11,be)]),e("td",ye,a(M(s.updated_at)),1),e("td",fe,[e("button",{onClick:r=>T(s),class:"text-blue-600 hover:text-blue-900 mr-3"}," Edit ",8,we),e("button",{onClick:r=>E(s.id),class:"text-red-600 hover:text-red-900"}," Delete ",8,he)])]))),128))])])]))])]),c.value?(d(),n("div",_e,[e("div",ke,[e("div",Te,[e("h3",Ce,a(u.value?"Edit Template":"Create New Template"),1),e("form",{onSubmit:z(C,["prevent"]),class:"space-y-4"},[e("div",null,[t[19]||(t[19]=e("label",{class:"block text-sm font-medium text-gray-700"},"Name",-1)),m(e("input",{"onUpdate:modelValue":t[0]||(t[0]=s=>o.value.name=s),type:"text",required:"",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"},null,512),[[b,o.value.name]])]),e("div",null,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700"},"Subject",-1)),m(e("input",{"onUpdate:modelValue":t[1]||(t[1]=s=>o.value.subject=s),type:"text",required:"",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"},null,512),[[b,o.value.subject]])]),e("div",null,[t[21]||(t[21]=e("label",{class:"block text-sm font-medium text-gray-700"},"Type",-1)),m(e("select",{"onUpdate:modelValue":t[2]||(t[2]=s=>o.value.type=s),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"},[(d(),n(v,null,f(_,s=>e("option",{key:s.value,value:s.value},a(s.label),9,Ee)),64))],512),[[L,o.value.type]])]),e("div",null,[t[22]||(t[22]=e("label",{class:"block text-sm font-medium text-gray-700"},"Body",-1)),m(e("textarea",{"onUpdate:modelValue":t[3]||(t[3]=s=>o.value.body=s),rows:"6",required:"",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",placeholder:"Email template body content..."},null,512),[[b,o.value.body]])]),e("div",je,[m(e("input",{"onUpdate:modelValue":t[4]||(t[4]=s=>o.value.is_active=s),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[q,o.value.is_active]]),t[23]||(t[23]=e("label",{class:"ml-2 block text-sm text-gray-900"},"Active",-1))]),e("div",Me,[e("button",{type:"button",onClick:t[5]||(t[5]=s=>c.value=!1),class:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"}," Cancel "),e("button",Ve,a(u.value?"Update":"Create"),1)])],32)])])])):U("",!0)]),_:1})],64))}};export{$e as default};
