import{a as A,t as S,w as C,b as R,i as z,c as J}from"./index-CGRqDMLC.js";import{Z as j,r as V,w as $,C as L,c as b,a1 as m,a2 as W,o as D,u as I}from"./vendor-BhKTHoN5.js";const w=z?window:void 0;function x(e){var t;const s=m(e);return(t=s==null?void 0:s.$el)!=null?t:s}function K(...e){const t=[],s=()=>{t.forEach(n=>n()),t.length=0},l=(n,r,c,i)=>(n.addEventListener(r,c,i),()=>n.removeEventListener(r,c,i)),u=b(()=>{const n=S(m(e[0])).filter(r=>r!=null);return n.every(r=>typeof r!="string")?n:void 0}),a=C(()=>{var n,r;return[(r=(n=u.value)==null?void 0:n.map(c=>x(c)))!=null?r:[w].filter(c=>c!=null),S(m(u.value?e[1]:e[0])),S(I(u.value?e[2]:e[1])),m(u.value?e[3]:e[2])]},([n,r,c,i])=>{if(s(),!(n!=null&&n.length)||!(r!=null&&r.length)||!(c!=null&&c.length))return;const p=J(i)?{...i}:i;t.push(...n.flatMap(v=>r.flatMap(f=>c.map(h=>l(v,f,h,p)))))},{flush:"post"}),o=()=>{a(),s()};return R(s),o}function T(){const e=W(!1),t=j();return t&&D(()=>{e.value=!0},t),e}function B(e){const t=T();return b(()=>(t.value,!!e()))}function U(e){return typeof e=="function"?e:typeof e=="string"?t=>t.key===e:Array.isArray(e)?t=>e.includes(t.key):()=>!0}function G(...e){let t,s,l={};e.length===3?(t=e[0],s=e[1],l=e[2]):e.length===2?typeof e[1]=="object"?(t=!0,s=e[0],l=e[1]):(t=e[0],s=e[1]):(t=!0,s=e[0]);const{target:u=w,eventName:a="keydown",passive:o=!1,dedupe:n=!1}=l,r=U(t);return K(u,a,i=>{i.repeat&&m(n)||r(i)&&s(i)},o)}function Z(e){return JSON.parse(JSON.stringify(e))}function H(e,t,s={}){const{window:l=w,...u}=s;let a;const o=B(()=>l&&"ResizeObserver"in l),n=()=>{a&&(a.disconnect(),a=void 0)},r=b(()=>{const p=m(e);return Array.isArray(p)?p.map(v=>x(v)):[x(p)]}),c=$(r,p=>{if(n(),o.value&&l){a=new ResizeObserver(t);for(const v of p)v&&a.observe(v,u)}},{immediate:!0,flush:"post"}),i=()=>{n(),c()};return R(i),{isSupported:o,stop:i}}function Q(e,t,s,l={}){var u,a,o;const{clone:n=!1,passive:r=!1,eventName:c,deep:i=!1,defaultValue:p,shouldEmit:v}=l,f=j(),h=s||(f==null?void 0:f.emit)||((u=f==null?void 0:f.$emit)==null?void 0:u.bind(f))||((o=(a=f==null?void 0:f.proxy)==null?void 0:a.$emit)==null?void 0:o.bind(f==null?void 0:f.proxy));let y=c;t||(t="modelValue"),y=y||`update:${t.toString()}`;const M=d=>n?typeof n=="function"?n(d):Z(d):d,N=()=>A(e[t])?M(e[t]):p,P=d=>{v?v(d)&&h(y,d):h(y,d)};if(r){const d=N(),E=V(d);let O=!1;return $(()=>e[t],g=>{O||(O=!0,E.value=M(g),L(()=>O=!1))}),$(E,g=>{!O&&(g!==e[t]||i)&&P(g)},{deep:i}),E}else return b({get(){return N()},set(d){P(d)}})}function X(){const e=j(),t=V(),s=b(()=>{var o,n;return["#text","#comment"].includes((o=t.value)==null?void 0:o.$el.nodeName)?(n=t.value)==null?void 0:n.$el.nextElementSibling:x(t)}),l=Object.assign({},e.exposed),u={};for(const o in e.props)Object.defineProperty(u,o,{enumerable:!0,configurable:!0,get:()=>e.props[o]});if(Object.keys(l).length>0)for(const o in l)Object.defineProperty(u,o,{enumerable:!0,configurable:!0,get:()=>l[o]});Object.defineProperty(u,"$el",{enumerable:!0,configurable:!0,get:()=>e.vnode.el}),e.exposed=u;function a(o){t.value=o,o&&(Object.defineProperty(u,"$el",{enumerable:!0,configurable:!0,get:()=>o instanceof Element?o:o.$el}),e.exposed=u)}return{forwardRef:a,currentRef:t,currentElement:s}}export{X as a,K as b,x as c,w as d,H as e,T as f,G as o,Q as u};
