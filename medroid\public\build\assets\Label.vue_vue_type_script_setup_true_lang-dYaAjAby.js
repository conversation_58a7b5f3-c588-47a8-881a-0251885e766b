import{J as r,y as d,e as n,g as u,K as p,M as c,u as o,l as b,v as g,d as x,A as v,a3 as _,c as w}from"./vendor-BhKTHoN5.js";import{P as y,c as f}from"./Primitive-DSQomZit.js";import{a as V,u as h}from"./useForwardExpose-DjhPD9_V.js";const k=r({__name:"Label",props:{for:{},asChild:{type:Boolean},as:{default:"label"}},setup(l){const t=l;return V(),(a,s)=>(n(),d(o(y),c(t,{onMousedown:s[0]||(s[0]=e=>{!e.defaultPrevented&&e.detail>1&&e.preventDefault()})}),{default:u(()=>[p(a.$slots,"default")]),_:3},16))}}),$=r({__name:"Input",props:{defaultValue:{},modelValue:{},class:{}},emits:["update:modelValue"],setup(l,{emit:t}){const a=l,e=h(a,"modelValue",t,{passive:!0,defaultValue:a.defaultValue});return(B,i)=>b((n(),x("input",{"onUpdate:modelValue":i[0]||(i[0]=m=>_(e)?e.value=m:null),"data-slot":"input",class:v(o(f)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a.class))},null,2)),[[g,o(e)]])}}),D=r({__name:"Label",props:{for:{},asChild:{type:Boolean},as:{},class:{}},setup(l){const t=l,a=w(()=>{const{class:s,...e}=t;return e});return(s,e)=>(n(),d(o(k),c({"data-slot":"label"},a.value,{class:o(f)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t.class)}),{default:u(()=>[p(s.$slots,"default")]),_:3},16,["class"]))}});export{D as _,$ as a};
