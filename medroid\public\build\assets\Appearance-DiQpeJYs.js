import{c as z,d as p,e as r,i as e,u as s,t as g,I as S,J as _,F as C,p as I,f as c,A as F,y as h,ab as M,g as f,m as A}from"./vendor-BhKTHoN5.js";import{u as $,a as B}from"./app-Dd4xG9yF.js";import{_ as j}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{c as b}from"./createLucideIcon-YxmScYOV.js";import{_ as D}from"./HeadingSmall.vue_vue_type_script_setup_true_lang-DVe5uQrS.js";import{_ as N}from"./AppLayout.vue_vue_type_script_setup_true_lang-DwGvFXKf.js";import{_ as H}from"./Layout.vue_vue_type_script_setup_true_lang-CjtMa_dt.js";import"./Primitive-DSQomZit.js";import"./index-CFmBC9d8.js";import"./index-CGRqDMLC.js";/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const L=b("MonitorIcon",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const V=b("MoonIcon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const q=b("SunIcon",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),T={class:"font-size-control"},E={key:0,class:"flex items-center space-x-2"},J=["disabled"],P={class:"text-sm font-medium min-w-[60px] text-center"},R=["disabled"],U={key:1,class:"space-y-4"},Z={class:"flex items-center justify-between"},G={class:"space-y-3"},K={class:"flex items-center space-x-4"},O=["disabled"],Q={class:"flex-1 relative"},W=["value","max"],X=["disabled"],Y={class:"text-center"},ee={class:"text-sm font-medium text-gray-900"},te={class:"text-xs text-gray-500"},se={__name:"FontSizeControl",props:{compact:{type:Boolean,default:!1}},setup(y){const{currentFontSize:o,fontSizeName:d,fontSizeDescription:m,availableFontSizes:u,setFontSize:l,increaseFontSize:n,decreaseFontSize:i,resetFontSize:x}=$(),k=z(()=>u.value.findIndex(v=>v.key===o.value)),w=v=>{const t=parseInt(v.target.value),a=u.value[t];a&&l(a.key)};return(v,t)=>(r(),p("div",T,[y.compact?(r(),p("div",E,[e("button",{onClick:t[0]||(t[0]=(...a)=>s(i)&&s(i)(...a)),disabled:s(o)==="small",class:"p-1 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed",title:"Decrease font size"},t[5]||(t[5]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 12H4"})],-1)]),8,J),e("span",P,g(s(d)),1),e("button",{onClick:t[1]||(t[1]=(...a)=>s(n)&&s(n)(...a)),disabled:s(o)==="xlarge",class:"p-1 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed",title:"Increase font size"},t[6]||(t[6]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4v16m8-8H4"})],-1)]),8,R)])):(r(),p("div",U,[e("div",Z,[t[7]||(t[7]=e("label",{class:"text-sm font-medium text-gray-700"},"Font Size",-1)),e("button",{onClick:t[2]||(t[2]=(...a)=>s(x)&&s(x)(...a)),class:"text-xs text-gray-500 hover:text-gray-700"}," Reset to Default ")]),e("div",G,[e("div",K,[e("button",{onClick:t[3]||(t[3]=(...a)=>s(i)&&s(i)(...a)),disabled:s(o)==="small",class:"p-2 rounded-md border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",title:"Decrease font size"},t[8]||(t[8]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 12H4"})],-1)]),8,O),e("div",Q,[e("input",{type:"range",value:k.value,min:0,max:s(u).length-1,onInput:w,class:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"},null,40,W),t[9]||(t[9]=e("div",{class:"flex justify-between text-xs text-gray-500 mt-1"},[e("span",null,"A"),e("span",{class:"text-sm"},"A"),e("span",{class:"text-lg"},"A"),e("span",{class:"text-xl"},"A")],-1))]),e("button",{onClick:t[4]||(t[4]=(...a)=>s(n)&&s(n)(...a)),disabled:s(o)==="xlarge",class:"p-2 rounded-md border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",title:"Increase font size"},t[10]||(t[10]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4v16m8-8H4"})],-1)]),8,X)]),e("div",Y,[e("div",ee,g(s(d)),1),e("div",te,g(s(m)),1)]),t[11]||(t[11]=S('<div class="p-3 bg-gray-50 rounded-lg border" data-v-7e5c7ce5><div class="text-xs text-gray-500 mb-1" data-v-7e5c7ce5>Preview:</div><div class="space-y-1" data-v-7e5c7ce5><p class="text-sm" data-v-7e5c7ce5>Small text example</p><p class="text-base" data-v-7e5c7ce5>Normal text example</p><p class="text-lg" data-v-7e5c7ce5>Large text example</p></div></div>',1))])]))]))}},ae=j(se,[["__scopeId","data-v-7e5c7ce5"]]),oe={class:"space-y-8"},ne={class:"inline-flex gap-1 rounded-lg bg-neutral-100 p-1 dark:bg-neutral-800"},re=["onClick"],le={class:"ml-1.5 text-sm"},ie={class:"max-w-md"},de=_({__name:"AppearanceTabs",setup(y){const{appearance:o,updateAppearance:d}=B(),m=[{value:"light",Icon:q,label:"Light"},{value:"dark",Icon:V,label:"Dark"},{value:"system",Icon:L,label:"System"}];return(u,l)=>(r(),p("div",oe,[e("div",null,[l[0]||(l[0]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4"},"Theme",-1)),e("div",ne,[(r(),p(C,null,I(m,({value:n,Icon:i,label:x})=>e("button",{key:n,onClick:k=>s(d)(n),class:F(["flex items-center rounded-md px-3.5 py-1.5 transition-colors",s(o)===n?"bg-white shadow-xs dark:bg-neutral-700 dark:text-neutral-100":"text-neutral-500 hover:bg-neutral-200/60 hover:text-black dark:text-neutral-400 dark:hover:bg-neutral-700/60"])},[(r(),h(M(i),{class:"-ml-1 h-4 w-4"})),e("span",le,g(x),1)],10,re)),64))])]),e("div",null,[l[1]||(l[1]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4"},"Font Size",-1)),e("div",ie,[c(ae)])])]))}}),ce={class:"space-y-6"},_e=_({__name:"Appearance",setup(y){const o=[{title:"Appearance settings",href:"/settings/appearance"}];return(d,m)=>(r(),h(N,{breadcrumbs:o},{default:f(()=>[c(s(A),{title:"Appearance settings"}),c(H,null,{default:f(()=>[e("div",ce,[c(D,{title:"Appearance settings",description:"Update your account's appearance settings"}),c(de)])]),_:1})]),_:1}))}});export{_e as default};
