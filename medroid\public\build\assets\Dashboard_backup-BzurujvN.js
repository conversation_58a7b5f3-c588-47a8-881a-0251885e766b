import{r as f,c as z,o as T,d as n,e as r,f as u,u as c,m as L,g,i as t,n as m,t as s,l as q,q as I,x as d,F as b,p as k,y as N,P as v,N as S,A as F,a as K}from"./vendor-BhKTHoN5.js";import{_ as $}from"./AppLayout.vue_vue_type_script_setup_true_lang-DwGvFXKf.js";import{_ as O}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const E={class:"unified-dashboard"},Y={class:"dashboard-container"},Q={class:"dashboard-header"},X={class:"header-content"},G={class:"dashboard-title"},W={class:"dashboard-subtitle"},J={class:"header-actions"},Z=["disabled"],tt={key:0,class:"fas fa-spinner fa-spin"},et={key:1,class:"fas fa-sync-alt"},st={key:0,class:"dashboard-content bg-white rounded-lg shadow p-6 mb-6"},ot={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},rt={class:"bg-blue-50 rounded-lg p-4 border-l-4 border-blue-500"},nt={class:"flex justify-between"},lt={class:"text-2xl font-bold text-blue-600"},at={class:"text-xs text-gray-400"},it={class:"bg-green-50 rounded-lg p-4 border-l-4 border-green-500"},dt={class:"flex justify-between"},ut={class:"text-2xl font-bold text-green-600"},ct={class:"text-xs text-gray-400"},gt={class:"bg-purple-50 rounded-lg p-4 border-l-4 border-purple-500"},vt={class:"flex justify-between"},mt={class:"text-2xl font-bold text-purple-600"},ht={class:"bg-orange-50 rounded-lg p-4 border-l-4 border-orange-500"},pt={class:"flex justify-between"},xt={class:"text-2xl font-bold text-orange-600"},wt={class:"text-xs text-gray-400"},ft={key:1,class:"dashboard-content bg-white rounded-lg shadow p-6 mb-6"},bt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},_t={class:"text-sm font-medium text-gray-900"},yt={class:"dashboard-content bg-white rounded-lg shadow p-6 mb-6"},kt={class:"flex justify-between items-center mb-4"},Mt={class:"text-sm text-gray-500"},jt={key:0,class:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4"},At={class:"flex"},Ct={class:"text-sm"},zt={key:1,class:"flex justify-center items-center py-8"},Bt={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},Dt={class:"bg-white rounded-lg shadow p-4 border-l-4 border-blue-500 hover:shadow-md transition duration-150"},Vt={class:"flex justify-between"},Pt={class:"text-2xl font-bold"},Ht={class:"flex items-center mt-1"},Rt={key:0,class:"text-xs text-green-600 flex items-center"},Ut={key:1,class:"text-xs text-red-600 flex items-center"},Tt={key:2,class:"text-xs text-gray-500"},Lt={class:"bg-white rounded-lg shadow p-4 border-l-4 border-green-500 hover:shadow-md transition duration-150"},qt={class:"flex justify-between"},It={class:"text-2xl font-bold"},Nt={class:"flex items-center mt-1"},St={class:"text-xs text-gray-500"},Ft={class:"bg-white rounded-lg shadow p-4 border-l-4 border-purple-500 hover:shadow-md transition duration-150"},Kt={class:"flex justify-between"},$t={class:"text-2xl font-bold"},Ot={class:"flex items-center mt-1"},Et={class:"text-xs text-gray-500"},Yt={class:"bg-white rounded-lg shadow p-4 border-l-4 border-red-500 hover:shadow-md transition duration-150"},Qt={class:"flex justify-between"},Xt={class:"text-2xl font-bold"},Gt={class:"flex items-center mt-1"},Wt={class:"text-xs text-gray-500"},Jt={class:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6"},Zt={class:"bg-white rounded-lg shadow p-4 hover:shadow-md transition duration-150"},te={class:"grid grid-cols-3 gap-2"},ee={class:"text-center p-2 bg-gray-50 rounded"},se={class:"text-xl font-bold"},oe={class:"text-center p-2 bg-gray-50 rounded"},re={class:"text-xl font-bold"},ne={class:"text-center p-2 bg-gray-50 rounded"},le={class:"text-xl font-bold"},ae={class:"bg-white rounded-lg shadow p-4 hover:shadow-md transition duration-150"},ie={class:"grid grid-cols-3 gap-2"},de={class:"text-center p-2 bg-gray-50 rounded"},ue={class:"text-xl font-bold"},ce={class:"text-center p-2 bg-gray-50 rounded"},ge={class:"text-xl font-bold"},ve={class:"text-center p-2 bg-gray-50 rounded"},me={class:"text-xl font-bold"},he={class:"bg-white rounded-lg shadow p-4 hover:shadow-md transition duration-150"},pe={class:"grid grid-cols-2 gap-2"},xe={class:"text-center p-2 bg-gray-50 rounded"},we={class:"text-xl font-bold"},fe={class:"text-center p-2 bg-gray-50 rounded"},be={class:"text-xl font-bold"},_e={key:2,class:"mt-4"},ye={class:"bg-white rounded-lg shadow p-4"},ke={class:"flex justify-between items-center mb-1"},Me={class:"text-gray-700"},je={class:"text-gray-900 font-semibold"},Ae={class:"w-full bg-gray-200 rounded-full h-2.5"},Ce={class:"dashboard-content bg-white rounded-lg shadow p-6 mb-6"},ze={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},Be={class:"bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition duration-150"},De={class:"p-5"},Ve={class:"mt-4"},Pe={class:"bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition duration-150"},He={class:"p-5"},Re={class:"mt-4"},Ue={class:"bg-gradient-to-r from-cyan-500 to-cyan-600 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition duration-150"},Te={class:"p-5"},Le={class:"mt-4"},qe={key:3,class:"dashboard-content bg-white rounded-lg shadow p-6 mb-6"},Ie={class:"bg-white overflow-hidden shadow-sm rounded-lg"},Ne={class:"p-6"},Se={class:"flex justify-between items-center mb-4"},Fe={key:0,class:"text-center py-8"},Ke={key:1,class:"space-y-4"},$e={class:"flex justify-between items-start"},Oe={class:"font-medium text-gray-900"},Ee={class:"text-sm text-gray-600"},Ye={class:"text-sm text-gray-500 mt-1"},Qe={class:"bg-white overflow-hidden shadow-sm rounded-lg"},Xe={class:"p-6"},Ge={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},We={__name:"Dashboard_backup",props:{stats:{type:Object,default:()=>({upcomingAppointments:0,completedAppointments:0,healthRecords:0})},recentAppointments:{type:Array,default:()=>[]},quickActions:{type:Array,default:()=>[]},kpiData:{type:Object,default:()=>({})},user:{type:Object,required:!0}},setup(i){const x=i,B=[{title:"Dashboard",href:"/dashboard"}],p=f(!1),M=f("Never"),w=f("admin"),h=f(null),o=f({total_users:0,monthly_active_users:0,daily_active_users:0,user_growth_rate:0,total_appointments:0,monthly_appointments:0,daily_appointments:0,appointment_completion_rate:0,total_consults:0,monthly_consults:0,daily_consults:0,consult_to_appointment_ratio:0,repeat_consult_users:0,diagnostic_accuracy:0,diagnostic_feedback_count:0,top_accurate_diagnoses:[],top_inaccurate_diagnoses:[],user_locations:[],total_revenue:0,monthly_revenue:0,average_appointment_value:0,...x.kpiData}),j=z(()=>{var a,e;return((e=(a=x.user)==null?void 0:a.roles)==null?void 0:e.some(l=>l.name==="admin"))||!1}),_=z(()=>{var a,e;return((e=(a=x.user)==null?void 0:a.user_permissions)==null?void 0:e.includes("view analytics"))||!1}),D=a=>new Date(a).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),V=a=>({scheduled:"bg-blue-100 text-blue-800",confirmed:"bg-green-100 text-green-800",in_progress:"bg-yellow-100 text-yellow-800",completed:"bg-gray-100 text-gray-800",cancelled:"bg-red-100 text-red-800"})[a]||"bg-gray-100 text-gray-800",P=()=>({admin:"Admin Dashboard",provider:"Provider Dashboard",patient:"Patient Dashboard",care_manager:"Care Manager Dashboard"})[w.value]||"Dashboard",H=()=>({admin:"Manage your healthcare platform from one place",provider:"Manage your patients and appointments",patient:"Track your health and appointments",care_manager:"Monitor patient care and compliance"})[w.value]||"Manage your healthcare services",A=(a,e)=>e?Math.round(a/e*100):0,R=a=>{if(!o.value.user_locations||o.value.user_locations.length===0)return 0;const e=Math.max(...o.value.user_locations.map(l=>l.count));return Math.round(a/e*100)},U=()=>{C(!0)},C=async(a=!1)=>{if(!_.value){h.value="You do not have permission to view analytics data.";return}p.value=!0,h.value=null;try{const e=await K.get("/api/management/dashboard/kpi",{params:{refresh:a},headers:{Accept:"application/json","X-Requested-With":"XMLHttpRequest"}});o.value={...o.value,...e.data},M.value=new Date().toLocaleString()}catch(e){console.error("Error fetching KPI data:",e),e.response?h.value=`Failed to load KPI data: ${e.response.data.message||e.message}`:e.request?h.value="Failed to load KPI data: No response from server":h.value=`Failed to load KPI data: ${e.message}`}finally{p.value=!1}};return T(()=>{x.user&&(w.value=x.user.role||"admin")}),(a,e)=>(r(),n(b,null,[u(c(L),{title:"Dashboard"}),u($,{breadcrumbs:B},{default:g(()=>[t("div",E,[t("div",Y,[t("div",Q,[t("div",X,[t("h1",G,s(P()),1),t("p",W,s(H()),1)]),t("div",J,[j.value?q((r(),n("select",{key:0,"onUpdate:modelValue":e[0]||(e[0]=l=>w.value=l),onChange:U,class:"role-selector"},e[2]||(e[2]=[t("option",{value:"admin"},"Admin View",-1),t("option",{value:"provider"},"Provider View",-1),t("option",{value:"patient"},"Patient View",-1),t("option",{value:"care_manager"},"Care Manager View",-1)]),544)),[[I,w.value]]):m("",!0),_.value?(r(),n("button",{key:1,onClick:e[1]||(e[1]=l=>C(!0)),class:"refresh-btn",disabled:p.value},[p.value?(r(),n("i",tt)):(r(),n("i",et)),d(" "+s(p.value?"Refreshing...":"Refresh Data"),1)],8,Z)):m("",!0)])]),j.value?(r(),n("div",st,[e[12]||(e[12]=t("h3",{class:"text-lg font-semibold text-gray-700 mb-4"},"Platform Overview",-1)),t("div",ot,[t("div",rt,[t("div",nt,[t("div",null,[e[3]||(e[3]=t("p",{class:"text-sm text-gray-500"},"Total Users",-1)),t("p",lt,s(i.stats.totalUsers||0),1),t("p",at,s(i.stats.newUsersThisMonth||0)+" new this month",1)]),e[4]||(e[4]=t("div",{class:"text-blue-500"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-8 w-8",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"})])],-1))])]),t("div",it,[t("div",dt,[t("div",null,[e[5]||(e[5]=t("p",{class:"text-sm text-gray-500"},"Total Appointments",-1)),t("p",ut,s(i.stats.totalAppointments||0),1),t("p",ct,s(i.stats.newAppointmentsThisMonth||0)+" new this month",1)]),e[6]||(e[6]=t("div",{class:"text-green-500"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-8 w-8",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1))])]),t("div",gt,[t("div",vt,[t("div",null,[e[7]||(e[7]=t("p",{class:"text-sm text-gray-500"},"Healthcare Providers",-1)),t("p",mt,s(i.stats.totalProviders||0),1),e[8]||(e[8]=t("p",{class:"text-xs text-gray-400"},"Active providers",-1))]),e[9]||(e[9]=t("div",{class:"text-purple-500"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-8 w-8",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])],-1))])]),t("div",ht,[t("div",pt,[t("div",null,[e[10]||(e[10]=t("p",{class:"text-sm text-gray-500"},"Monthly Revenue",-1)),t("p",xt,"$"+s(i.stats.monthlyRevenue||0),1),t("p",wt,"Total: $"+s(i.stats.totalRevenue||0),1)]),e[11]||(e[11]=t("div",{class:"text-orange-500"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-8 w-8",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1))])])])])):m("",!0),i.quickActions.length>0?(r(),n("div",ft,[e[14]||(e[14]=t("h3",{class:"text-lg font-semibold text-gray-700 mb-4"},"Quick Actions",-1)),t("div",bt,[(r(!0),n(b,null,k(i.quickActions,l=>(r(),N(c(v),{key:l.href,href:l.href,class:"flex items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition duration-150"},{default:g(()=>[e[13]||(e[13]=t("div",{class:"flex-shrink-0 mr-3"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6 text-gray-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})])],-1)),t("div",null,[t("h4",_t,s(l.title),1)])]),_:2},1032,["href"]))),128))])])):m("",!0),_.value?(r(),n(b,{key:2},[t("div",yt,[t("div",kt,[e[15]||(e[15]=t("h3",{class:"text-lg font-semibold text-gray-700"},"Key Performance Indicators",-1)),t("div",Mt," Last updated: "+s(M.value),1)]),h.value?(r(),n("div",jt,[t("div",At,[e[17]||(e[17]=t("div",{class:"py-1"},[t("svg",{class:"fill-current h-6 w-6 text-red-500 mr-4",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20"},[t("path",{d:"M2.93 17.07A10 10 0 1 1 17.07 2.93 10 10 0 0 1 2.93 17.07zm12.73-1.41A8 8 0 1 0 4.34 4.34a8 8 0 0 0 11.32 11.32zM9 11V9h2v6H9v-4zm0-6h2v2H9V5z"})])],-1)),t("div",null,[e[16]||(e[16]=t("p",{class:"font-bold"},"Error",-1)),t("p",Ct,s(h.value),1)])])])):m("",!0),p.value?(r(),n("div",zt,e[18]||(e[18]=[t("div",{class:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"},null,-1),t("span",{class:"ml-3 text-gray-600"},"Loading KPI data...",-1)]))):m("",!0),t("div",Bt,[t("div",Dt,[t("div",Vt,[t("div",null,[e[21]||(e[21]=t("p",{class:"text-sm text-gray-500"},"Total Users",-1)),t("p",Pt,s(o.value.total_users||0),1),t("div",Ht,[o.value.user_growth_rate>0?(r(),n("span",Rt,[e[19]||(e[19]=t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3 w-3 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 10l7-7m0 0l7 7m-7-7v18"})],-1)),d(" "+s(o.value.user_growth_rate||0)+"% ",1)])):o.value.user_growth_rate<0?(r(),n("span",Ut,[e[20]||(e[20]=t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-3 w-3 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 14l-7 7m0 0l-7-7m7 7V3"})],-1)),d(" "+s(Math.abs(o.value.user_growth_rate||0))+"% ",1)])):(r(),n("span",Tt,"No change"))])]),e[22]||(e[22]=t("div",{class:"text-blue-500"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-8 w-8",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"})])],-1))])]),t("div",Lt,[t("div",qt,[t("div",null,[e[23]||(e[23]=t("p",{class:"text-sm text-gray-500"},"Monthly Active Users",-1)),t("p",It,s(o.value.monthly_active_users||0),1),t("div",Nt,[t("span",St,s(A(o.value.monthly_active_users,o.value.total_users))+"% of total",1)])]),e[24]||(e[24]=t("div",{class:"text-green-500"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-8 w-8",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})])],-1))])]),t("div",Ft,[t("div",Kt,[t("div",null,[e[25]||(e[25]=t("p",{class:"text-sm text-gray-500"},"Daily Active Users",-1)),t("p",$t,s(o.value.daily_active_users||0),1),t("div",Ot,[t("span",Et,s(A(o.value.daily_active_users,o.value.monthly_active_users))+"% of monthly",1)])]),e[26]||(e[26]=t("div",{class:"text-purple-500"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-8 w-8",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),t("div",Yt,[t("div",Qt,[t("div",null,[e[27]||(e[27]=t("p",{class:"text-sm text-gray-500"},"Diagnostic Accuracy",-1)),t("p",Xt,s(o.value.diagnostic_accuracy||0)+"%",1),t("div",Gt,[t("span",Wt,"Based on "+s(o.value.diagnostic_feedback_count||0)+" feedbacks",1)])]),e[28]||(e[28]=t("div",{class:"text-red-500"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-8 w-8",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])])]),t("div",Jt,[t("div",Zt,[e[32]||(e[32]=t("h4",{class:"font-semibold text-gray-700 mb-3 flex items-center"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 mr-2 text-blue-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})]),d(" Appointments ")],-1)),t("div",te,[t("div",ee,[e[29]||(e[29]=t("p",{class:"text-sm text-gray-500"},"Total",-1)),t("p",se,s(o.value.total_appointments||0),1)]),t("div",oe,[e[30]||(e[30]=t("p",{class:"text-sm text-gray-500"},"Monthly",-1)),t("p",re,s(o.value.monthly_appointments||0),1)]),t("div",ne,[e[31]||(e[31]=t("p",{class:"text-sm text-gray-500"},"Daily",-1)),t("p",le,s(o.value.daily_appointments||0),1)])])]),t("div",ae,[e[36]||(e[36]=t("h4",{class:"font-semibold text-gray-700 mb-3 flex items-center"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 mr-2 text-green-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"})]),d(" AI Consults ")],-1)),t("div",ie,[t("div",de,[e[33]||(e[33]=t("p",{class:"text-sm text-gray-500"},"Total",-1)),t("p",ue,s(o.value.total_consults||0),1)]),t("div",ce,[e[34]||(e[34]=t("p",{class:"text-sm text-gray-500"},"Monthly",-1)),t("p",ge,s(o.value.monthly_consults||0),1)]),t("div",ve,[e[35]||(e[35]=t("p",{class:"text-sm text-gray-500"},"Daily",-1)),t("p",me,s(o.value.daily_consults||0),1)])])]),t("div",he,[e[39]||(e[39]=t("h4",{class:"font-semibold text-gray-700 mb-3 flex items-center"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 mr-2 text-purple-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})]),d(" Conversion Metrics ")],-1)),t("div",pe,[t("div",xe,[e[37]||(e[37]=t("p",{class:"text-sm text-gray-500"},"Consult to Appt Ratio",-1)),t("p",we,s(o.value.consult_to_appointment_ratio||0)+"%",1)]),t("div",fe,[e[38]||(e[38]=t("p",{class:"text-sm text-gray-500"},"Repeat Consult Users",-1)),t("p",be,s(o.value.repeat_consult_users||0),1)])])])]),o.value.user_locations&&o.value.user_locations.length>0?(r(),n("div",_e,[e[40]||(e[40]=t("h4",{class:"font-semibold text-gray-700 mb-3 flex items-center"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 mr-2 text-teal-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]),d(" User Locations ")],-1)),t("div",ye,[(r(!0),n(b,null,k(o.value.user_locations,(l,y)=>(r(),n("div",{key:y,class:"mb-3"},[t("div",ke,[t("span",Me,s(l.location),1),t("span",je,s(l.count),1)]),t("div",Ae,[t("div",{class:"bg-teal-600 h-2.5 rounded-full",style:S({width:R(l.count)+"%"})},null,4)])]))),128))])])):m("",!0)]),t("div",Ce,[e[47]||(e[47]=t("h3",{class:"text-lg font-semibold text-gray-700 mb-4"},"Management Dashboard",-1)),t("div",ze,[t("div",Be,[t("div",De,[e[42]||(e[42]=t("div",{class:"flex items-center"},[t("div",{class:"flex-shrink-0 bg-white p-3 rounded-full"},[t("svg",{class:"h-6 w-6 text-blue-600",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"})])]),t("div",{class:"ml-4"},[t("h3",{class:"text-lg font-medium text-white"},"Users"),t("p",{class:"text-blue-100"},"Manage system users")])],-1)),t("div",Ve,[u(c(v),{href:"/users",class:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-blue-600 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},{default:g(()=>e[41]||(e[41]=[d(" View Users ")])),_:1})])])]),t("div",Pe,[t("div",He,[e[44]||(e[44]=t("div",{class:"flex items-center"},[t("div",{class:"flex-shrink-0 bg-white p-3 rounded-full"},[t("svg",{class:"h-6 w-6 text-green-600",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])]),t("div",{class:"ml-4"},[t("h3",{class:"text-lg font-medium text-white"},"Providers"),t("p",{class:"text-green-100"},"Manage healthcare providers")])],-1)),t("div",Re,[u(c(v),{href:"/providers",class:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-green-600 bg-white hover:bg-green-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"},{default:g(()=>e[43]||(e[43]=[d(" View Providers ")])),_:1})])])]),t("div",Ue,[t("div",Te,[e[46]||(e[46]=t("div",{class:"flex items-center"},[t("div",{class:"flex-shrink-0 bg-white p-3 rounded-full"},[t("svg",{class:"h-6 w-6 text-cyan-600",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"})])]),t("div",{class:"ml-4"},[t("h3",{class:"text-lg font-medium text-white"},"Patients"),t("p",{class:"text-cyan-100"},"Manage patients")])],-1)),t("div",Le,[u(c(v),{href:"/patients",class:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-cyan-600 bg-white hover:bg-cyan-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500"},{default:g(()=>e[45]||(e[45]=[d(" View Patients ")])),_:1})])])])])])],64)):m("",!0),_.value?m("",!0):(r(),n("div",qe,e[48]||(e[48]=[t("div",{class:"text-center py-8"},[t("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})]),t("h3",{class:"mt-2 text-lg font-medium text-gray-900"},"Analytics Access Required"),t("p",{class:"mt-1 text-sm text-gray-500"}," You don't have permission to view analytics data. Please contact an administrator if you need access. ")],-1)]))),t("div",Ie,[t("div",Ne,[t("div",Se,[e[50]||(e[50]=t("h3",{class:"text-lg font-medium text-gray-900"},"Recent Appointments",-1)),u(c(v),{href:"/patient/appointments",class:"text-blue-600 hover:text-blue-800 text-sm font-medium"},{default:g(()=>e[49]||(e[49]=[d(" View All ")])),_:1})]),i.recentAppointments.length===0?(r(),n("div",Fe,[e[52]||(e[52]=t("p",{class:"text-gray-500"},"No appointments yet.",-1)),u(c(v),{href:"/providers",class:"mt-2 inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700"},{default:g(()=>e[51]||(e[51]=[d(" Book Your First Appointment ")])),_:1})])):(r(),n("div",Ke,[(r(!0),n(b,null,k(i.recentAppointments,l=>{var y;return r(),n("div",{key:l.id,class:"border border-gray-200 rounded-lg p-4"},[t("div",$e,[t("div",null,[t("h4",Oe," Dr. "+s(l.provider.user.name),1),t("p",Ee,s(((y=l.provider)==null?void 0:y.specialization)||"General Practice"),1),t("p",Ye,s(D(l.scheduled_at)),1)]),t("span",{class:F([V(l.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},s(l.status),3)])])}),128))]))])]),t("div",Qe,[t("div",Xe,[e[57]||(e[57]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Quick Actions",-1)),t("div",Ge,[u(c(v),{href:"/chat",class:"flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"},{default:g(()=>e[53]||(e[53]=[t("div",{class:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3"},[t("svg",{class:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})])],-1),t("span",{class:"text-sm font-medium text-gray-900"},"AI Chat",-1)])),_:1}),u(c(v),{href:"/providers",class:"flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"},{default:g(()=>e[54]||(e[54]=[t("div",{class:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3"},[t("svg",{class:"w-4 h-4 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])],-1),t("span",{class:"text-sm font-medium text-gray-900"},"Find Doctors",-1)])),_:1}),u(c(v),{href:"/appointments/create",class:"flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"},{default:g(()=>e[55]||(e[55]=[t("div",{class:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-3"},[t("svg",{class:"w-4 h-4 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1),t("span",{class:"text-sm font-medium text-gray-900"},"Book Appointment",-1)])),_:1}),u(c(v),{href:"/settings/profile",class:"flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"},{default:g(()=>e[56]||(e[56]=[t("div",{class:"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-3"},[t("svg",{class:"w-4 h-4 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})])],-1),t("span",{class:"text-sm font-medium text-gray-900"},"Settings",-1)])),_:1})])])])])])]),_:1})],64))}},os=O(We,[["__scopeId","data-v-31170a5f"]]);export{os as default};
