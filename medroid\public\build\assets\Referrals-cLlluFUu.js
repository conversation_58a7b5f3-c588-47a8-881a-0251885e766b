import{_ as b}from"./AppLayout.vue_vue_type_script_setup_true_lang-DwGvFXKf.js";import{r as c,o as k,d as o,e as d,f as u,u as M,m as C,g as R,i as t,t as a,F as f,p as B,A as z}from"./vendor-BhKTHoN5.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const D={class:"p-6"},S={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6"},A={class:"bg-white rounded-lg shadow p-6"},N={class:"flex items-center"},j={class:"ml-4"},E={class:"text-2xl font-bold text-gray-900"},V={class:"bg-white rounded-lg shadow p-6"},$={class:"flex items-center"},F={class:"ml-4"},L={class:"text-2xl font-bold text-gray-900"},U={class:"bg-white rounded-lg shadow p-6"},H={class:"flex items-center"},P={class:"ml-4"},T={class:"text-2xl font-bold text-gray-900"},q={class:"bg-white rounded-lg shadow p-6"},G={class:"flex items-center"},I={class:"ml-4"},J={class:"text-2xl font-bold text-gray-900"},K={class:"bg-white rounded-lg shadow"},O={key:0,class:"p-6"},Q={key:1,class:"p-6 text-center text-gray-500"},W={key:2,class:"overflow-x-auto"},X={class:"min-w-full divide-y divide-gray-200"},Y={class:"bg-white divide-y divide-gray-200"},Z={class:"px-6 py-4 whitespace-nowrap"},tt={class:"text-sm font-medium text-gray-900"},et={class:"text-sm text-gray-500"},st={class:"px-6 py-4 whitespace-nowrap"},rt={class:"text-sm font-medium text-gray-900"},at={class:"text-sm text-gray-500"},ot={class:"px-6 py-4 whitespace-nowrap"},dt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},lt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},mt={__name:"Referrals",setup(nt){const _=[{title:"Dashboard",href:"/dashboard"},{title:"Referrals",href:"/referrals"}],n=c(!1),i=c([]),l=c({total_referrals:0,completed_referrals:0,pending_referrals:0,total_credits_awarded:0}),h=async()=>{n.value=!0;try{const s=await window.axios.get("/referrals-list");i.value=s.data.data||[]}catch(s){console.error("Error fetching referrals:",s)}finally{n.value=!1}},y=async()=>{try{const s=await window.axios.get("/referrals-stats");l.value=s.data}catch(s){console.error("Error fetching referral stats:",s)}};k(()=>{h(),y()});const v=s=>new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),w=s=>{switch(s){case"completed":return"bg-green-100 text-green-800";case"pending":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}};return(s,e)=>(d(),o(f,null,[u(M(C),{title:"Referrals Management"}),u(b,{breadcrumbs:_},{default:R(()=>[t("div",D,[e[11]||(e[11]=t("div",{class:"mb-6"},[t("h1",{class:"text-2xl font-bold text-gray-900"},"Referrals Management"),t("p",{class:"text-gray-600"},"Manage and track user referrals and rewards")],-1)),t("div",S,[t("div",A,[t("div",N,[e[1]||(e[1]=t("div",{class:"p-2 bg-blue-100 rounded-lg"},[t("svg",{class:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"})])],-1)),t("div",j,[e[0]||(e[0]=t("p",{class:"text-sm font-medium text-gray-600"},"Total Referrals",-1)),t("p",E,a(l.value.total_referrals),1)])])]),t("div",V,[t("div",$,[e[3]||(e[3]=t("div",{class:"p-2 bg-green-100 rounded-lg"},[t("svg",{class:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),t("div",F,[e[2]||(e[2]=t("p",{class:"text-sm font-medium text-gray-600"},"Completed",-1)),t("p",L,a(l.value.completed_referrals),1)])])]),t("div",U,[t("div",H,[e[5]||(e[5]=t("div",{class:"p-2 bg-yellow-100 rounded-lg"},[t("svg",{class:"w-6 h-6 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),t("div",P,[e[4]||(e[4]=t("p",{class:"text-sm font-medium text-gray-600"},"Pending",-1)),t("p",T,a(l.value.pending_referrals),1)])])]),t("div",q,[t("div",G,[e[7]||(e[7]=t("div",{class:"p-2 bg-purple-100 rounded-lg"},[t("svg",{class:"w-6 h-6 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),t("div",I,[e[6]||(e[6]=t("p",{class:"text-sm font-medium text-gray-600"},"Credits Awarded",-1)),t("p",J,"$"+a(l.value.total_credits_awarded),1)])])])]),t("div",K,[e[10]||(e[10]=t("div",{class:"px-6 py-4 border-b border-gray-200"},[t("h2",{class:"text-lg font-medium text-gray-900"},"Recent Referrals")],-1)),n.value?(d(),o("div",O,e[8]||(e[8]=[t("div",{class:"animate-pulse"},[t("div",{class:"h-4 bg-gray-200 rounded w-3/4 mb-4"}),t("div",{class:"h-4 bg-gray-200 rounded w-1/2 mb-4"}),t("div",{class:"h-4 bg-gray-200 rounded w-5/6"})],-1)]))):i.value.length===0?(d(),o("div",Q," No referrals found. ")):(d(),o("div",W,[t("table",X,[e[9]||(e[9]=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Referrer"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Referred User"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Status"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Credit Amount"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Date")])],-1)),t("tbody",Y,[(d(!0),o(f,null,B(i.value,r=>{var p,x,g,m;return d(),o("tr",{key:r.id},[t("td",Z,[t("div",tt,a(((p=r.referrer)==null?void 0:p.name)||"N/A"),1),t("div",et,a(((x=r.referrer)==null?void 0:x.email)||"N/A"),1)]),t("td",st,[t("div",rt,a(((g=r.referred)==null?void 0:g.name)||r.referred_email),1),t("div",at,a(((m=r.referred)==null?void 0:m.email)||r.referred_email),1)]),t("td",ot,[t("span",{class:z([w(r.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},a(r.status),3)]),t("td",dt," $"+a(r.credit_amount||"0.00"),1),t("td",lt,a(v(r.created_at)),1)])}),128))])])]))])])]),_:1})],64))}};export{mt as default};
