import{_ as v}from"./AppLayout.vue_vue_type_script_setup_true_lang-DwGvFXKf.js";import{r as c,o as _,d as r,e as s,f,u as p,m as k,g as x,i as e,F as n,p as u,t as o,n as b,y as w,P,x as B}from"./vendor-BhKTHoN5.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const R={class:"flex items-center justify-between"},j={class:"flex mt-2","aria-label":"Breadcrumb"},D={class:"inline-flex items-center space-x-1 md:space-x-3"},M={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},E={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},N={class:"py-12"},V={class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},C={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},A={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},F={class:"p-6 text-gray-900 dark:text-gray-100"},L={key:0,class:"text-center py-4"},T={key:1,class:"space-y-3"},z={class:"font-medium"},G={class:"text-sm text-gray-500 dark:text-gray-400"},S={class:"flex space-x-2"},$={key:0,class:"text-red-600 hover:text-red-900 text-sm"},q={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},H={class:"p-6 text-gray-900 dark:text-gray-100"},I={key:0,class:"text-center py-4"},J={key:1,class:"space-y-3"},K={class:"font-medium"},O={class:"text-sm text-gray-500 dark:text-gray-400"},ee={__name:"Permissions",setup(Q){const d=[{title:"Dashboard",href:"/dashboard"},{title:"Permissions",href:"/permissions"}],a=c(!1),g=c([]),y=c([]),h=async()=>{a.value=!0;try{const[i,t]=await Promise.all([window.axios.get("/roles-list"),window.axios.get("/permissions-list")]);g.value=i.data,y.value=t.data}catch(i){console.error("Error fetching data:",i)}finally{a.value=!1}};return _(()=>{h()}),(i,t)=>(s(),r(n,null,[f(p(k),{title:"Permissions Management"}),f(v,null,{header:x(()=>[e("div",R,[e("div",null,[t[1]||(t[1]=e("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Permissions Management ",-1)),e("nav",j,[e("ol",D,[(s(),r(n,null,u(d,(l,m)=>e("li",{key:m,class:"inline-flex items-center"},[m<d.length-1?(s(),w(p(P),{key:0,href:l.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:x(()=>[B(o(l.title),1)]),_:2},1032,["href"])):(s(),r("span",M,o(l.title),1)),m<d.length-1?(s(),r("svg",E,t[0]||(t[0]=[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):b("",!0)])),64))])])])])]),default:x(()=>[e("div",N,[e("div",V,[e("div",C,[e("div",A,[e("div",F,[t[4]||(t[4]=e("div",{class:"flex items-center justify-between mb-4"},[e("h3",{class:"text-lg font-medium"},"Roles"),e("button",{class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-sm"}," Add Role ")],-1)),a.value?(s(),r("div",L,t[2]||(t[2]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):(s(),r("div",T,[(s(!0),r(n,null,u(g.value,l=>(s(),r("div",{key:l.id,class:"flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg"},[e("div",null,[e("h4",z,o(l.display_name),1),e("p",G,o(l.permissions_count)+" permissions ",1)]),e("div",S,[t[3]||(t[3]=e("button",{class:"text-blue-600 hover:text-blue-900 text-sm"},"Edit",-1)),l.name!=="admin"?(s(),r("button",$,"Delete")):b("",!0)])]))),128))]))])]),e("div",q,[e("div",H,[t[7]||(t[7]=e("div",{class:"flex items-center justify-between mb-4"},[e("h3",{class:"text-lg font-medium"},"Permissions"),e("button",{class:"bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-sm"}," Add Permission ")],-1)),a.value?(s(),r("div",I,t[5]||(t[5]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):(s(),r("div",J,[(s(!0),r(n,null,u(y.value,l=>(s(),r("div",{key:l.id,class:"flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg"},[e("div",null,[e("h4",K,o(l.name),1),e("p",O," Group: "+o(l.group),1)]),t[6]||(t[6]=e("div",{class:"flex space-x-2"},[e("button",{class:"text-blue-600 hover:text-blue-900 text-sm"},"Edit"),e("button",{class:"text-red-600 hover:text-red-900 text-sm"},"Delete")],-1))]))),128))]))])])]),t[8]||(t[8]=e("div",{class:"mt-6 bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},[e("div",{class:"p-6 text-gray-900 dark:text-gray-100"},[e("h3",{class:"text-lg font-medium mb-4"},"Role-Permission Matrix"),e("div",{class:"text-center py-8 text-gray-500 dark:text-gray-400"},[e("i",{class:"fas fa-cogs text-4xl mb-4"}),e("p",null,"Role-Permission matrix will be implemented here"),e("p",{class:"text-sm"},"This will allow you to assign permissions to roles")])])],-1))])])]),_:1})],64))}};export{ee as default};
