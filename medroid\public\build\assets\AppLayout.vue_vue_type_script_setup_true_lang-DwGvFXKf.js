import{r as V,c as l,d as o,e as r,N as F,A as M,t as _,W as pe,o as U,H as X,i as s,f as b,n as f,F as P,p as R,g,u as h,P as k,x as $,y as C,a as T,J as S,K as L,z as ge}from"./vendor-BhKTHoN5.js";import{_ as G}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{c as E,P as ye}from"./Primitive-DSQomZit.js";import{c as xe}from"./createLucideIcon-YxmScYOV.js";const be=["src","alt"],we={__name:"MedroidLogo",props:{size:{type:Number,default:32},color:{type:String,default:null},useDarkVersion:{type:Boolean,default:!1},showShadow:{type:Boolean,default:!0},showIcon:{type:Boolean,default:!0},iconClass:{type:String,default:"fas fa-heart"},fallbackText:{type:String,default:"M"},alt:{type:String,default:"Medroid Logo"}},setup(n){const i=n,a=V(!1),c=V(!1),u=l(()=>"/medroid_logo.png"),x=l(()=>({display:"inline-block",width:`${i.size}px`,height:`${i.size}px`})),A=l(()=>({width:"100%",height:"100%",objectFit:"contain",display:"block"})),y=l(()=>({width:"100%",height:"100%",borderRadius:"50%",background:`linear-gradient(135deg, ${i.color||"#17C3B2"} 0%, #8BE9C8 100%)`,display:"flex",alignItems:"center",justifyContent:"center",boxShadow:i.showShadow?`0 ${i.size*.1}px ${i.size*.15}px rgba(23, 195, 178, 0.3)`:"none"})),w=l(()=>({fontSize:`${i.size*.5}px`,color:"white"})),H=l(()=>({fontSize:`${i.size*.6}px`,fontWeight:"bold",color:"white",fontFamily:"Arial, sans-serif"})),j=()=>{a.value=!0},m=()=>{c.value=!0,a.value=!1};return(I,B)=>(r(),o("div",{class:"medroid-logo",style:F(x.value)},[a.value?(r(),o("div",{key:1,class:"logo-fallback",style:F(y.value)},[n.showIcon?(r(),o("i",{key:0,class:M(n.iconClass),style:F(w.value)},null,6)):(r(),o("span",{key:1,class:"logo-text",style:F(H.value)},_(n.fallbackText),5))],4)):(r(),o("img",{key:0,src:u.value,alt:n.alt,style:F(A.value),onError:j,onLoad:m},null,44,be))],4))}},_e=G(we,[["__scopeId","data-v-015d9d74"]]);function Me(){return{logout:()=>{try{localStorage.removeItem("user"),localStorage.removeItem("auth_token"),localStorage.removeItem("chat_history"),sessionStorage.clear()}catch(i){console.log("Error clearing storage:",i)}pe.post("/logout",{},{onFinish:()=>{window.location.href="/"}})}}}const ke={class:"h-full bg-white border-r border-gray-200 text-gray-800 flex flex-col"},Ce={class:"flex items-center justify-between p-3 border-b border-gray-200"},ze={class:"flex items-center"},$e={key:0,class:"ml-2 text-lg font-semibold text-medroid-navy"},Ve={class:"flex items-center space-x-2"},Se={class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Le={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"},Ae={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"},Be={class:"flex-1 overflow-y-auto py-3"},He={class:"space-y-1 px-2 flex flex-col h-full"},je={class:"w-4 h-4 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ie=["d"],Fe={key:0,class:"ml-2"},Pe={key:0,class:"pt-6 flex flex-col flex-1 min-h-0"},Ee={class:"px-3 mb-2"},Re={class:"flex items-center justify-between"},Ne={class:"flex-1 min-h-0 overflow-y-auto"},Te={key:0,class:"px-3 py-2"},We={key:1,class:"space-y-1 px-3"},De={class:"flex-1 min-w-0"},qe={class:"text-gray-900 font-medium truncate group-hover:text-medroid-orange"},Oe={class:"text-gray-500 text-xs mt-0.5"},Ue={key:2,class:"px-3 py-2"},Xe={key:0,class:"px-3 py-2 border-t border-gray-200 mt-auto"},Ge={class:"text-xs font-semibold text-green-600 hover:text-medroid-orange"},Je={key:1,class:"pt-6"},Ke={key:0,class:"px-3 mb-2"},Qe={class:"w-4 h-4 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ye=["d"],Ze={key:0,class:"ml-2"},et={class:"border-t border-gray-200 p-3"},tt={class:"flex items-center"},st={key:0,class:"ml-2 flex-1"},rt={class:"flex items-center gap-2"},at={class:"text-sm font-medium text-gray-900"},ot=["title"],nt={class:"text-xs text-gray-500 capitalize"},it={key:0,class:"mt-3 space-y-1"},lt={key:1,class:"mt-3"},ut={__name:"AppSidebar",props:{user:{type:Object,required:!1,default:()=>null},isMobile:{type:Boolean,default:!1}},emits:["close"],setup(n,{emit:i}){const a=n,c=i,u=V(!1),x=V([]),A=V(!1),y=V({balance:0,total_earned:0,total_used:0,referral_earnings:0,admin_credits:0}),w=l(()=>{var t;return((t=a.user)==null?void 0:t.role)||"patient"}),H=l(()=>{var t;return((t=a.user)==null?void 0:t.is_founder_member)||!1}),j=l(()=>{var t;return((t=a.user)==null?void 0:t.founder_club_info)||null}),m=l(()=>{var t,e;return((e=(t=a.user)==null?void 0:t.roles)==null?void 0:e.some(v=>v.name==="admin"))||!1}),I=l(()=>{var t,e;return((e=(t=a.user)==null?void 0:t.roles)==null?void 0:e.some(v=>v.name==="provider"))||!1}),B=l(()=>{var t,e;return((e=(t=a.user)==null?void 0:t.roles)==null?void 0:e.some(v=>v.name==="patient"))||!1}),J=l(()=>{const t=[];return(m.value||I.value)&&t.push({title:"Dashboard",href:"/dashboard",icon:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z",permission:!0}),B.value&&t.push({title:"Chat",href:"/chat",icon:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z",permission:!0},{title:"Discover",href:"/discover",icon:"M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2zm4.24 5.76-2.12 6.36-6.36 2.12 2.12-6.36 6.36-2.12z",permission:!0},{title:"Shop",href:"/shop",icon:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z",permission:!0},{title:"My Orders",href:"/shop/orders",icon:"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01",permission:!0},{title:"Appointments",href:"/patient/appointments",icon:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z",permission:!0}),I.value&&t.push({title:"Schedule",href:"/provider/schedule",icon:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z",permission:!0},{title:"Availability",href:"/provider/availability",icon:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z",permission:!0},{title:"Appointments",href:"/provider/appointments",icon:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2v12a2 2 0 002 2z",permission:!0},{title:"My Patients",href:"/provider/patients",icon:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z",permission:!0},{title:"Services",href:"/provider/services",icon:"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z",permission:!0},{title:"Products",href:"/provider/products",icon:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M9 1v6m6-6v6",permission:!0},{title:"Earnings",href:"/provider/earnings",icon:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z",permission:!0}),t}),W=l(()=>m.value?[{title:"Users",href:"/users",icon:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z",permission:!0},{title:"Providers",href:"/providers",icon:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z",permission:!0},{title:"Patients",href:"/patients",icon:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",permission:!0},{title:"Clinics",href:"/clinics",icon:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4",permission:!0},{title:"Appointments",href:"/manage/appointments",icon:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z",permission:!0},{title:"Payments",href:"/payments",icon:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z",permission:!0},{title:"Chats",href:"/chats",icon:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z",permission:!0},{title:"Permissions",href:"/permissions",icon:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z",permission:!0},{title:"Services",href:"/services",icon:"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z",permission:!0},{title:"Products",href:"/admin/products",icon:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M9 1v6m6-6v6",permission:!0},{title:"Categories",href:"/admin/categories",icon:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",permission:!0},{title:"Orders",href:"/admin/orders",icon:"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01",permission:!0},{title:"Email Templates",href:"/email-templates",icon:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z",permission:!0},{title:"Notifications",href:"/notifications",icon:"M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9",permission:!0},{title:"Referrals",href:"/referrals",icon:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z",permission:!0},{title:"Credits",href:"/credits",icon:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z",permission:!0},{title:"Clubs",href:"/clubs",icon:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z",permission:!0},{title:"Waitlist",href:"/waitlist",icon:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z",permission:!0}]:[]),K=l(()=>{var t,e;return m.value||((e=(t=a.user)==null?void 0:t.user_permissions)==null?void 0:e.includes("view users"))}),Q=l(()=>{var t,e;return m.value||((e=(t=a.user)==null?void 0:t.user_permissions)==null?void 0:e.includes("view providers"))}),Y=l(()=>{var t,e;return m.value||((e=(t=a.user)==null?void 0:t.user_permissions)==null?void 0:e.includes("view patients"))});l(()=>{var t,e;return m.value||((e=(t=a.user)==null?void 0:t.user_permissions)==null?void 0:e.includes("view analytics"))});const Z=l(()=>{var t,e;return m.value||((e=(t=a.user)==null?void 0:t.user_permissions)==null?void 0:e.includes("view appointments"))}),ee=l(()=>{var t,e;return m.value||((e=(t=a.user)==null?void 0:t.user_permissions)==null?void 0:e.includes("view payments"))}),te=l(()=>{var t,e;return m.value||((e=(t=a.user)==null?void 0:t.user_permissions)==null?void 0:e.includes("view chats"))}),se=l(()=>{var t,e;return m.value||((e=(t=a.user)==null?void 0:t.user_permissions)==null?void 0:e.includes("manage notifications"))}),re=l(()=>{var t,e;return m.value||((e=(t=a.user)==null?void 0:t.user_permissions)==null?void 0:e.includes("manage email templates"))}),ae=l(()=>{var t,e;return m.value||((e=(t=a.user)==null?void 0:t.user_permissions)==null?void 0:e.includes("manage permissions"))}),oe=l(()=>{var t,e;return m.value||((e=(t=a.user)==null?void 0:t.user_permissions)==null?void 0:e.includes("manage services"))}),ne=l(()=>{var t,e;return m.value||((e=(t=a.user)==null?void 0:t.user_permissions)==null?void 0:e.includes("view referrals"))}),ie=l(()=>{var t,e;return m.value||((e=(t=a.user)==null?void 0:t.user_permissions)==null?void 0:e.includes("view credits"))}),le=l(()=>{var t,e;return m.value||((e=(t=a.user)==null?void 0:t.user_permissions)==null?void 0:e.includes("view clubs"))}),ue=l(()=>{var t,e;return m.value||((e=(t=a.user)==null?void 0:t.user_permissions)==null?void 0:e.includes("view clinics"))});l(()=>K.value||Q.value||Y.value||ue.value||Z.value||ee.value||te.value||oe.value),l(()=>ae.value||re.value||se.value||ne.value||ie.value||le.value);const de=()=>{u.value=!u.value,localStorage.setItem("app-sidebar-collapsed",u.value)},D=()=>{window.innerWidth<768&&!u.value&&(u.value=!0,localStorage.setItem("app-sidebar-collapsed","true"))},ce=async()=>{if(B.value)try{const t=await T.get("/credits-balance");y.value=t.data}catch(t){console.error("Error loading credit balance:",t),y.value={balance:0,total_earned:0,total_used:0,referral_earnings:0,admin_credits:0}}},me=async()=>{var t;if(B.value){A.value=!0;try{try{await T.post("/web-api/chat/update-titles",{},{headers:{Accept:"application/json","Content-Type":"application/json","X-CSRF-TOKEN":((t=document.querySelector('meta[name="csrf-token"]'))==null?void 0:t.getAttribute("content"))||"","X-Requested-With":"XMLHttpRequest"},withCredentials:!0})}catch(z){console.log("Title update failed (non-critical):",z)}const v=(await T.get("/web-api/chat/history")).data;let p=[];Array.isArray(v)?p=v:v.conversations&&Array.isArray(v.conversations)?p=v.conversations:v.data&&Array.isArray(v.data)&&(p=v.data),Array.isArray(p)||(console.warn("conversations is not an array in AppSidebar:",p),p=[]);const d=p.filter(z=>!z.messages||!Array.isArray(z.messages)?!1:z.messages.some(N=>N&&N.content&&N.content.trim().length>0));x.value=d.slice(0,8)}catch(e){console.error("Error fetching chat history:",e),x.value=[]}finally{A.value=!1}}},he=t=>{if(t.title&&t.title.trim())return t.title.length>25?t.title.substring(0,25)+"...":t.title;if(t.messages&&t.messages.length>0){const e=t.messages[0].content;return e.length>25?e.substring(0,25)+"...":e}return"New Chat"},ve=t=>{const e=new Date(t),p=Math.floor((new Date-e)/(1e3*60*60));return p<1?"Just now":p<24?`${p}h ago`:p<168?`${Math.floor(p/24)}d ago`:e.toLocaleDateString()},{logout:fe}=Me(),q=()=>{y.value={balance:0,total_earned:0,total_used:0,referral_earnings:0,admin_credits:0},x.value=[],fe()};return U(()=>{const t=localStorage.getItem("app-sidebar-collapsed");t!==null&&(u.value=t==="true"),window.innerWidth<768&&(u.value=!0),window.addEventListener("resize",D),B.value&&(me(),ce())}),X(()=>{window.removeEventListener("resize",D)}),(t,e)=>{var v,p;return r(),o("div",{class:M(["h-screen flex-shrink-0",[n.isMobile?"w-64":u.value?"w-16":"w-64","transition-all duration-300"]])},[s("div",ke,[s("div",Ce,[s("div",ze,[b(_e,{size:28}),!u.value||n.isMobile?(r(),o("span",$e,"Medroid")):f("",!0)]),s("div",Ve,[n.isMobile?(r(),o("button",{key:0,onClick:e[0]||(e[0]=d=>c("close")),class:"text-gray-400 hover:text-gray-600 focus:outline-none p-1"},e[4]||(e[4]=[s("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))):f("",!0),n.isMobile?f("",!0):(r(),o("button",{key:1,onClick:de,class:"text-gray-400 hover:text-gray-600 focus:outline-none"},[(r(),o("svg",Se,[u.value?(r(),o("path",Le)):(r(),o("path",Ae))]))]))])]),s("div",Be,[s("nav",He,[(r(!0),o(P,null,R(J.value,d=>(r(),o("div",{key:d.href},[b(h(k),{href:d.href,onClick:e[1]||(e[1]=z=>n.isMobile&&c("close")),class:M(["flex items-center px-2 py-1.5 text-sm font-medium rounded-lg transition-colors duration-200",[t.$page.url===d.href||t.$page.url.startsWith(d.href+"/")&&d.href!=="/"?"bg-medroid-orange text-white":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"]])},{default:g(()=>[(r(),o("svg",je,[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:d.icon},null,8,Ie)])),!u.value||n.isMobile?(r(),o("span",Fe,_(d.title),1)):f("",!0)]),_:2},1032,["href","class"])]))),128)),B.value&&(!u.value||n.isMobile)?(r(),o("div",Pe,[s("div",Ee,[s("div",Re,[e[6]||(e[6]=s("h3",{class:"text-xs font-semibold text-gray-400 uppercase tracking-wider"},"Recent Chats",-1)),b(h(k),{href:"/chat",class:"text-xs text-medroid-orange hover:text-medroid-orange-dark",title:"Start new chat"},{default:g(()=>e[5]||(e[5]=[s("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1)])),_:1})])]),s("div",Ne,[A.value?(r(),o("div",Te,e[7]||(e[7]=[s("div",{class:"flex items-center text-xs text-gray-500"},[s("div",{class:"animate-spin rounded-full h-3 w-3 border-b-2 border-gray-400 mr-2"}),$(" Loading chats... ")],-1)]))):x.value.length>0?(r(),o("div",We,[(r(!0),o(P,null,R(x.value,d=>(r(),C(h(k),{key:d._id||d.id,href:`/chat?conversation=${d._id||d.id}`,onClick:e[2]||(e[2]=z=>n.isMobile&&c("close")),class:"flex items-start py-2 text-xs rounded-lg transition-colors duration-200 hover:bg-gray-50 group block"},{default:g(()=>[e[8]||(e[8]=s("div",{class:"flex-shrink-0 mr-2 mt-0.5"},[s("div",{class:"w-2 h-2 bg-medroid-orange rounded-full"})],-1)),s("div",De,[s("p",qe,_(he(d)),1),s("p",Oe,_(ve(d.updated_at||d.createdAt)),1)])]),_:2},1032,["href"]))),128))])):(r(),o("div",Ue,[e[10]||(e[10]=s("p",{class:"text-xs text-gray-500"},"No recent chats",-1)),b(h(k),{href:"/chat",class:"text-xs text-medroid-orange hover:text-medroid-orange-dark mt-1 inline-block"},{default:g(()=>e[9]||(e[9]=[$(" Start your first chat ")])),_:1})]))]),x.value.length>0?(r(),o("div",Xe,[b(h(k),{href:"/chat-history",class:"text-xs text-gray-600 hover:text-medroid-orange flex items-center justify-start py-1 transition-colors"},{default:g(()=>e[11]||(e[11]=[s("svg",{class:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1),$(" View all chats ")])),_:1})])):f("",!0),s("div",{class:M(["px-3 py-3 border-t border-gray-200",{"mt-auto":x.value.length===0}])},[b(h(k),{href:"/credit-history",class:"inline-flex items-center justify-between w-auto px-3 py-2 border border-gray-300 rounded-md bg-white hover:bg-gray-50 transition-colors shadow-sm",title:`Total Balance: $${parseFloat(y.value.balance||0).toFixed(2)} | Referrals: $${parseFloat(y.value.referral_earnings||0).toFixed(2)} | Admin: $${parseFloat(y.value.admin_credits||0).toFixed(2)} | Used: $${parseFloat(y.value.total_used||0).toFixed(2)}`},{default:g(()=>[e[12]||(e[12]=s("div",{class:"flex items-center"},[s("svg",{class:"w-3 h-3 mr-1 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})]),s("span",{class:"text-xs text-gray-600 hover:text-medroid-orange mr-2"},"Credits:")],-1)),s("span",Ge,"$"+_(parseFloat(y.value.balance||0).toFixed(2)),1)]),_:1},8,["title"])],2)])):f("",!0),W.value.length>0?(r(),o("div",Je,[!u.value||n.isMobile?(r(),o("div",Ke,e[13]||(e[13]=[s("h3",{class:"text-xs font-semibold text-gray-400 uppercase tracking-wider"},"Management",-1)]))):f("",!0),(r(!0),o(P,null,R(W.value,d=>(r(),o("div",{key:d.href},[b(h(k),{href:d.href,onClick:e[3]||(e[3]=z=>n.isMobile&&c("close")),class:M(["flex items-center px-2 py-1.5 text-sm font-medium rounded-lg transition-colors duration-200",[t.$page.url===d.href||t.$page.url.startsWith(d.href+"/")&&d.href!=="/"?"bg-medroid-orange text-white":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"]])},{default:g(()=>[(r(),o("svg",Qe,[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:d.icon},null,8,Ye)])),!u.value||n.isMobile?(r(),o("span",Ze,_(d.title),1)):f("",!0)]),_:2},1032,["href","class"])]))),128))])):f("",!0)])]),s("div",et,[s("div",tt,[e[15]||(e[15]=s("div",{class:"w-7 h-7 bg-gray-300 rounded-full flex items-center justify-center"},[s("svg",{class:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])],-1)),!u.value||n.isMobile?(r(),o("div",st,[s("div",rt,[s("p",at,_((v=n.user)==null?void 0:v.name),1),H.value?(r(),o("div",{key:0,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-purple-600 to-indigo-600 text-white shadow-sm",title:((p=j.value)==null?void 0:p.club_name)||"Medroid Founders Club"},e[14]||(e[14]=[s("svg",{class:"w-3 h-3 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[s("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})],-1),$(" Founders' Club ")]),8,ot)):f("",!0)]),s("p",nt,_(w.value),1)])):f("",!0)]),!u.value||n.isMobile?(r(),o("div",it,[b(h(k),{href:"/settings/profile",class:"flex items-center px-2 py-1 text-xs text-gray-600 hover:text-gray-900 rounded"},{default:g(()=>e[16]||(e[16]=[s("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})],-1),$(" Settings ")])),_:1}),s("button",{onClick:q,class:"flex items-center px-2 py-1 text-xs text-red-600 hover:text-red-800 rounded w-full text-left"},e[17]||(e[17]=[s("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})],-1),$(" Logout ")]))])):n.isMobile?f("",!0):(r(),o("div",lt,[s("button",{onClick:q,class:"flex items-center justify-center p-2 text-red-600 hover:text-red-800 rounded",title:"Logout"},e[18]||(e[18]=[s("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})],-1)]))]))])])],2)}}},O=G(ut,[["__scopeId","data-v-58145060"]]),dt=S({__name:"Breadcrumb",props:{class:{}},setup(n){const i=n;return(a,c)=>(r(),o("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",class:M(i.class)},[L(a.$slots,"default")],2))}});/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ct=xe("ChevronRightIcon",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),mt=S({__name:"BreadcrumbItem",props:{class:{}},setup(n){const i=n;return(a,c)=>(r(),o("li",{"data-slot":"breadcrumb-item",class:M(h(E)("inline-flex items-center gap-1.5",i.class))},[L(a.$slots,"default")],2))}}),ht=S({__name:"BreadcrumbLink",props:{asChild:{type:Boolean},as:{default:"a"},class:{}},setup(n){const i=n;return(a,c)=>(r(),C(h(ye),{"data-slot":"breadcrumb-link",as:a.as,"as-child":a.asChild,class:M(h(E)("hover:text-foreground transition-colors",i.class))},{default:g(()=>[L(a.$slots,"default")]),_:3},8,["as","as-child","class"]))}}),vt=S({__name:"BreadcrumbList",props:{class:{}},setup(n){const i=n;return(a,c)=>(r(),o("ol",{"data-slot":"breadcrumb-list",class:M(h(E)("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",i.class))},[L(a.$slots,"default")],2))}}),ft=S({__name:"BreadcrumbPage",props:{class:{}},setup(n){const i=n;return(a,c)=>(r(),o("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",class:M(h(E)("text-foreground font-normal",i.class))},[L(a.$slots,"default")],2))}}),pt=S({__name:"BreadcrumbSeparator",props:{class:{}},setup(n){const i=n;return(a,c)=>(r(),o("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",class:M(h(E)("[&>svg]:size-3.5",i.class))},[L(a.$slots,"default",{},()=>[b(h(ct))])],2))}}),gt=S({__name:"Breadcrumbs",props:{breadcrumbs:{}},setup(n){return(i,a)=>(r(),C(h(dt),null,{default:g(()=>[b(h(vt),null,{default:g(()=>[(r(!0),o(P,null,R(i.breadcrumbs,(c,u)=>(r(),o(P,{key:u},[b(h(mt),null,{default:g(()=>[u===i.breadcrumbs.length-1?(r(),C(h(ft),{key:0},{default:g(()=>[$(_(c.title),1)]),_:2},1024)):(r(),C(h(ht),{key:1,"as-child":""},{default:g(()=>[b(h(k),{href:c.href??"#"},{default:g(()=>[$(_(c.title),1)]),_:2},1032,["href"])]),_:2},1024))]),_:2},1024),u!==i.breadcrumbs.length-1?(r(),C(h(pt),{key:0})):f("",!0)],64))),128))]),_:1})]),_:1}))}}),yt={class:"flex h-screen bg-gray-100"},xt={key:3,class:"w-64 bg-white border-r border-gray-200 flex items-center justify-center"},bt={class:"flex-1 flex flex-col overflow-hidden"},wt={class:"bg-white shadow-sm border-b border-gray-200"},_t={class:"px-4 py-2"},Mt={class:"flex items-center justify-between"},kt={class:"flex items-center space-x-3"},Ct={class:"flex items-center space-x-3"},zt={class:"text-sm text-gray-700 hidden sm:inline"},$t={class:"text-sm text-gray-700 sm:hidden"},Vt={class:"flex-1 overflow-x-hidden overflow-y-auto bg-gray-100"},St={__name:"AppSidebarLayout",props:{breadcrumbs:{type:Array,default:()=>[]}},setup(n){const i=ge(),a=l(()=>{var y;return((y=i.props.auth)==null?void 0:y.user)||null}),c=V(!1),u=V(!1),x=()=>{typeof window<"u"&&(c.value=window.innerWidth<768,c.value||(u.value=!1))},A=()=>{u.value=!u.value};return U(()=>{x(),typeof window<"u"&&window.addEventListener("resize",x)}),X(()=>{typeof window<"u"&&window.removeEventListener("resize",x)}),(y,w)=>{var H,j,m;return r(),o("div",yt,[c.value&&u.value?(r(),o("div",{key:0,class:"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden",onClick:w[0]||(w[0]=I=>u.value=!1)})):f("",!0),a.value&&!c.value?(r(),C(O,{key:1,user:a.value},null,8,["user"])):f("",!0),a.value&&c.value?(r(),o("div",{key:2,class:M(["fixed inset-y-0 left-0 z-50 transform transition-transform duration-300 ease-in-out lg:hidden",u.value?"translate-x-0":"-translate-x-full"])},[b(O,{user:a.value,"is-mobile":!0,onClose:w[1]||(w[1]=I=>u.value=!1)},null,8,["user"])],2)):!a.value&&!c.value?(r(),o("div",xt,w[2]||(w[2]=[s("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):f("",!0),s("div",bt,[s("header",wt,[s("div",_t,[s("div",Mt,[s("div",kt,[c.value?(r(),o("button",{key:0,onClick:A,class:"p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-medroid-orange"},w[3]||(w[3]=[s("svg",{class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)]))):f("",!0),s("div",null,[n.breadcrumbs.length>0?(r(),C(gt,{key:0,breadcrumbs:n.breadcrumbs},null,8,["breadcrumbs"])):f("",!0)])]),s("div",Ct,[s("span",zt,"Welcome, "+_(((H=a.value)==null?void 0:H.name)||"Guest"),1),s("span",$t,_(((m=(j=a.value)==null?void 0:j.name)==null?void 0:m.split(" ")[0])||"Guest"),1)])])])]),s("main",Vt,[L(y.$slots,"default")])])])}}},jt=S({__name:"AppLayout",props:{breadcrumbs:{default:()=>[]}},setup(n){return(i,a)=>(r(),C(St,{breadcrumbs:i.breadcrumbs},{default:g(()=>[L(i.$slots,"default")]),_:3},8,["breadcrumbs"]))}});export{_e as M,jt as _};
