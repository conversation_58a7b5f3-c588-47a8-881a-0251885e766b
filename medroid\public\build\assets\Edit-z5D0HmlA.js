import{r as y,o as S,d as l,e as s,f,u as x,m as T,g as p,i as t,j as $,l as g,n as m,v as _,t as n,F as k,p as N,q as L,s as q,P as h,x as v,y as A,W as O}from"./vendor-BhKTHoN5.js";import{_ as W}from"./AppLayout.vue_vue_type_script_setup_true_lang-DwGvFXKf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const z={class:"flex items-center justify-between"},I={class:"flex mt-2","aria-label":"Breadcrumb"},R={class:"inline-flex items-center space-x-1 md:space-x-3"},G={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},H={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},J={class:"py-12"},K={class:"mx-auto max-w-4xl sm:px-6 lg:px-8"},Q={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},X={class:"p-6 text-gray-900 dark:text-gray-100"},Y={key:0,class:"mt-1 text-sm text-red-600"},Z={key:0,class:"mt-1 text-sm text-red-600"},ee=["value"],te={key:0,class:"mt-1 text-sm text-red-600"},re={key:0,class:"mt-1 text-sm text-red-600"},ae={class:"flex items-center"},oe={class:"flex items-center justify-end space-x-3"},se=["disabled"],de={key:0,class:"fas fa-spinner fa-spin mr-2"},le={key:1,class:"fas fa-save mr-2"},ge={__name:"Edit",props:{category:Object,parentCategories:Array},setup(P){var C,E,V,U,B,D;const d=P,b=[{title:"Dashboard",href:"/dashboard"},{title:"Categories",href:"/admin/categories"},{title:"Edit Category",href:`/admin/categories/${(C=d.category)==null?void 0:C.id}/edit`}],c=y(!1),w=y(d.parentCategories||[]),a=y({name:((E=d.category)==null?void 0:E.name)||"",description:((V=d.category)==null?void 0:V.description)||"",parent_id:((U=d.category)==null?void 0:U.parent_id)||"",sort_order:((B=d.category)==null?void 0:B.sort_order)||0,is_active:((D=d.category)==null?void 0:D.is_active)??!0}),i=y({}),j=async()=>{var u;if((u=d.category)!=null&&u.id)try{const e=await window.axios.get(`/admin/categories/${d.category.id}/edit`);e.data.category&&(a.value={name:e.data.category.name,description:e.data.category.description||"",parent_id:e.data.category.parent_id||"",sort_order:e.data.category.sort_order||0,is_active:e.data.category.is_active??!0}),e.data.parent_categories&&(w.value=e.data.parent_categories)}catch(e){console.error("Error fetching category data:",e)}},F=async()=>{var u,e,r;c.value=!0,i.value={};try{const o=new FormData;o.append("name",a.value.name),o.append("description",a.value.description),a.value.parent_id&&o.append("parent_id",a.value.parent_id),o.append("sort_order",a.value.sort_order),o.append("is_active",a.value.is_active?"1":"0"),o.append("_method","PUT");const M=await window.axios.post(`/admin/save-category/${d.category.id}`,o,{headers:{"Content-Type":"multipart/form-data"}});M.data.success?(alert("Category updated successfully!"),O.visit("/admin/categories")):alert(M.data.message||"Error updating category")}catch(o){console.error("Error updating category:",o),((u=o.response)==null?void 0:u.status)===422?i.value=o.response.data.errors||{}:alert(((r=(e=o.response)==null?void 0:e.data)==null?void 0:r.message)||"Error updating category")}finally{c.value=!1}};return S(()=>{(!d.category||!d.parentCategories)&&j()}),(u,e)=>(s(),l(k,null,[f(x(T),{title:"Edit Category"}),f(W,null,{header:p(()=>[t("div",z,[t("div",null,[e[6]||(e[6]=t("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Edit Category ",-1)),t("nav",I,[t("ol",R,[(s(),l(k,null,N(b,(r,o)=>t("li",{key:o,class:"inline-flex items-center"},[o<b.length-1?(s(),A(x(h),{key:0,href:r.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:p(()=>[v(n(r.title),1)]),_:2},1032,["href"])):(s(),l("span",G,n(r.title),1)),o<b.length-1?(s(),l("svg",H,e[5]||(e[5]=[t("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):m("",!0)])),64))])])]),t("div",null,[f(x(h),{href:"/admin/categories",class:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded mr-2"},{default:p(()=>e[7]||(e[7]=[t("i",{class:"fas fa-arrow-left mr-2"},null,-1),v(" Back to Categories ")])),_:1})])])]),default:p(()=>[t("div",J,[t("div",K,[t("div",Q,[t("div",X,[t("form",{onSubmit:$(F,["prevent"]),class:"space-y-6"},[t("div",null,[e[8]||(e[8]=t("label",{for:"name",class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Category Name * ",-1)),g(t("input",{id:"name","onUpdate:modelValue":e[0]||(e[0]=r=>a.value.name=r),type:"text",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"Enter category name"},null,512),[[_,a.value.name]]),i.value.name?(s(),l("p",Y,n(i.value.name[0]),1)):m("",!0)]),t("div",null,[e[9]||(e[9]=t("label",{for:"description",class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Description ",-1)),g(t("textarea",{id:"description","onUpdate:modelValue":e[1]||(e[1]=r=>a.value.description=r),rows:"3",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"Enter category description"},null,512),[[_,a.value.description]]),i.value.description?(s(),l("p",Z,n(i.value.description[0]),1)):m("",!0)]),t("div",null,[e[11]||(e[11]=t("label",{for:"parent_id",class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Parent Category ",-1)),g(t("select",{id:"parent_id","onUpdate:modelValue":e[2]||(e[2]=r=>a.value.parent_id=r),class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},[e[10]||(e[10]=t("option",{value:""},"Root Category (No Parent)",-1)),(s(!0),l(k,null,N(w.value,r=>(s(),l("option",{key:r.id,value:r.id},n(r.name),9,ee))),128))],512),[[L,a.value.parent_id]]),i.value.parent_id?(s(),l("p",te,n(i.value.parent_id[0]),1)):m("",!0)]),t("div",null,[e[12]||(e[12]=t("label",{for:"sort_order",class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Sort Order ",-1)),g(t("input",{id:"sort_order","onUpdate:modelValue":e[3]||(e[3]=r=>a.value.sort_order=r),type:"number",min:"0",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"0"},null,512),[[_,a.value.sort_order,void 0,{number:!0}]]),e[13]||(e[13]=t("p",{class:"mt-1 text-sm text-gray-500"},"Lower numbers appear first",-1)),i.value.sort_order?(s(),l("p",re,n(i.value.sort_order[0]),1)):m("",!0)]),t("div",null,[t("label",ae,[g(t("input",{"onUpdate:modelValue":e[4]||(e[4]=r=>a.value.is_active=r),type:"checkbox",class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[q,a.value.is_active]]),e[14]||(e[14]=t("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"},"Active",-1))]),e[15]||(e[15]=t("p",{class:"mt-1 text-sm text-gray-500"},"Inactive categories won't be visible to users",-1))]),t("div",oe,[f(x(h),{href:"/admin/categories",class:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"},{default:p(()=>e[16]||(e[16]=[v(" Cancel ")])),_:1}),t("button",{type:"submit",disabled:c.value,class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"},[c.value?(s(),l("i",de)):(s(),l("i",le)),v(" "+n(c.value?"Updating...":"Update Category"),1)],8,se)])],32)])])])])]),_:1})],64))}};export{ge as default};
