import{r as m,y as E,e as n,g as V,i as s,d as r,n as i,l as F,v as A,t as e,A as c,F as w,p as k,a as b}from"./vendor-BhKTHoN5.js";import{_ as M}from"./AppLayout.vue_vue_type_script_setup_true_lang-DwGvFXKf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const N={class:"p-6"},R={class:"max-w-7xl mx-auto"},O={class:"bg-white rounded-lg shadow-sm border border-gray-200 mb-6"},P={class:"p-6"},j={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},B=["disabled"],I={key:0,class:"mt-2 text-xs text-gray-600"},W={class:"bg-white rounded-lg shadow-sm border border-gray-200 mb-6"},z={class:"p-6"},D=["disabled"],U={key:0,class:"space-y-4"},H={class:"font-medium capitalize"},L={class:"text-sm mt-1"},Y={key:0,class:"text-xs mt-2"},$={key:1,class:"text-xs mt-2"},q={class:"mt-4 p-4 bg-gray-50 rounded-md"},G={class:"text-sm text-gray-600 mt-1"},J={key:0,class:"mt-2"},K={class:"text-sm text-green-600"},Q={key:1,class:"mt-2"},X={class:"text-sm text-red-600"},Z={class:"bg-white rounded-lg shadow-sm border border-gray-200 mb-6"},ss={class:"p-6"},es=["disabled"],ts={key:0,class:"space-y-4"},as={class:"font-medium capitalize"},os={class:"text-sm mt-1"},ns={key:0,class:"text-xs mt-2"},rs={key:1,class:"text-xs mt-2"},ls={class:"mt-4 p-4 bg-gray-50 rounded-md"},is={class:"text-sm text-gray-600 mt-1"},ds={key:0,class:"mt-2"},us={class:"text-sm text-green-600"},cs={key:1,class:"mt-2"},ms={class:"text-sm text-red-600"},gs={class:"bg-white rounded-lg shadow-sm border border-gray-200"},vs={class:"p-6"},_s={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},ys={class:"text-sm mt-1"},ps={class:"text-sm mt-1"},fs={class:"text-sm mt-1"},Ss={__name:"SystemVerification",setup(hs){const v=m(""),_=m(!1),l=m(null),y=m(!1),d=m(null),p=m(!1),u=m(null),T=async()=>{var o,t;if(!v.value){alert("Please enter an email address");return}_.value=!0,l.value=null;try{const a=await b.post("/test-email-config",{email:v.value});l.value=a.data}catch(a){l.value={success:!1,message:((t=(o=a.response)==null?void 0:o.data)==null?void 0:t.message)||"Email test failed"}}finally{_.value=!1}},S=async()=>{y.value=!0,d.value=null;try{const o=await b.post("/verify-transaction-system");d.value=o.data}catch(o){console.error("Transaction verification failed:",o)}finally{y.value=!1}},C=async()=>{p.value=!0,u.value=null;try{const o=await b.post("/verify-anonymous-chat-mapping");u.value=o.data}catch(o){console.error("Chat verification failed:",o)}finally{p.value=!1}},x=o=>{switch(o){case"working":return"bg-green-50 border-green-200";case"no_data":return"bg-yellow-50 border-yellow-200";default:return"bg-red-50 border-red-200"}},f=o=>{switch(o){case"email":return l.value?l.value.success?"Working":"Issues Found":"Not Tested";case"transactions":return d.value?d.value.summary.overall_status:"Not Tested";case"chat":return u.value?u.value.summary.overall_status:"Not Tested";default:return"Unknown"}},h=o=>{switch(f(o)){case"Working":case"all_working":return"bg-green-50 border border-green-200";case"Issues Found":case"issues_found":return"bg-red-50 border border-red-200";default:return"bg-gray-50 border border-gray-200"}};return(o,t)=>(n(),E(M,null,{default:V(()=>[s("div",N,[s("div",R,[t[11]||(t[11]=s("h1",{class:"text-3xl font-bold text-gray-900 mb-8"},"System Verification Dashboard",-1)),s("div",O,[s("div",P,[t[2]||(t[2]=s("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Email Configuration Test",-1)),s("div",j,[s("div",null,[t[1]||(t[1]=s("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Test Email Address",-1)),F(s("input",{"onUpdate:modelValue":t[0]||(t[0]=a=>v.value=a),type:"email",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Enter email to test"},null,512),[[A,v.value]]),s("button",{onClick:T,disabled:_.value,class:"mt-3 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"},e(_.value?"Testing...":"Test Email Configuration"),9,B)]),l.value?(n(),r("div",{key:0,class:c(["p-4 rounded-md",l.value.success?"bg-green-50 border border-green-200":"bg-red-50 border border-red-200"])},[s("h3",{class:c(["font-medium",l.value.success?"text-green-800":"text-red-800"])},e(l.value.success?"Email Test Successful":"Email Test Failed"),3),s("p",{class:c(["text-sm mt-1",l.value.success?"text-green-600":"text-red-600"])},e(l.value.message),3),l.value.config?(n(),r("div",I,[s("p",null,"Mailer: "+e(l.value.config.mailer),1),s("p",null,"Host: "+e(l.value.config.host),1),s("p",null,"Port: "+e(l.value.config.port),1)])):i("",!0)],2)):i("",!0)])])]),s("div",W,[s("div",z,[t[4]||(t[4]=s("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Transaction System Verification",-1)),s("button",{onClick:S,disabled:y.value,class:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 mb-4"},e(y.value?"Verifying...":"Verify Transaction System"),9,D),d.value?(n(),r("div",U,[(n(!0),r(w,null,k(d.value.verification_results,(a,g)=>(n(),r("div",{key:g,class:c(["p-4 rounded-md border",x(a.status)])},[s("h3",H,e(g.replace("_"," ")),1),s("p",L,"Status: "+e(a.status),1),a.total_payments!==void 0?(n(),r("div",Y,[s("p",null,"Total Payments: "+e(a.total_payments),1),s("p",null,"Recent Payments: "+e(a.recent_payments),1)])):i("",!0),a.total_transactions!==void 0?(n(),r("div",$,[s("p",null,"Total Transactions: "+e(a.total_transactions),1),s("p",null,"Recent Transactions: "+e(a.recent_transactions),1)])):i("",!0)],2))),128)),s("div",q,[t[3]||(t[3]=s("h3",{class:"font-medium text-gray-900"},"Summary",-1)),s("p",G,"Overall Status: "+e(d.value.summary.overall_status),1),d.value.summary.working_features.length?(n(),r("div",J,[s("p",K,"Working Features: "+e(d.value.summary.working_features.join(", ")),1)])):i("",!0),d.value.summary.issues_found.length?(n(),r("div",Q,[s("p",X,"Issues Found: "+e(d.value.summary.issues_found.join(", ")),1)])):i("",!0)])])):i("",!0)])]),s("div",Z,[s("div",ss,[t[6]||(t[6]=s("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Anonymous Chat Mapping Verification",-1)),s("button",{onClick:C,disabled:p.value,class:"px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 mb-4"},e(p.value?"Verifying...":"Verify Anonymous Chat Mapping"),9,es),u.value?(n(),r("div",ts,[(n(!0),r(w,null,k(u.value.verification_results,(a,g)=>(n(),r("div",{key:g,class:c(["p-4 rounded-md border",x(a.status)])},[s("h3",as,e(g.replace("_"," ")),1),s("p",os,"Status: "+e(a.status),1),a.anonymous_chats_count!==void 0?(n(),r("div",ns,[s("p",null,"Anonymous Chats: "+e(a.anonymous_chats_count),1),s("p",null,"Total Chats: "+e(a.total_chats_count),1),s("p",null,"Anonymous Percentage: "+e(a.anonymous_chat_percentage)+"%",1)])):i("",!0),a.transferred_chats_count!==void 0?(n(),r("div",rs,[s("p",null,"Transferred Chats: "+e(a.transferred_chats_count),1),s("p",null,"Transfer Method Exists: "+e(a.transfer_method_exists?"Yes":"No"),1)])):i("",!0)],2))),128)),s("div",ls,[t[5]||(t[5]=s("h3",{class:"font-medium text-gray-900"},"Summary",-1)),s("p",is,"Overall Status: "+e(u.value.summary.overall_status),1),u.value.summary.working_features.length?(n(),r("div",ds,[s("p",us,"Working Features: "+e(u.value.summary.working_features.join(", ")),1)])):i("",!0),u.value.summary.issues_found.length?(n(),r("div",cs,[s("p",ms,"Issues Found: "+e(u.value.summary.issues_found.join(", ")),1)])):i("",!0)])])):i("",!0)])]),s("div",gs,[s("div",vs,[t[10]||(t[10]=s("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"System Status Overview",-1)),s("div",_s,[s("div",{class:c(["p-4 rounded-md",h("email")])},[t[7]||(t[7]=s("h3",{class:"font-medium"},"Email System",-1)),s("p",ys,e(f("email")),1)],2),s("div",{class:c(["p-4 rounded-md",h("transactions")])},[t[8]||(t[8]=s("h3",{class:"font-medium"},"Transaction System",-1)),s("p",ps,e(f("transactions")),1)],2),s("div",{class:c(["p-4 rounded-md",h("chat")])},[t[9]||(t[9]=s("h3",{class:"font-medium"},"Chat System",-1)),s("p",fs,e(f("chat")),1)],2)])])])])])]),_:1}))}};export{Ss as default};
