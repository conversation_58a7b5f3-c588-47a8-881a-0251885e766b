import{r as v,B as i,c as _,o as C,d as b,e as f,f as h,u as V,m as P,g as S,i as t,t as o,A as g,n as B,x as y,F as A,a as m}from"./vendor-BhKTHoN5.js";import{_ as T}from"./AppLayout.vue_vue_type_script_setup_true_lang-DwGvFXKf.js";import{_ as $}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const D={class:"min-h-screen bg-gray-50"},E={class:"bg-white shadow-sm border-b border-gray-200"},H={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},N={class:"py-6"},L={class:"flex items-center justify-between"},O={class:"flex items-center space-x-3"},z={class:"text-sm text-gray-500"},F={class:"bg-white border-b border-gray-200"},I={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Q={class:"flex space-x-8"},U={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},q={key:0,class:"space-y-6"},G={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},J={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},K={class:"flex items-center"},R={class:"ml-4"},W={class:"text-2xl font-semibold text-gray-900"},X={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},Y={class:"flex items-center"},Z={class:"ml-4"},tt={class:"text-2xl font-semibold text-gray-900"},st={class:"text-xs text-gray-500"},et={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},ot={class:"flex items-center"},at={class:"ml-4"},rt={class:"text-2xl font-semibold text-gray-900"},nt={class:"text-xs text-gray-500"},it={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},lt={class:"flex items-center"},dt={class:"ml-4"},ct={class:"text-2xl font-semibold text-gray-900"},pt={class:"text-xs text-gray-500"},gt={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},mt={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},xt={class:"space-y-4"},ut={class:"flex items-center justify-between"},vt={class:"text-sm font-medium text-gray-900"},_t={class:"flex items-center justify-between"},bt={class:"text-sm font-medium text-gray-900"},ft={class:"flex items-center justify-between"},ht={class:"text-sm font-medium text-gray-900"},yt={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},wt={class:"space-y-3"},kt={__name:"SocialMediaManager",setup(Mt){const w=[{name:"Dashboard",href:"/dashboard"},{name:"Social Media Manager",href:"/admin/social-media"}],x=v(!1),a=v("dashboard"),e=i({total_posts:0,pending_posts:0,approved_posts:0,rejected_posts:0,total_stories:0,active_stories:0,total_users:0,posts_today:0,stories_today:0}),l=i({data:[],current_page:1,last_page:1,total:0,loading:!1}),d=i({data:[],current_page:1,last_page:1,total:0,loading:!1}),u=i({posts:{status:"all",source:"all",search:"",date_from:"",date_to:"",sort_by:"created_at",sort_order:"desc"},stories:{status:"all",search:"",date_from:"",date_to:"",sort_by:"created_at",sort_order:"desc"}}),k=_(()=>e.total_posts===0?0:Math.round(e.pending_posts/e.total_posts*100)),M=_(()=>e.total_posts===0?0:Math.round(e.approved_posts/e.total_posts*100)),j=async()=>{x.value=!0;try{const r=await m.get("/admin/api/social-media");Object.assign(e,r.data.stats)}catch(r){console.error("Error loading dashboard:",r)}finally{x.value=!1}},c=async(r=1)=>{l.loading=!0;try{const s={page:r,per_page:20,...u.posts},n=await m.get("/admin/api/social-media/posts",{params:s});Object.assign(l,n.data)}catch(s){console.error("Error loading posts:",s)}finally{l.loading=!1}},p=async(r=1)=>{d.loading=!0;try{const s={page:r,per_page:20,...u.stories},n=await m.get("/admin/api/social-media/stories",{params:s});Object.assign(d,n.data)}catch(s){console.error("Error loading stories:",s)}finally{d.loading=!1}};return C(()=>{j(),c(),p()}),(r,s)=>(f(),b(A,null,[h(V(P),{title:"Social Media Manager - Admin"}),h(T,{breadcrumbs:w},{default:S(()=>[t("div",D,[t("div",E,[t("div",H,[t("div",N,[t("div",L,[s[5]||(s[5]=t("div",null,[t("h1",{class:"text-2xl font-bold text-gray-900"},"Social Media Manager"),t("p",{class:"text-gray-600"},"Manage posts, stories, and user-generated content")],-1)),t("div",O,[t("div",z," Last updated: "+o(new Date().toLocaleTimeString()),1)])])])])]),t("div",F,[t("div",I,[t("nav",Q,[t("button",{onClick:s[0]||(s[0]=n=>a.value="dashboard"),class:g(["py-4 px-1 border-b-2 font-medium text-sm transition-colors",a.value==="dashboard"?"border-teal-500 text-teal-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"])}," Dashboard ",2),t("button",{onClick:s[1]||(s[1]=n=>{a.value="posts",c()}),class:g(["py-4 px-1 border-b-2 font-medium text-sm transition-colors",a.value==="posts"?"border-teal-500 text-teal-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"])}," Posts Management ",2),t("button",{onClick:s[2]||(s[2]=n=>{a.value="stories",p()}),class:g(["py-4 px-1 border-b-2 font-medium text-sm transition-colors",a.value==="stories"?"border-teal-500 text-teal-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"])}," Stories Management ",2)])])]),t("div",U,[a.value==="dashboard"?(f(),b("div",q,[t("div",G,[t("div",J,[t("div",K,[s[7]||(s[7]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})])])],-1)),t("div",R,[s[6]||(s[6]=t("p",{class:"text-sm font-medium text-gray-600"},"Total Posts",-1)),t("p",W,o(e.total_posts),1)])])]),t("div",X,[t("div",Y,[s[9]||(s[9]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",Z,[s[8]||(s[8]=t("p",{class:"text-sm font-medium text-gray-600"},"Pending Posts",-1)),t("p",tt,o(e.pending_posts),1),t("p",st,o(k.value)+"% of total",1)])])]),t("div",et,[t("div",ot,[s[11]||(s[11]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})])])],-1)),t("div",at,[s[10]||(s[10]=t("p",{class:"text-sm font-medium text-gray-600"},"Approved Posts",-1)),t("p",rt,o(e.approved_posts),1),t("p",nt,o(M.value)+"% of total",1)])])]),t("div",it,[t("div",lt,[s[13]||(s[13]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V6a2 2 0 00-2-2"})])])],-1)),t("div",dt,[s[12]||(s[12]=t("p",{class:"text-sm font-medium text-gray-600"},"Active Stories",-1)),t("p",ct,o(e.active_stories),1),t("p",pt,o(e.total_stories)+" total",1)])])])]),t("div",gt,[t("div",mt,[s[17]||(s[17]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Today's Activity",-1)),t("div",xt,[t("div",ut,[s[14]||(s[14]=t("span",{class:"text-sm text-gray-600"},"Posts Created",-1)),t("span",vt,o(e.posts_today),1)]),t("div",_t,[s[15]||(s[15]=t("span",{class:"text-sm text-gray-600"},"Stories Created",-1)),t("span",bt,o(e.stories_today),1)]),t("div",ft,[s[16]||(s[16]=t("span",{class:"text-sm text-gray-600"},"Total Users",-1)),t("span",ht,o(e.total_users),1)])])]),t("div",yt,[s[20]||(s[20]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Quick Actions",-1)),t("div",wt,[t("button",{onClick:s[3]||(s[3]=n=>{a.value="posts",c()}),class:"w-full text-left px-4 py-3 bg-teal-50 text-teal-700 rounded-lg hover:bg-teal-100 transition-colors"},s[18]||(s[18]=[t("div",{class:"flex items-center"},[t("svg",{class:"w-5 h-5 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})]),y(" Manage Posts ")],-1)])),t("button",{onClick:s[4]||(s[4]=n=>{a.value="stories",p()}),class:"w-full text-left px-4 py-3 bg-purple-50 text-purple-700 rounded-lg hover:bg-purple-100 transition-colors"},s[19]||(s[19]=[t("div",{class:"flex items-center"},[t("svg",{class:"w-5 h-5 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V6a2 2 0 00-2-2"})]),y(" Manage Stories ")],-1)]))])])])])):B("",!0)])])]),_:1})],64))}},Bt=$(kt,[["__scopeId","data-v-becc1240"]]);export{Bt as default};
