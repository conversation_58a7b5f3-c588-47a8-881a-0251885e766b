<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ProductCategory;
use Illuminate\Support\Str;

class ProductCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if it's safe to proceed
        if (!$this->isSafeToUpdate()) {
            $this->command->error('Cannot update categories: Products are assigned to existing categories.');
            $this->command->info('Please reassign products to new categories first, or use --force flag if you understand the risks.');
            return;
        }

        // Clear existing categories safely
        $this->clearExistingCategories();

        // Seed new categories
        $categories = [
            [
                'name' => 'Blood Tests',
                'description' => 'Blood testing kits and related products',
                'sort_order' => 1,
            ],
            [
                'name' => 'Heart Health',
                'description' => 'Products for cardiovascular health and heart care',
                'sort_order' => 2,
            ],
            [
                'name' => 'Medication',
                'description' => 'Prescription and over-the-counter medications',
                'sort_order' => 3,
            ],
            [
                'name' => 'Weight Loss',
                'description' => 'Weight management and weight loss products',
                'sort_order' => 4,
            ],
            [
                'name' => 'Sexual Health',
                'description' => 'Sexual wellness and reproductive health products',
                'sort_order' => 5,
            ],
            [
                'name' => 'Womens Health',
                'description' => 'Health products specifically for women',
                'sort_order' => 6,
            ],
            [
                'name' => 'Mens Health',
                'description' => 'Health products specifically for men',
                'sort_order' => 7,
            ],
            [
                'name' => 'Kids Health',
                'description' => 'Health products for children and pediatric care',
                'sort_order' => 8,
            ],
            [
                'name' => 'Beauty',
                'description' => 'Beauty and cosmetic products',
                'sort_order' => 9,
            ],
            [
                'name' => 'Wellness',
                'description' => 'General wellness and lifestyle products',
                'sort_order' => 10,
            ],
            [
                'name' => 'Supplements and Vitamins',
                'description' => 'Nutritional supplements and vitamin products',
                'sort_order' => 11,
            ],
            [
                'name' => 'Baby Care',
                'description' => 'Products for infant and baby care',
                'sort_order' => 12,
            ],
            [
                'name' => 'Diet and Nutrition',
                'description' => 'Dietary products and nutrition supplements',
                'sort_order' => 13,
            ],
            [
                'name' => 'Ear Care',
                'description' => 'Products for ear health and hearing care',
                'sort_order' => 14,
            ],
            [
                'name' => 'Eye Care',
                'description' => 'Products for eye health and vision care',
                'sort_order' => 15,
            ],
            [
                'name' => 'Health Remedies',
                'description' => 'Natural and alternative health remedies',
                'sort_order' => 16,
            ],
            [
                'name' => 'Skin Care',
                'description' => 'Skincare and dermatological products',
                'sort_order' => 17,
            ],
            [
                'name' => 'Hair Care',
                'description' => 'Hair care and scalp health products',
                'sort_order' => 18,
            ],
        ];

        foreach ($categories as $categoryData) {
            ProductCategory::create([
                'name' => $categoryData['name'],
                'slug' => Str::slug($categoryData['name']),
                'description' => $categoryData['description'],
                'parent_id' => null,
                'sort_order' => $categoryData['sort_order'],
                'is_active' => true,
            ]);
        }

        $this->command->info('Product categories seeded successfully!');
    }

    /**
     * Check if it's safe to update categories.
     */
    private function isSafeToUpdate(): bool
    {
        // Check if any products are assigned to existing categories
        $productsWithCategories = \DB::table('products')
            ->whereNotNull('category_id')
            ->count();

        if ($productsWithCategories > 0) {
            $this->command->warn("Found {$productsWithCategories} products assigned to existing categories.");
            return false;
        }

        return true;
    }

    /**
     * Clear existing product categories safely.
     */
    private function clearExistingCategories(): void
    {
        $existingCount = ProductCategory::count();

        if ($existingCount > 0) {
            // Only delete if no products are assigned
            ProductCategory::query()->delete();
            $this->command->info("Cleared {$existingCount} existing product categories.");
        } else {
            $this->command->info('No existing categories to clear.');
        }
    }
}
