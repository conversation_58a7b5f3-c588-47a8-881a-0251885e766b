import{r as u,c as g,o as $,d as m,e as c,f as U,u as z,m as D,g as O,i as e,t as o,l as r,v as i,q as K,n as P,F as A,p as L,C as W,a as G,W as Q}from"./vendor-BhKTHoN5.js";import{_ as H}from"./AppLayout.vue_vue_type_script_setup_true_lang-DwGvFXKf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const J={class:"py-12"},R={class:"max-w-7xl mx-auto sm:px-6 lg:px-8"},X={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6"},Y={class:"p-6 bg-white border-b border-gray-200"},Z={class:"flex items-center justify-between"},ee={class:"text-gray-600 mt-1"},te={class:"text-right"},se={class:"text-2xl font-bold text-gray-900"},le={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},oe={class:"lg:col-span-2 space-y-6"},ae={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},re={class:"p-6"},de={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ne={class:"md:col-span-2"},ie={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},ue={class:"p-6"},me={key:0,class:"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg"},ce={key:1,class:"mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded-lg"},pe=["disabled"],be={key:0},ge={key:1},ve={class:"lg:col-span-1"},ye={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg sticky top-6"},fe={class:"p-6"},xe={class:"space-y-3 mb-4"},_e={class:"flex-shrink-0"},he=["src","alt"],we={class:"flex-1 min-w-0"},ke={class:"text-sm font-medium text-gray-900 truncate"},Se={class:"text-sm text-gray-500"},Ce={class:"text-sm font-medium text-gray-900"},qe={class:"border-t border-gray-200 pt-4 space-y-2"},Ue={class:"flex justify-between text-sm"},Pe={class:"text-gray-900"},Ae={class:"flex justify-between text-sm"},Ve={class:"text-gray-900"},Ee={class:"flex justify-between text-sm"},Fe={class:"text-gray-900"},Me={class:"border-t border-gray-200 pt-2"},Ne={class:"flex justify-between text-lg font-semibold"},je={class:"text-gray-900"},De={__name:"Checkout",props:{cartItems:Array,subtotal:Number,taxAmount:Number,shippingAmount:Number,totalAmount:Number,stripeKey:String},setup(k){const p=k,V=[{title:"Shop",href:"/shop"},{title:"Shopping Cart",href:"/shop/cart"},{title:"Checkout",href:"/shop/checkout"}],s=u({first_name:"",last_name:"",email:"",phone:"",address_line_1:"",city:"",state:"",postal_code:"",country:"US"}),E=u({first_name:"",last_name:"",address_line_1:"",city:"",state:"",postal_code:"",country:"US"}),F=u(!1),M=u("standard"),d=u(!1),a=u(""),v=u("");let y=null,S=null,f=null;const N=g(()=>"£"+p.subtotal.toFixed(2)),j=g(()=>"£"+p.taxAmount.toFixed(2)),I=g(()=>"£"+p.shippingAmount.toFixed(2)),h=g(()=>"£"+p.totalAmount.toFixed(2)),C=g(()=>p.cartItems.reduce((n,t)=>n+t.quantity,0)),T=async()=>{try{if(!window.Stripe){const t=document.createElement("script");t.src="https://js.stripe.com/v3/",t.async=!0,document.head.appendChild(t),await new Promise((l,b)=>{t.onload=l,t.onerror=b})}y=window.Stripe(p.stripeKey),S=y.elements(),f=S.create("card",{style:{base:{fontSize:"16px",color:"#424770","::placeholder":{color:"#aab7c4"}},invalid:{color:"#9e2146"}}}),await W(),document.getElementById("card-element")&&f.mount("#card-element"),f.on("change",({error:t})=>{const l=document.getElementById("card-errors");t?l.textContent=t.message:l.textContent=""})}catch(n){console.error("Error initializing Stripe:",n),a.value="Failed to initialize payment system"}},B=async()=>{var n,t;if(!d.value){d.value=!0,a.value="",v.value="";try{if(!s.value.first_name||!s.value.last_name||!s.value.email||!s.value.phone||!s.value.address_line_1||!s.value.city||!s.value.state||!s.value.postal_code){a.value="Please fill in all required billing information",d.value=!1;return}const{error:l,paymentMethod:b}=await y.createPaymentMethod({type:"card",card:f,billing_details:{name:`${s.value.first_name} ${s.value.last_name}`,email:s.value.email,phone:s.value.phone,address:{line1:s.value.address_line_1,city:s.value.city,state:s.value.state,postal_code:s.value.postal_code,country:s.value.country}}});if(l){a.value=l.message,d.value=!1;return}const x={billing_address:s.value,shipping_address:F.value?E.value:s.value,shipping_method:M.value,payment_method_id:b.id},_=await G.post("/shop/checkout",x);if(_.data.success){const w=_.data.payment_intent;if(w.status==="requires_action"||w.status==="requires_source_action"){const{error:q}=await y.confirmCardPayment(w.client_secret);if(q){a.value=q.message,d.value=!1;return}}v.value="Order placed successfully!",setTimeout(()=>{Q.visit(`/shop/orders/${_.data.order.order_number}`)},2e3)}else a.value=_.data.message||"Order failed"}catch(l){console.error("Payment error:",l),a.value=((t=(n=l.response)==null?void 0:n.data)==null?void 0:t.message)||"Payment failed. Please try again."}finally{d.value=!1}}};return $(()=>{T()}),(n,t)=>(c(),m(A,null,[U(z(D),{title:"Checkout - Medroid"}),U(H,{breadcrumbs:V},{default:O(()=>[e("div",J,[e("div",R,[e("div",X,[e("div",Y,[e("div",Z,[e("div",null,[t[9]||(t[9]=e("h1",{class:"text-2xl font-bold text-gray-900"},"Checkout",-1)),e("p",ee,o(C.value)+" "+o(C.value===1?"item":"items")+" in your order",1)]),e("div",te,[t[10]||(t[10]=e("p",{class:"text-sm text-gray-600"},"Total",-1)),e("p",se,o(h.value),1)])])])]),e("div",le,[e("div",oe,[e("div",ae,[e("div",re,[t[21]||(t[21]=e("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Billing Information",-1)),e("div",de,[e("div",null,[t[11]||(t[11]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"First Name *",-1)),r(e("input",{"onUpdate:modelValue":t[0]||(t[0]=l=>s.value.first_name=l),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"},null,512),[[i,s.value.first_name]])]),e("div",null,[t[12]||(t[12]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Last Name *",-1)),r(e("input",{"onUpdate:modelValue":t[1]||(t[1]=l=>s.value.last_name=l),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"},null,512),[[i,s.value.last_name]])]),e("div",null,[t[13]||(t[13]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Email *",-1)),r(e("input",{"onUpdate:modelValue":t[2]||(t[2]=l=>s.value.email=l),type:"email",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"},null,512),[[i,s.value.email]])]),e("div",null,[t[14]||(t[14]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Phone *",-1)),r(e("input",{"onUpdate:modelValue":t[3]||(t[3]=l=>s.value.phone=l),type:"tel",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"},null,512),[[i,s.value.phone]])]),e("div",ne,[t[15]||(t[15]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Address *",-1)),r(e("input",{"onUpdate:modelValue":t[4]||(t[4]=l=>s.value.address_line_1=l),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"},null,512),[[i,s.value.address_line_1]])]),e("div",null,[t[16]||(t[16]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"City *",-1)),r(e("input",{"onUpdate:modelValue":t[5]||(t[5]=l=>s.value.city=l),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"},null,512),[[i,s.value.city]])]),e("div",null,[t[17]||(t[17]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"State *",-1)),r(e("input",{"onUpdate:modelValue":t[6]||(t[6]=l=>s.value.state=l),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"},null,512),[[i,s.value.state]])]),e("div",null,[t[18]||(t[18]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Postal Code *",-1)),r(e("input",{"onUpdate:modelValue":t[7]||(t[7]=l=>s.value.postal_code=l),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"},null,512),[[i,s.value.postal_code]])]),e("div",null,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Country *",-1)),r(e("select",{"onUpdate:modelValue":t[8]||(t[8]=l=>s.value.country=l),class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"},t[19]||(t[19]=[e("option",{value:"US"},"United States",-1),e("option",{value:"CA"},"Canada",-1),e("option",{value:"GB"},"United Kingdom",-1)]),512),[[K,s.value.country]])])])])]),e("div",ie,[e("div",ue,[t[22]||(t[22]=e("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Payment Information",-1)),t[23]||(t[23]=e("div",{class:"mb-4"},[e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Card Details"),e("div",{id:"card-element",class:"p-3 border border-gray-300 rounded-lg bg-white"}),e("div",{id:"card-errors",class:"text-red-600 text-sm mt-2"})],-1)),a.value?(c(),m("div",me,o(a.value),1)):P("",!0),v.value?(c(),m("div",ce,o(v.value),1)):P("",!0),e("button",{onClick:B,disabled:d.value,class:"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"},[d.value?(c(),m("span",be,"Processing...")):(c(),m("span",ge,"Place Order - "+o(h.value),1))],8,pe),t[24]||(t[24]=e("div",{class:"mt-4 text-center"},[e("p",{class:"text-sm text-gray-500"}," Secure checkout powered by Stripe ")],-1))])])]),e("div",ve,[e("div",ye,[e("div",fe,[t[29]||(t[29]=e("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Order Summary",-1)),e("div",xe,[(c(!0),m(A,null,L(k.cartItems,l=>{var b,x;return c(),m("div",{key:l.id,class:"flex items-center space-x-3"},[e("div",_e,[e("img",{src:((x=(b=l.product.images)==null?void 0:b[0])==null?void 0:x.url)||"/images/placeholder.jpg",alt:l.product.name,class:"w-12 h-12 object-cover rounded-lg"},null,8,he)]),e("div",we,[e("p",ke,o(l.product.name),1),e("p",Se,"Qty: "+o(l.quantity),1)]),e("div",Ce," $"+o(parseFloat(l.total_price).toFixed(2)),1)])}),128))]),e("div",qe,[e("div",Ue,[t[25]||(t[25]=e("span",{class:"text-gray-600"},"Subtotal",-1)),e("span",Pe,o(N.value),1)]),e("div",Ae,[t[26]||(t[26]=e("span",{class:"text-gray-600"},"Tax",-1)),e("span",Ve,o(j.value),1)]),e("div",Ee,[t[27]||(t[27]=e("span",{class:"text-gray-600"},"Shipping",-1)),e("span",Fe,o(I.value),1)]),e("div",Me,[e("div",Ne,[t[28]||(t[28]=e("span",{class:"text-gray-900"},"Total",-1)),e("span",je,o(h.value),1)])])])])])])])])])]),_:1})],64))}};export{De as default};
