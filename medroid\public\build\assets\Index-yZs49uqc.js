import{z,c as x,r as m,o as U,d,e as o,f as h,u as y,m as F,g,i as t,l as A,v as I,F as k,p as S,t as l,q as T,n as v,A as j,y as $,P as _,x as w}from"./vendor-BhKTHoN5.js";import{_ as q}from"./AppLayout.vue_vue_type_script_setup_true_lang-DwGvFXKf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const O={class:"flex items-center justify-between"},Q={class:"flex mt-2","aria-label":"Breadcrumb"},G={class:"inline-flex items-center space-x-1 md:space-x-3"},H={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},J={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},K={key:0},W={class:"py-12"},X={class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},Y={class:"mb-6 bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},Z={class:"p-6"},ee={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},te=["value"],se={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},ae={class:"p-6 text-gray-900 dark:text-gray-100"},re={key:0,class:"text-center py-8"},oe={key:1,class:"text-center py-8"},de={key:2,class:"overflow-x-auto"},le={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},ie={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},ne={class:"px-6 py-4 whitespace-nowrap"},ce={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},ue={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},ge={class:"px-6 py-4 whitespace-nowrap"},pe={class:"text-sm text-gray-900 dark:text-gray-100"},xe={class:"px-6 py-4 whitespace-nowrap"},me={class:"text-sm text-gray-900 dark:text-gray-100"},ye={class:"px-6 py-4 whitespace-nowrap"},ve={class:"text-sm text-gray-900 dark:text-gray-100"},fe={class:"px-6 py-4 whitespace-nowrap"},he=["onClick"],ke={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},_e=["onClick"],Ae={__name:"Index",setup(we){const B=z(),u=x(()=>{var a;return(a=B.props.auth)==null?void 0:a.user}),b=[{title:"Dashboard",href:"/dashboard"},{title:"Categories",href:"/admin/categories"}],C=m(!1),p=m([]),E=m([]),i=m(""),n=m("all"),f=async()=>{C.value=!0;try{const a=new URLSearchParams;i.value&&a.append("search",i.value),n.value!=="all"&&a.append("parent_id",n.value);const e=await window.axios.get(`/admin/categories-list?${a.toString()}`);e.data.categories&&e.data.categories.data?p.value=e.data.categories.data||[]:Array.isArray(e.data.categories)?p.value=e.data.categories:p.value=[],e.data.parent_categories&&(E.value=e.data.parent_categories)}catch(a){console.error("Error fetching categories:",a),p.value=[]}finally{C.value=!1}},L=async a=>{var e,s;if(confirm("Are you sure you want to delete this category?"))try{const r=await window.axios.delete(`/admin/delete-category/${a}`);r.data.success?(alert("Category deleted successfully"),f()):alert(r.data.message||"Error deleting category")}catch(r){console.error("Error deleting category:",r),alert(((s=(e=r.response)==null?void 0:e.data)==null?void 0:s.message)||"Error deleting category")}},V=async a=>{try{const e=await window.axios.patch(`/admin/toggle-category-status/${a}`);e.data.success?f():alert(e.data.message||"Error updating category status")}catch(e){console.error("Error toggling category status:",e),alert("Error updating category status")}},D=a=>a?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",P=x(()=>p.value.filter(a=>{const e=!i.value||a.name.toLowerCase().includes(i.value.toLowerCase())||a.description&&a.description.toLowerCase().includes(i.value.toLowerCase()),s=n.value==="all"||n.value==="root"&&!a.parent_id||a.parent_id==n.value;return e&&s})),N=x(()=>{var a,e,s,r;return((e=(a=u.value)==null?void 0:a.user_permissions)==null?void 0:e.includes("create products"))||((r=(s=u.value)==null?void 0:s.roles)==null?void 0:r.some(c=>c.name==="admin"))||!1}),M=x(()=>{var a,e,s,r;return((e=(a=u.value)==null?void 0:a.user_permissions)==null?void 0:e.includes("edit products"))||((r=(s=u.value)==null?void 0:s.roles)==null?void 0:r.some(c=>c.name==="admin"))||!1}),R=x(()=>{var a,e,s,r;return((e=(a=u.value)==null?void 0:a.user_permissions)==null?void 0:e.includes("delete products"))||((r=(s=u.value)==null?void 0:s.roles)==null?void 0:r.some(c=>c.name==="admin"))||!1});return U(()=>{f()}),(a,e)=>(o(),d(k,null,[h(y(F),{title:"Product Categories"}),h(q,null,{header:g(()=>[t("div",O,[t("div",null,[e[3]||(e[3]=t("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Product Categories ",-1)),t("nav",Q,[t("ol",G,[(o(),d(k,null,S(b,(s,r)=>t("li",{key:r,class:"inline-flex items-center"},[r<b.length-1?(o(),$(y(_),{key:0,href:s.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:g(()=>[w(l(s.title),1)]),_:2},1032,["href"])):(o(),d("span",H,l(s.title),1)),r<b.length-1?(o(),d("svg",J,e[2]||(e[2]=[t("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):v("",!0)])),64))])])]),N.value?(o(),d("div",K,[h(y(_),{href:"/admin/categories/create",class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"},{default:g(()=>e[4]||(e[4]=[t("i",{class:"fas fa-plus mr-2"},null,-1),w(" Add Category ")])),_:1})])):v("",!0)])]),default:g(()=>[t("div",W,[t("div",X,[t("div",Y,[t("div",Z,[t("div",ee,[t("div",null,[A(t("input",{"onUpdate:modelValue":e[0]||(e[0]=s=>i.value=s),type:"text",placeholder:"Search categories...",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[I,i.value]])]),t("div",null,[A(t("select",{"onUpdate:modelValue":e[1]||(e[1]=s=>n.value=s),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},[e[5]||(e[5]=t("option",{value:"all"},"All Categories",-1)),e[6]||(e[6]=t("option",{value:"root"},"Root Categories",-1)),(o(!0),d(k,null,S(E.value,s=>(o(),d("option",{key:s.id,value:s.id},l(s.name)+" (Subcategories) ",9,te))),128))],512),[[T,n.value]])]),t("div",null,[t("button",{onClick:f,class:"w-full bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"}," Refresh ")])])])]),t("div",se,[t("div",ae,[C.value?(o(),d("div",re,e[7]||(e[7]=[t("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):P.value.length===0?(o(),d("div",oe,e[8]||(e[8]=[t("p",{class:"text-gray-500 dark:text-gray-400"},"No categories found.",-1)]))):(o(),d("div",de,[t("table",le,[e[11]||(e[11]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Category "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Parent "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Products "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Sort Order "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Status "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Actions ")])],-1)),t("tbody",ie,[(o(!0),d(k,null,S(P.value,s=>{var r;return o(),d("tr",{key:s.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[t("td",ne,[t("div",null,[t("div",ce,l(s.name),1),s.description?(o(),d("div",ue,l(s.description),1)):v("",!0)])]),t("td",ge,[t("span",pe,l(((r=s.parent)==null?void 0:r.name)||"Root Category"),1)]),t("td",xe,[t("span",me,l(s.products_count||0),1)]),t("td",ye,[t("span",ve,l(s.sort_order),1)]),t("td",fe,[t("button",{onClick:c=>V(s.id),class:j([D(s.is_active),"inline-flex px-2 py-1 text-xs font-semibold rounded-full cursor-pointer hover:opacity-80"])},l(s.is_active?"Active":"Inactive"),11,he)]),t("td",ke,[h(y(_),{href:`/admin/categories/${s.id}`,class:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"},{default:g(()=>e[9]||(e[9]=[w(" View ")])),_:2},1032,["href"]),M.value?(o(),$(y(_),{key:0,href:`/admin/categories/${s.id}/edit`,class:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3"},{default:g(()=>e[10]||(e[10]=[w(" Edit ")])),_:2},1032,["href"])):v("",!0),R.value?(o(),d("button",{key:1,onClick:c=>L(s.id),class:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"}," Delete ",8,_e)):v("",!0)])])}),128))])])]))])])])])]),_:1})],64))}};export{Ae as default};
