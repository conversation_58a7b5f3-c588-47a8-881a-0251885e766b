import{z as T,c as y,r as S,B as W,o as L,d as l,e as o,f as v,u as h,m as z,g as f,i as t,n,F as w,p as V,j as G,l as g,v as m,t as i,q as E,s as j,P as q,x as C,y as H,W as J}from"./vendor-BhKTHoN5.js";import{_ as K}from"./AppLayout.vue_vue_type_script_setup_true_lang-DwGvFXKf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const Q={class:"flex items-center justify-between"},R={class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"},X={class:"flex mt-2","aria-label":"Breadcrumb"},Y={class:"inline-flex items-center space-x-1 md:space-x-3"},Z={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},ee={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},te={class:"py-12"},re={class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},se={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},ae={class:"p-6"},oe={key:0,class:"mb-6"},le={class:"flex flex-wrap gap-4"},de={key:0,class:"relative"},ie=["src","alt"],ue=["src","alt"],ne={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ge={key:0,class:"text-red-500 text-sm"},ce={key:0,class:"text-red-500 text-sm"},me={key:0,class:"text-red-500 text-sm"},pe=["value"],be={key:0,class:"text-red-500 text-sm"},ye={key:0,class:"text-red-500 text-sm"},fe={key:0,class:"text-red-500 text-sm"},xe={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ke={key:0,class:"text-red-500 text-sm"},ve={key:0,class:"text-red-500 text-sm"},he={key:0,class:"grid grid-cols-1 md:grid-cols-3 gap-6"},we={key:0,class:"text-red-500 text-sm"},_e={key:0,class:"text-red-500 text-sm"},Pe={key:0,class:"text-red-500 text-sm"},Ue={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Ve={key:0,class:"text-red-500 text-sm"},qe={key:0,class:"text-red-500 text-sm"},Ce={class:"flex items-center space-x-6"},De={class:"flex items-center"},Se={class:"flex items-center"},Ee={class:"flex items-center justify-end space-x-3"},je=["disabled"],Ie={__name:"Edit",props:{product:Object,categories:Array},setup(c){const x=c,B=T(),F=y(()=>{var d;return(d=B.props.auth)==null?void 0:d.user}),_=y(()=>{var d;return((d=F.value)==null?void 0:d.role)==="provider"}),P=y(()=>_.value?[{title:"Dashboard",href:"/dashboard"},{title:"My Products",href:"/provider/products"},{title:"Edit Product",href:"#"}]:[{title:"Dashboard",href:"/dashboard"},{title:"Products",href:"/admin/products"},{title:"Edit Product",href:"#"}]),M=y(()=>_.value?"/provider":"/admin"),U=y(()=>_.value?"/provider/products":"/admin/products"),k=S(!1),s=W({name:"",description:"",short_description:"",type:"physical",category_id:"",price:"",sale_price:"",sku:"",stock_quantity:"",manage_stock:!0,weight:"",dimensions:"",is_featured:!1,is_active:!0,digital_files:[],download_limit:"",download_expiry_days:"",featured_image:null,gallery_images:[]}),a=S({}),D=y(()=>s.type==="digital");L(()=>{x.product&&Object.keys(s).forEach(d=>{x.product[d]!==void 0&&d!=="featured_image"&&d!=="gallery_images"&&(s[d]=x.product[d])})});const N=async()=>{var d,e,p;k.value=!0,a.value={};try{const u=new FormData;Object.keys(s).forEach(r=>{r==="featured_image"&&s[r]?u.append(r,s[r]):r==="gallery_images"&&s[r].length>0?s[r].forEach((A,O)=>{u.append(`gallery_images[${O}]`,A)}):r==="digital_files"&&D.value?u.append(r,JSON.stringify(s[r])):s[r]!==null&&s[r]!==""&&u.append(r,s[r])}),(await window.axios.put(`${M.value}/save-product/${x.product.id}`,u,{headers:{"Content-Type":"multipart/form-data"}})).data.success&&J.visit(U.value,{onSuccess:()=>{alert("Product updated successfully!")}})}catch(u){((d=u.response)==null?void 0:d.status)===422?a.value=u.response.data.errors||{}:alert("Error updating product: "+(((p=(e=u.response)==null?void 0:e.data)==null?void 0:p.message)||u.message))}finally{k.value=!1}},$=d=>{const e=d.target.files[0];e&&(s.featured_image=e)},I=d=>{const e=Array.from(d.target.files);s.gallery_images=e};return(d,e)=>(o(),l(w,null,[v(h(z),{title:"Edit Product"}),v(K,null,{header:f(()=>{var p;return[t("div",Q,[t("div",null,[t("h2",R," Edit Product: "+i((p=c.product)==null?void 0:p.name),1),t("nav",X,[t("ol",Y,[(o(!0),l(w,null,V(P.value,(u,b)=>(o(),l("li",{key:b,class:"inline-flex items-center"},[b<P.value.length-1?(o(),H(h(q),{key:0,href:u.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:f(()=>[C(i(u.title),1)]),_:2},1032,["href"])):(o(),l("span",Z,i(u.title),1)),b<P.value.length-1?(o(),l("svg",ee,e[13]||(e[13]=[t("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):n("",!0)]))),128))])])]),v(h(q),{href:U.value,class:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"},{default:f(()=>e[14]||(e[14]=[C(" Back to Products ")])),_:1},8,["href"])])]}),default:f(()=>{var p,u,b;return[t("div",te,[t("div",re,[t("div",se,[t("div",ae,[(p=c.product)!=null&&p.featured_image||(b=(u=c.product)==null?void 0:u.images)!=null&&b.length?(o(),l("div",oe,[e[16]||(e[16]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4"},"Current Images",-1)),t("div",le,[c.product.featured_image?(o(),l("div",de,[t("img",{src:`/storage/${c.product.featured_image}`,alt:c.product.name,class:"w-24 h-24 object-cover rounded border"},null,8,ie),e[15]||(e[15]=t("span",{class:"absolute -top-2 -right-2 bg-blue-500 text-white text-xs px-2 py-1 rounded"},"Featured",-1))])):n("",!0),(o(!0),l(w,null,V(c.product.images,r=>(o(),l("div",{key:r.id,class:"relative"},[t("img",{src:`/storage/${r.image_path}`,alt:r.alt_text,class:"w-24 h-24 object-cover rounded border"},null,8,ue)]))),128))])])):n("",!0),t("form",{onSubmit:G(N,["prevent"]),class:"space-y-6"},[t("div",ne,[t("div",null,[e[17]||(e[17]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Product Name *",-1)),g(t("input",{"onUpdate:modelValue":e[0]||(e[0]=r=>s.name=r),type:"text",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[m,s.name]]),a.value.name?(o(),l("span",ge,i(a.value.name[0]),1)):n("",!0)]),t("div",null,[e[18]||(e[18]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"SKU *",-1)),g(t("input",{"onUpdate:modelValue":e[1]||(e[1]=r=>s.sku=r),type:"text",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[m,s.sku]]),a.value.sku?(o(),l("span",ce,i(a.value.sku[0]),1)):n("",!0)]),t("div",null,[e[20]||(e[20]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Product Type *",-1)),g(t("select",{"onUpdate:modelValue":e[2]||(e[2]=r=>s.type=r),required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},e[19]||(e[19]=[t("option",{value:"physical"},"Physical Product",-1),t("option",{value:"digital"},"Digital Product",-1)]),512),[[E,s.type]]),a.value.type?(o(),l("span",me,i(a.value.type[0]),1)):n("",!0)]),t("div",null,[e[22]||(e[22]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Category *",-1)),g(t("select",{"onUpdate:modelValue":e[3]||(e[3]=r=>s.category_id=r),required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},[e[21]||(e[21]=t("option",{value:""},"Select Category",-1)),(o(!0),l(w,null,V(c.categories,r=>(o(),l("option",{key:r.id,value:r.id},i(r.name),9,pe))),128))],512),[[E,s.category_id]]),a.value.category_id?(o(),l("span",be,i(a.value.category_id[0]),1)):n("",!0)])]),t("div",null,[e[23]||(e[23]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Short Description",-1)),g(t("textarea",{"onUpdate:modelValue":e[4]||(e[4]=r=>s.short_description=r),rows:"2",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[m,s.short_description]]),a.value.short_description?(o(),l("span",ye,i(a.value.short_description[0]),1)):n("",!0)]),t("div",null,[e[24]||(e[24]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Description *",-1)),g(t("textarea",{"onUpdate:modelValue":e[5]||(e[5]=r=>s.description=r),rows:"4",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[m,s.description]]),a.value.description?(o(),l("span",fe,i(a.value.description[0]),1)):n("",!0)]),t("div",xe,[t("div",null,[e[25]||(e[25]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Price *",-1)),g(t("input",{"onUpdate:modelValue":e[6]||(e[6]=r=>s.price=r),type:"number",step:"0.01",min:"0",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[m,s.price]]),a.value.price?(o(),l("span",ke,i(a.value.price[0]),1)):n("",!0)]),t("div",null,[e[26]||(e[26]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Sale Price",-1)),g(t("input",{"onUpdate:modelValue":e[7]||(e[7]=r=>s.sale_price=r),type:"number",step:"0.01",min:"0",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[m,s.sale_price]]),a.value.sale_price?(o(),l("span",ve,i(a.value.sale_price[0]),1)):n("",!0)])]),D.value?n("",!0):(o(),l("div",he,[t("div",null,[e[27]||(e[27]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Stock Quantity *",-1)),g(t("input",{"onUpdate:modelValue":e[8]||(e[8]=r=>s.stock_quantity=r),type:"number",min:"0",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[m,s.stock_quantity]]),a.value.stock_quantity?(o(),l("span",we,i(a.value.stock_quantity[0]),1)):n("",!0)]),t("div",null,[e[28]||(e[28]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Weight (kg)",-1)),g(t("input",{"onUpdate:modelValue":e[9]||(e[9]=r=>s.weight=r),type:"number",step:"0.01",min:"0",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[m,s.weight]]),a.value.weight?(o(),l("span",_e,i(a.value.weight[0]),1)):n("",!0)]),t("div",null,[e[29]||(e[29]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Dimensions",-1)),g(t("input",{"onUpdate:modelValue":e[10]||(e[10]=r=>s.dimensions=r),type:"text",placeholder:"L x W x H",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[m,s.dimensions]]),a.value.dimensions?(o(),l("span",Pe,i(a.value.dimensions[0]),1)):n("",!0)])])),t("div",Ue,[t("div",null,[e[30]||(e[30]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Replace Featured Image",-1)),t("input",{type:"file",accept:"image/*",onChange:$,class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,32),a.value.featured_image?(o(),l("span",Ve,i(a.value.featured_image[0]),1)):n("",!0)]),t("div",null,[e[31]||(e[31]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Add Gallery Images",-1)),t("input",{type:"file",accept:"image/*",multiple:"",onChange:I,class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,32),a.value.gallery_images?(o(),l("span",qe,i(a.value.gallery_images[0]),1)):n("",!0)])]),t("div",Ce,[t("label",De,[g(t("input",{"onUpdate:modelValue":e[11]||(e[11]=r=>s.is_featured=r),type:"checkbox",class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[j,s.is_featured]]),e[32]||(e[32]=t("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"},"Featured Product",-1))]),t("label",Se,[g(t("input",{"onUpdate:modelValue":e[12]||(e[12]=r=>s.is_active=r),type:"checkbox",class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[j,s.is_active]]),e[33]||(e[33]=t("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"},"Active",-1))])]),t("div",Ee,[v(h(q),{href:U.value,class:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"},{default:f(()=>e[34]||(e[34]=[C(" Cancel ")])),_:1},8,["href"]),t("button",{type:"submit",disabled:k.value,class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"},i(k.value?"Updating...":"Update Product"),9,je)])],32)])])])])]}),_:1})],64))}};export{Ie as default};
