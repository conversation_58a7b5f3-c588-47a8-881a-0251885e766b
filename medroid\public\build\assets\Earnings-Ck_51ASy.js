import{_ as C}from"./AppLayout.vue_vue_type_script_setup_true_lang-DwGvFXKf.js";import{r as c,c as T,o as E,d,e as x,f as h,u as D,m as S,g as B,i as t,A as n,x as b,t as a,F as w,p as N,a as P}from"./vendor-BhKTHoN5.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const F={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},L={class:"mb-6 flex justify-between items-center"},$={class:"flex bg-gray-100 rounded-lg p-1"},G=["disabled"],U={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},V={class:"bg-white rounded-lg shadow-sm border p-6"},A={class:"flex items-center"},j={class:"text-2xl font-bold text-gray-900"},z={class:"bg-white rounded-lg shadow-sm border p-6"},H={class:"flex items-center"},I={class:"text-2xl font-bold text-gray-900"},O={class:"bg-white rounded-lg shadow-sm border p-6"},R={class:"flex items-center"},W={class:"text-2xl font-bold text-gray-900"},Y={class:"bg-white rounded-lg shadow-sm border p-6"},q={class:"flex items-center"},J={class:"text-2xl font-bold text-gray-900"},K={class:"bg-white rounded-lg shadow-sm border"},Q={class:"p-6"},X={key:0,class:"text-center py-8"},Z={key:1,class:"text-center py-8"},tt={key:2,class:"overflow-x-auto"},et={class:"min-w-full divide-y divide-gray-200"},st={class:"bg-white divide-y divide-gray-200"},at={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},rt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},ot={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},lt={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},nt={class:"px-6 py-4 whitespace-nowrap"},gt={__name:"Earnings",setup(it){const _=[{title:"Dashboard",href:"/dashboard"},{title:"Earnings",href:"/provider/earnings"}],i=c(!1),r=c({total:0,thisMonth:0,lastMonth:0,pending:0,paid:0}),g=c([]),l=c("month"),m=T(()=>r.value.lastMonth===0?0:((r.value.thisMonth-r.value.lastMonth)/r.value.lastMonth*100).toFixed(1)),u=async()=>{i.value=!0;try{const s=await P.get("/provider/get-earnings",{params:{period:l.value}});r.value=s.data.earnings||r.value,g.value=s.data.transactions||[]}catch(s){console.error("Error fetching earnings:",s)}finally{i.value=!1}},p=s=>new Intl.NumberFormat("en-GB",{style:"currency",currency:"GBP"}).format(s),k=s=>new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),M=s=>({paid:"bg-green-100 text-green-800",pending:"bg-yellow-100 text-yellow-800",processing:"bg-blue-100 text-blue-800",failed:"bg-red-100 text-red-800"})[s]||"bg-gray-100 text-gray-800";return E(()=>{u()}),(s,e)=>(x(),d(w,null,[h(D(S),{title:"Earnings"}),h(C,{breadcrumbs:_},{default:B(()=>[t("div",F,[e[15]||(e[15]=t("div",{class:"mb-8"},[t("h1",{class:"text-3xl font-bold text-gray-900"},"Earnings"),t("p",{class:"mt-2 text-gray-600"},"Track your income and payment history")],-1)),t("div",L,[t("div",$,[t("button",{onClick:e[0]||(e[0]=o=>{l.value="week",u()}),class:n(["px-3 py-1 rounded text-sm font-medium transition-colors",l.value==="week"?"bg-white text-gray-900 shadow":"text-gray-600 hover:text-gray-900"])}," This Week ",2),t("button",{onClick:e[1]||(e[1]=o=>{l.value="month",u()}),class:n(["px-3 py-1 rounded text-sm font-medium transition-colors",l.value==="month"?"bg-white text-gray-900 shadow":"text-gray-600 hover:text-gray-900"])}," This Month ",2),t("button",{onClick:e[2]||(e[2]=o=>{l.value="year",u()}),class:n(["px-3 py-1 rounded text-sm font-medium transition-colors",l.value==="year"?"bg-white text-gray-900 shadow":"text-gray-600 hover:text-gray-900"])}," This Year ",2)]),t("button",{onClick:u,disabled:i.value,class:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"},[t("i",{class:n(["fas",i.value?"fa-spinner fa-spin":"fa-sync-alt","mr-2"])},null,2),b(" "+a(i.value?"Loading...":"Refresh"),1)],8,G)]),t("div",U,[t("div",V,[t("div",A,[e[4]||(e[4]=t("div",{class:"p-3 rounded-full bg-green-100 mr-4"},[t("i",{class:"fas fa-dollar-sign text-green-600 text-xl"})],-1)),t("div",null,[e[3]||(e[3]=t("p",{class:"text-sm text-gray-500"},"Total Earnings",-1)),t("p",j,a(p(r.value.total)),1)])])]),t("div",z,[t("div",H,[e[6]||(e[6]=t("div",{class:"p-3 rounded-full bg-blue-100 mr-4"},[t("i",{class:"fas fa-calendar-alt text-blue-600 text-xl"})],-1)),t("div",null,[e[5]||(e[5]=t("p",{class:"text-sm text-gray-500"},"This Month",-1)),t("p",I,a(p(r.value.thisMonth)),1),t("p",{class:n(["text-xs mt-1",m.value>=0?"text-green-600":"text-red-600"])},[t("i",{class:n(["fas",m.value>=0?"fa-arrow-up":"fa-arrow-down","mr-1"])},null,2),b(" "+a(Math.abs(m.value))+"% from last month ",1)],2)])])]),t("div",O,[t("div",R,[e[8]||(e[8]=t("div",{class:"p-3 rounded-full bg-yellow-100 mr-4"},[t("i",{class:"fas fa-clock text-yellow-600 text-xl"})],-1)),t("div",null,[e[7]||(e[7]=t("p",{class:"text-sm text-gray-500"},"Pending",-1)),t("p",W,a(p(r.value.pending)),1)])])]),t("div",Y,[t("div",q,[e[10]||(e[10]=t("div",{class:"p-3 rounded-full bg-purple-100 mr-4"},[t("i",{class:"fas fa-check-circle text-purple-600 text-xl"})],-1)),t("div",null,[e[9]||(e[9]=t("p",{class:"text-sm text-gray-500"},"Paid Out",-1)),t("p",J,a(p(r.value.paid)),1)])])])]),t("div",K,[e[14]||(e[14]=t("div",{class:"p-6 border-b"},[t("h2",{class:"text-xl font-semibold text-gray-900"},"Transaction History")],-1)),t("div",Q,[i.value?(x(),d("div",X,e[11]||(e[11]=[t("i",{class:"fas fa-spinner fa-spin text-2xl text-gray-400 mb-4"},null,-1),t("p",{class:"text-gray-600"},"Loading transactions...",-1)]))):g.value.length===0?(x(),d("div",Z,e[12]||(e[12]=[t("i",{class:"fas fa-receipt text-4xl text-gray-300 mb-4"},null,-1),t("p",{class:"text-gray-600"},"No transactions found",-1),t("p",{class:"text-sm text-gray-500 mt-2"},"Transactions will appear here after completed appointments",-1)]))):(x(),d("div",tt,[t("table",et,[e[13]||(e[13]=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Date "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Patient "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Service "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Amount "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Status ")])],-1)),t("tbody",st,[(x(!0),d(w,null,N(g.value,o=>{var y,f,v;return x(),d("tr",{key:o.id},[t("td",at,a(k(o.created_at)),1),t("td",rt,a(((f=(y=o.patient)==null?void 0:y.user)==null?void 0:f.name)||"Unknown"),1),t("td",ot,a(((v=o.service)==null?void 0:v.name)||"Consultation"),1),t("td",lt,a(p(o.amount)),1),t("td",nt,[t("span",{class:n(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",M(o.status)])},a(o.status.toUpperCase()),3)])])}),128))])])]))])])])]),_:1})],64))}};export{gt as default};
