import{r as b,w as z,d as r,n as i,e as s,i as e,t as o,x as p,A as k,F as C,p as Y,N as J,o as K,c as _,f as T,u as H,m as Q,g as R,l as h,v as L,q as M,y as W,P as X}from"./vendor-BhKTHoN5.js";import{_ as Z}from"./AppLayout.vue_vue_type_script_setup_true_lang-DwGvFXKf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const ee={key:0,class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},te={class:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},ae={class:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full"},se={class:"bg-white dark:bg-gray-800 px-6 py-4 border-b border-gray-200 dark:border-gray-700"},re={class:"flex items-center justify-between"},le={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},de={key:0,class:"bg-white dark:bg-gray-800 px-6 py-4 max-h-96 overflow-y-auto"},oe={class:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6"},ne={class:"space-y-2"},ie={class:"flex items-center mb-3"},ge={key:0,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 mr-2"},ue={key:1,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 mr-2"},xe={key:0,class:"flex items-center"},ye={class:"text-sm text-gray-900 dark:text-gray-100"},ce={class:"text-xs text-gray-500 dark:text-gray-400 ml-2"},pe={class:"flex items-center"},fe={key:0,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 mr-2"},me={key:1,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 mr-2"},be={key:2,class:"text-sm text-gray-900 dark:text-gray-100"},ve={key:3,class:"text-sm text-gray-900 dark:text-gray-100"},ke={key:4,class:"text-xs text-gray-500 dark:text-gray-400 ml-2"},he={class:"space-y-2"},_e={class:"flex items-center"},we={key:0,class:"flex items-center"},$e={class:"text-xs text-gray-500 dark:text-gray-400 mr-2"},Ce={class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"},Ae={key:1,class:"flex items-center"},Se={key:2,class:"space-y-1"},Ie={key:0,class:"flex items-center"},Ee={key:1,class:"flex items-center"},Ue={class:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-4"},Pe={class:"space-y-4 max-h-64 overflow-y-auto border border-gray-200 dark:border-gray-700 rounded-lg p-4"},Fe={class:"flex items-start justify-between mb-2"},De={class:"flex items-center"},Ne={class:"text-xs text-gray-500 dark:text-gray-400"},Me={class:"flex items-center space-x-2"},Ve={key:0,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"},Be={key:1,class:"text-xs text-green-500"},je={key:2,class:"text-xs text-yellow-500",title:"Message was truncated"},Te={class:"text-sm text-gray-900 dark:text-gray-100"},Re={key:0,class:"mt-2 text-xs text-gray-500 dark:text-gray-400"},Le={key:0,class:"flex items-center"},ze={class:"w-16 bg-gray-200 rounded-full h-1.5 dark:bg-gray-700"},Ye={class:"ml-2"},Oe={key:0,class:"text-center text-gray-500 dark:text-gray-400 py-8"},qe={class:"bg-gray-50 dark:bg-gray-700 px-6 py-4 flex items-center justify-between"},Ge={class:"flex space-x-3"},He={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},Je={key:0,class:"text-red-600 dark:text-red-400"},Ke={__name:"ChatDetailModal",props:{show:Boolean,chatId:[String,Number]},emits:["close","updated"],setup(V,{emit:w}){const f=V,y=w,d=b(null),u=b([]),x=b(!1);z(()=>f.chatId,g=>{g&&f.show&&v()}),z(()=>f.show,g=>{g&&f.chatId&&v()});const v=async()=>{if(f.chatId){x.value=!0;try{const g=await window.axios.get(`/chats-detail/${f.chatId}`);if(d.value=g.data,d.value.type==="ai_chat")u.value=d.value.messages||[];else{const l=await window.axios.get(`/chats-messages/${f.chatId}`);u.value=l.data.messages||[]}}catch(g){console.error("Error fetching chat details:",g)}finally{x.value=!1}}},A=async()=>{const g=prompt("Please provide a reason for flagging this chat:");if(g)try{await window.axios.post(`/chats-flag/${f.chatId}`,{reason:g}),await v(),y("updated")}catch(l){console.error("Error flagging chat:",l),alert("Failed to flag chat")}},$=async()=>{try{await window.axios.post(`/chats-unflag/${f.chatId}`),await v(),y("updated")}catch(g){console.error("Error unflagging chat:",g),alert("Failed to remove flag")}},S=async()=>{if(confirm("Are you sure you want to archive this chat?"))try{await window.axios.post(`/chats-archive/${f.chatId}`),await v(),y("updated")}catch(g){console.error("Error archiving chat:",g),alert("Failed to archive chat")}},I=g=>new Date(g).toLocaleString(),B=g=>({active:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",inactive:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200",ended:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"})[g]||"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200",j=g=>({patient:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",provider:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",admin:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",system:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"})[g]||"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200",E=g=>({user:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",assistant:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",system:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"})[g]||"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200";return(g,l)=>{var U,P,F,D,n,t,a,m;return V.show?(s(),r("div",ee,[e("div",te,[e("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:l[0]||(l[0]=c=>g.$emit("close"))}),e("div",ae,[e("div",se,[e("div",re,[e("div",null,[l[3]||(l[3]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-gray-100"}," Chat Details ",-1)),d.value?(s(),r("p",le,o(d.value.title||"Untitled Chat")+" - "+o(I(d.value.created_at)),1)):i("",!0)]),e("button",{onClick:l[1]||(l[1]=c=>g.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},l[4]||(l[4]=[e("i",{class:"fas fa-times text-xl"},null,-1)]))])]),d.value?(s(),r("div",de,[e("div",oe,[e("div",null,[l[8]||(l[8]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2"},"Participants",-1)),e("div",ne,[e("div",ie,[d.value.type==="ai_chat"?(s(),r("span",ge,l[5]||(l[5]=[e("i",{class:"fas fa-robot mr-1"},null,-1),p(" AI Chat ")]))):(s(),r("span",ue,l[6]||(l[6]=[e("i",{class:"fas fa-users mr-1"},null,-1),p(" User Chat ")])))]),d.value.patient?(s(),r("div",xe,[l[7]||(l[7]=e("span",{class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 mr-2"}," Patient ",-1)),e("span",ye,o(((U=d.value.patient.user)==null?void 0:U.name)||"Unknown"),1),e("span",ce," ("+o(((P=d.value.patient.user)==null?void 0:P.email)||"No email")+") ",1)])):i("",!0),e("div",pe,[d.value.type==="ai_chat"?(s(),r("span",fe," AI Assistant ")):d.value.provider?(s(),r("span",me," Provider ")):i("",!0),d.value.type==="ai_chat"?(s(),r("span",be," AI Clinical Assistant ")):d.value.provider?(s(),r("span",ve,o(((F=d.value.provider.user)==null?void 0:F.name)||"Unknown"),1)):i("",!0),d.value.provider?(s(),r("span",ke," ("+o(((D=d.value.provider.user)==null?void 0:D.email)||"No email")+") ",1)):i("",!0)])])]),e("div",null,[l[13]||(l[13]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2"},"Status & Flags",-1)),e("div",he,[e("div",_e,[l[9]||(l[9]=e("span",{class:"text-xs text-gray-500 dark:text-gray-400 mr-2"},"Status:",-1)),d.value.type==="ai_chat"?(s(),r("span",{key:0,class:k([d.value.escalated?"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200":"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200","inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},o(d.value.escalated?"Escalated":"Active"),3)):(s(),r("span",{key:1,class:k([B(d.value.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},o(d.value.status),3))]),d.value.is_flagged||d.value.type==="ai_chat"&&d.value.escalated?(s(),r("div",we,[e("span",$e,o(d.value.type==="ai_chat"?"Escalated:":"Flagged:"),1),e("span",Ce,o(d.value.flag_reason||d.value.escalation_reason||"No reason provided"),1)])):i("",!0),d.value.is_archived?(s(),r("div",Ae,l[10]||(l[10]=[e("span",{class:"text-xs text-gray-500 dark:text-gray-400 mr-2"},"Archived:",-1),e("span",{class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"}," Yes ",-1)]))):i("",!0),d.value.type==="ai_chat"?(s(),r("div",Se,[d.value.is_anonymous?(s(),r("div",Ie,l[11]||(l[11]=[e("span",{class:"text-xs text-gray-500 dark:text-gray-400 mr-2"},"Anonymous:",-1),e("span",{class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"}," Yes ",-1)]))):i("",!0),d.value.is_public?(s(),r("div",Ee,l[12]||(l[12]=[e("span",{class:"text-xs text-gray-500 dark:text-gray-400 mr-2"},"Public:",-1),e("span",{class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"}," Yes ",-1)]))):i("",!0)])):i("",!0)])])]),e("div",null,[e("h4",Ue,"Messages ("+o(u.value.length)+")",1),e("div",Pe,[(s(!0),r(C,null,Y(u.value,(c,N)=>(s(),r("div",{key:c.id||N,class:"border-b border-gray-100 dark:border-gray-700 pb-3 last:border-b-0"},[e("div",Fe,[e("div",De,[d.value.type==="ai_chat"?(s(),r("span",{key:0,class:k([E(c.role),"inline-flex px-2 py-1 text-xs font-semibold rounded-full mr-2"])},o(c.role==="user"?"Patient":"AI Assistant"),3)):(s(),r("span",{key:1,class:k([j(c.sender_type),"inline-flex px-2 py-1 text-xs font-semibold rounded-full mr-2"])},o(c.sender_type),3)),e("span",Ne,o(I(c.created_at||c.timestamp)),1)]),e("div",Me,[c.is_system_message?(s(),r("span",Ve," System ")):i("",!0),c.is_read?(s(),r("span",Be,l[14]||(l[14]=[e("i",{class:"fas fa-check-double"},null,-1)]))):i("",!0),c.isTruncated?(s(),r("span",je,l[15]||(l[15]=[e("i",{class:"fas fa-cut"},null,-1)]))):i("",!0)])]),e("p",Te,o(c.content),1),d.value.type==="ai_chat"&&c.metadata?(s(),r("div",Re,[c.metadata.confidence?(s(),r("div",Le,[l[16]||(l[16]=e("span",{class:"mr-2"},"Confidence:",-1)),e("div",ze,[e("div",{class:"bg-blue-600 h-1.5 rounded-full",style:J(`width: ${c.metadata.confidence*100}%`)},null,4)]),e("span",Ye,o(Math.round(c.metadata.confidence*100))+"%",1)])):i("",!0)])):i("",!0)]))),128)),u.value.length===0?(s(),r("div",Oe," No messages in this chat ")):i("",!0)])])])):i("",!0),e("div",qe,[e("div",Ge,[((n=d.value)==null?void 0:n.type)==="user_chat"?(s(),r(C,{key:0},[(t=d.value)!=null&&t.is_flagged?(s(),r("button",{key:1,onClick:$,class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"},l[18]||(l[18]=[e("i",{class:"fas fa-flag mr-2"},null,-1),p(" Remove Flag ")]))):(s(),r("button",{key:0,onClick:A,class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"},l[17]||(l[17]=[e("i",{class:"fas fa-flag mr-2"},null,-1),p(" Flag Chat ")]))),(a=d.value)!=null&&a.is_archived?i("",!0):(s(),r("button",{key:2,onClick:S,class:"inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"},l[19]||(l[19]=[e("i",{class:"fas fa-archive mr-2"},null,-1),p(" Archive ")])))],64)):((m=d.value)==null?void 0:m.type)==="ai_chat"?(s(),r("div",He,[l[20]||(l[20]=p(" AI chats are managed automatically. ")),d.value.escalated?(s(),r("span",Je,"This conversation was escalated for human review.")):i("",!0)])):i("",!0)]),e("button",{onClick:l[2]||(l[2]=c=>g.$emit("close")),class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"}," Close ")])])])])):i("",!0)}}},Qe={class:"flex items-center justify-between"},We={class:"flex mt-2","aria-label":"Breadcrumb"},Xe={class:"inline-flex items-center space-x-1 md:space-x-3"},Ze={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},et={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},tt={class:"py-12"},at={class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},st={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6"},rt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},lt={class:"p-6"},dt={class:"flex items-center"},ot={class:"ml-4"},nt={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},it={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},gt={class:"p-6"},ut={class:"flex items-center"},xt={class:"ml-4"},yt={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},ct={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},pt={class:"p-6"},ft={class:"flex items-center"},mt={class:"ml-4"},bt={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},vt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},kt={class:"p-6"},ht={class:"flex items-center"},_t={class:"ml-4"},wt={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},$t={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6"},Ct={class:"p-6"},At={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-4"},St={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},It={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},Et={class:"p-6 text-gray-900 dark:text-gray-100"},Ut={key:0,class:"text-center py-8"},Pt={key:1,class:"overflow-x-auto"},Ft={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Dt={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Nt={class:"px-6 py-4 whitespace-nowrap"},Mt={key:0,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"},Vt={key:1,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"},Bt={class:"px-6 py-4 whitespace-nowrap"},jt={class:"flex items-center"},Tt={class:"flex-shrink-0 h-10 w-10"},Rt={class:"ml-4"},Lt={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},zt={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},Yt={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},Ot={class:"text-xs text-gray-400 dark:text-gray-500"},qt={class:"px-6 py-4 whitespace-nowrap"},Gt={class:"text-sm text-gray-900 dark:text-gray-100"},Ht={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},Jt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},Kt={class:"px-6 py-4 whitespace-nowrap"},Qt={class:"px-6 py-4 whitespace-nowrap"},Wt={class:"flex flex-col space-y-1"},Xt={key:0,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"},Zt={key:1,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"},ea={key:2,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"},ta={key:3,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"},aa={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},sa=["onClick"],ra={key:0},la={key:2,class:"mt-6 flex items-center justify-between"},da={class:"text-sm text-gray-700 dark:text-gray-300"},oa={class:"flex space-x-2"},na=["disabled"],ia={class:"px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300"},ga=["disabled"],ma={__name:"Chats",setup(V){const w=[{title:"Dashboard",href:"/dashboard"},{title:"Chats",href:"/chats"}],f=b(!1),y=b([]),d=b({}),u=b({current_page:1,last_page:1,per_page:15,total:0}),x=b({search:"",type:"",role:"",status:"",flagged:"",archived:"",start_date:"",end_date:"",sort_by:"created_at",sort_dir:"desc",per_page:15,page:1}),v=b(!1),A=b(null),$=async()=>{f.value=!0;try{const n=new URLSearchParams;Object.keys(x.value).forEach(m=>{x.value[m]!==""&&x.value[m]!==null&&n.append(m,x.value[m])});const a=(await window.axios.get(`/chats-list?${n.toString()}`)).data;Array.isArray(a)?(y.value=a,u.value={current_page:1,last_page:1,per_page:a.length,total:a.length}):a&&Array.isArray(a.data)?(y.value=a.data,u.value={current_page:a.current_page||1,last_page:a.last_page||1,per_page:a.per_page||15,total:a.total||0}):(console.warn("Unexpected response structure:",a),y.value=[],u.value={current_page:1,last_page:1,per_page:15,total:0})}catch(n){console.error("Error fetching chats:",n),y.value=[],u.value={current_page:1,last_page:1,per_page:15,total:0}}finally{f.value=!1}},S=async()=>{try{const n=await window.axios.get("/chats-stats");d.value=n.data}catch(n){console.error("Error fetching stats:",n)}};K(()=>{$(),S()}),z(x,()=>{x.value.page=1,$()},{deep:!0});const I=()=>{x.value={search:"",type:"",role:"",status:"",flagged:"",archived:"",start_date:"",end_date:"",sort_by:"created_at",sort_dir:"desc",per_page:15,page:1}},B=n=>{A.value=n,v.value=!0},j=()=>{$(),S()},E=n=>{x.value.page=n},g=n=>({active:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",inactive:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200",ended:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"})[n]||"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200",l=n=>new Date(n).toLocaleString(),U=_(()=>u.value.total||0),P=_(()=>Array.isArray(y.value)?y.value.filter(n=>n.status==="active"||n.type==="ai_chat"&&!n.escalated).length:0),F=_(()=>Array.isArray(y.value)?y.value.filter(n=>n.is_flagged||n.type==="ai_chat"&&n.escalated).length:0);_(()=>Array.isArray(y.value)?y.value.filter(n=>n.is_archived).length:0);const D=_(()=>Array.isArray(y.value)?y.value.filter(n=>n.type==="ai_chat").length:0);return _(()=>Array.isArray(y.value)?y.value.filter(n=>n.type==="user_chat").length:0),(n,t)=>(s(),r(C,null,[T(H(Q),{title:"Chat Management"}),T(Z,null,{header:R(()=>[e("div",Qe,[e("div",null,[t[11]||(t[11]=e("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Chat Management ",-1)),e("nav",We,[e("ol",Xe,[(s(),r(C,null,Y(w,(a,m)=>e("li",{key:m,class:"inline-flex items-center"},[m<w.length-1?(s(),W(H(X),{key:0,href:a.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:R(()=>[p(o(a.title),1)]),_:2},1032,["href"])):(s(),r("span",Ze,o(a.title),1)),m<w.length-1?(s(),r("svg",et,t[10]||(t[10]=[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):i("",!0)])),64))])])])])]),default:R(()=>[e("div",tt,[e("div",at,[e("div",st,[e("div",rt,[e("div",lt,[e("div",dt,[t[13]||(t[13]=e("div",{class:"flex-shrink-0"},[e("i",{class:"fas fa-comments text-2xl text-blue-500"})],-1)),e("div",ot,[t[12]||(t[12]=e("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Total Chats",-1)),e("p",nt,o(U.value),1)])])])]),e("div",it,[e("div",gt,[e("div",ut,[t[15]||(t[15]=e("div",{class:"flex-shrink-0"},[e("i",{class:"fas fa-circle text-2xl text-green-500"})],-1)),e("div",xt,[t[14]||(t[14]=e("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Active Chats",-1)),e("p",yt,o(P.value),1)])])])]),e("div",ct,[e("div",pt,[e("div",ft,[t[17]||(t[17]=e("div",{class:"flex-shrink-0"},[e("i",{class:"fas fa-robot text-2xl text-purple-500"})],-1)),e("div",mt,[t[16]||(t[16]=e("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"AI Chats",-1)),e("p",bt,o(D.value),1)])])])]),e("div",vt,[e("div",kt,[e("div",ht,[t[19]||(t[19]=e("div",{class:"flex-shrink-0"},[e("i",{class:"fas fa-flag text-2xl text-red-500"})],-1)),e("div",_t,[t[18]||(t[18]=e("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Flagged/Escalated",-1)),e("p",wt,o(F.value),1)])])])])]),e("div",$t,[e("div",Ct,[e("div",At,[e("div",null,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Search",-1)),h(e("input",{"onUpdate:modelValue":t[0]||(t[0]=a=>x.value.search=a),type:"text",placeholder:"Search users, messages...",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},null,512),[[L,x.value.search]])]),e("div",null,[t[22]||(t[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Chat Type",-1)),h(e("select",{"onUpdate:modelValue":t[1]||(t[1]=a=>x.value.type=a),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},t[21]||(t[21]=[e("option",{value:""},"All Types",-1),e("option",{value:"user_chat"},"User Chats",-1),e("option",{value:"ai_chat"},"AI Chats",-1)]),512),[[M,x.value.type]])]),e("div",null,[t[24]||(t[24]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Role",-1)),h(e("select",{"onUpdate:modelValue":t[2]||(t[2]=a=>x.value.role=a),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},t[23]||(t[23]=[e("option",{value:""},"All Roles",-1),e("option",{value:"patient"},"Patient",-1),e("option",{value:"provider"},"Provider",-1)]),512),[[M,x.value.role]])]),e("div",null,[t[26]||(t[26]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Status",-1)),h(e("select",{"onUpdate:modelValue":t[3]||(t[3]=a=>x.value.status=a),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},t[25]||(t[25]=[e("option",{value:""},"All Status",-1),e("option",{value:"active"},"Active",-1),e("option",{value:"inactive"},"Inactive",-1),e("option",{value:"ended"},"Ended",-1),e("option",{value:"escalated"},"Escalated",-1)]),512),[[M,x.value.status]])]),e("div",null,[t[28]||(t[28]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Flagged/Escalated",-1)),h(e("select",{"onUpdate:modelValue":t[4]||(t[4]=a=>x.value.flagged=a),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},t[27]||(t[27]=[e("option",{value:""},"All",-1),e("option",{value:"true"},"Flagged/Escalated Only",-1),e("option",{value:"false"},"Not Flagged/Escalated",-1)]),512),[[M,x.value.flagged]])])]),e("div",St,[e("div",null,[t[29]||(t[29]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Start Date",-1)),h(e("input",{"onUpdate:modelValue":t[5]||(t[5]=a=>x.value.start_date=a),type:"date",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},null,512),[[L,x.value.start_date]])]),e("div",null,[t[30]||(t[30]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"End Date",-1)),h(e("input",{"onUpdate:modelValue":t[6]||(t[6]=a=>x.value.end_date=a),type:"date",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},null,512),[[L,x.value.end_date]])]),e("div",{class:"flex items-end"},[e("button",{onClick:I,class:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"},t[31]||(t[31]=[e("i",{class:"fas fa-times mr-2"},null,-1),p(" Clear Filters ")]))])])])]),e("div",It,[e("div",Et,[f.value?(s(),r("div",Ut,t[32]||(t[32]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):(s(),r("div",Pt,[e("table",Ft,[t[41]||(t[41]=e("thead",{class:"bg-gray-50 dark:bg-gray-700"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Type "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Participants "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Created "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Messages "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Status "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Flags "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Actions ")])],-1)),e("tbody",Dt,[(s(!0),r(C,null,Y(Array.isArray(y.value)?y.value:[],a=>{var m,c,N,O,q,G;return s(),r("tr",{key:a.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",Nt,[a.type==="ai_chat"?(s(),r("span",Mt,t[33]||(t[33]=[e("i",{class:"fas fa-robot mr-1"},null,-1),p(" AI Chat ")]))):(s(),r("span",Vt,t[34]||(t[34]=[e("i",{class:"fas fa-users mr-1"},null,-1),p(" User Chat ")])))]),e("td",Bt,[e("div",jt,[e("div",Tt,[e("div",{class:k(["h-10 w-10 rounded-full flex items-center justify-center",a.type==="ai_chat"?"bg-purple-100 dark:bg-purple-900":"bg-blue-100 dark:bg-blue-900"])},[e("i",{class:k(a.type==="ai_chat"?"fas fa-robot text-purple-600 dark:text-purple-400":"fas fa-comment text-blue-600 dark:text-blue-400")},null,2)],2)]),e("div",Rt,[e("div",Lt,o(((c=(m=a.patient)==null?void 0:m.user)==null?void 0:c.name)||"Unknown Patient"),1),a.type==="user_chat"?(s(),r("div",zt," with "+o(((O=(N=a.provider)==null?void 0:N.user)==null?void 0:O.name)||"Unknown Provider"),1)):(s(),r("div",Yt," with AI Assistant ")),e("div",Ot,o(((G=(q=a.patient)==null?void 0:q.user)==null?void 0:G.email)||""),1)])])]),e("td",qt,[e("div",Gt,o(l(a.created_at)),1),a.title?(s(),r("div",Ht,o(a.title),1)):i("",!0)]),e("td",Jt,o(a.message_count||0),1),e("td",Kt,[a.type==="ai_chat"?(s(),r("span",{key:0,class:k([a.escalated?"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200":"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200","inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},o(a.escalated?"Escalated":"Active"),3)):(s(),r("span",{key:1,class:k([g(a.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},o(a.status),3))]),e("td",Qt,[e("div",Wt,[a.is_flagged||a.type==="ai_chat"&&a.escalated?(s(),r("span",Xt,[t[35]||(t[35]=e("i",{class:"fas fa-flag mr-1"},null,-1)),p(" "+o(a.type==="ai_chat"?"Escalated":"Flagged"),1)])):i("",!0),a.is_archived?(s(),r("span",Zt,t[36]||(t[36]=[e("i",{class:"fas fa-archive mr-1"},null,-1),p(" Archived ")]))):i("",!0),a.type==="ai_chat"&&a.is_anonymous?(s(),r("span",ea,t[37]||(t[37]=[e("i",{class:"fas fa-user-secret mr-1"},null,-1),p(" Anonymous ")]))):i("",!0),a.type==="ai_chat"&&a.is_public?(s(),r("span",ta,t[38]||(t[38]=[e("i",{class:"fas fa-globe mr-1"},null,-1),p(" Public ")]))):i("",!0)])]),e("td",aa,[e("button",{onClick:ua=>B(a.id),class:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"},t[39]||(t[39]=[e("i",{class:"fas fa-eye mr-1"},null,-1),p(" View Details ")]),8,sa)])])}),128)),!Array.isArray(y.value)||y.value.length===0?(s(),r("tr",ra,t[40]||(t[40]=[e("td",{colspan:"7",class:"px-6 py-4 text-center text-gray-500 dark:text-gray-400"}," No chats found matching your criteria ",-1)]))):i("",!0)])])])),u.value.last_page>1?(s(),r("div",la,[e("div",da," Showing "+o((u.value.current_page-1)*u.value.per_page+1)+" to "+o(Math.min(u.value.current_page*u.value.per_page,u.value.total))+" of "+o(u.value.total)+" results ",1),e("div",oa,[e("button",{onClick:t[7]||(t[7]=a=>E(u.value.current_page-1)),disabled:u.value.current_page<=1,class:"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"}," Previous ",8,na),e("span",ia," Page "+o(u.value.current_page)+" of "+o(u.value.last_page),1),e("button",{onClick:t[8]||(t[8]=a=>E(u.value.current_page+1)),disabled:u.value.current_page>=u.value.last_page,class:"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"}," Next ",8,ga)])])):i("",!0)])])])]),T(Ke,{show:v.value,"chat-id":A.value,onClose:t[9]||(t[9]=a=>v.value=!1),onUpdated:j},null,8,["show","chat-id"])]),_:1})],64))}};export{ma as default};
