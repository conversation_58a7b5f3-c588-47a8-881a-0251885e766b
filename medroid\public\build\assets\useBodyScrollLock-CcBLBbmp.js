import{a as R,o as Z,c as ee,b as te}from"./useForwardExpose-DjhPD9_V.js";import{i as T,g as ne,h as oe,j as se,k as N}from"./index-CGRqDMLC.js";import{r as w,a9 as P,C,J as U,c as O,B as K,y as H,e as j,g as $,K as z,u as S,N as re,w as Y,H as ie}from"./vendor-BhKTHoN5.js";import{P as q}from"./Primitive-DSQomZit.js";import{i as ae}from"./useForwardPropsEmits-DFe9BlYF.js";function V(e,t,n){const o=n.originalEvent.target,r=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),o.dispatchEvent(r)}const ue="dismissableLayer.pointerDownOutside",ce="dismissableLayer.focusOutside";function X(e,t){const n=t.closest("[data-dismissable-layer]"),o=e.dataset.dismissableLayer===""?e:e.querySelector("[data-dismissable-layer]"),r=Array.from(e.ownerDocument.querySelectorAll("[data-dismissable-layer]"));return!!(n&&o===n||r.indexOf(o)<r.indexOf(n))}function le(e,t){var a;const n=((a=t==null?void 0:t.value)==null?void 0:a.ownerDocument)??(globalThis==null?void 0:globalThis.document),o=w(!1),r=w(()=>{});return P(l=>{if(!T)return;const d=async m=>{const c=m.target;if(t!=null&&t.value){if(X(t.value,c)){o.value=!1;return}if(m.target&&!o.value){let s=function(){V(ue,e,u)};const u={originalEvent:m};m.pointerType==="touch"?(n.removeEventListener("click",r.value),r.value=s,n.addEventListener("click",r.value,{once:!0})):s()}else n.removeEventListener("click",r.value);o.value=!1}},p=window.setTimeout(()=>{n.addEventListener("pointerdown",d)},0);l(()=>{window.clearTimeout(p),n.removeEventListener("pointerdown",d),n.removeEventListener("click",r.value)})}),{onPointerDownCapture:()=>o.value=!0}}function de(e,t){var r;const n=((r=t==null?void 0:t.value)==null?void 0:r.ownerDocument)??(globalThis==null?void 0:globalThis.document),o=w(!1);return P(a=>{if(!T)return;const l=async d=>{t!=null&&t.value&&(await C(),await C(),!(!t.value||X(t.value,d.target))&&d.target&&!o.value&&V(ce,e,{originalEvent:d}))};n.addEventListener("focusin",l),a(()=>n.removeEventListener("focusin",l))}),{onFocusCapture:()=>o.value=!0,onBlurCapture:()=>o.value=!1}}const h=K({layersRoot:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Be=U({__name:"DismissableLayer",props:{disableOutsidePointerEvents:{type:Boolean,default:!1},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","dismiss"],setup(e,{emit:t}){const n=e,o=t,{forwardRef:r,currentElement:a}=R(),l=O(()=>{var i;return((i=a.value)==null?void 0:i.ownerDocument)??globalThis.document}),d=O(()=>h.layersRoot),p=O(()=>a.value?Array.from(d.value).indexOf(a.value):-1),m=O(()=>h.layersWithOutsidePointerEventsDisabled.size>0),c=O(()=>{const i=Array.from(d.value),[v]=[...h.layersWithOutsidePointerEventsDisabled].slice(-1),f=i.indexOf(v);return p.value>=f}),s=le(async i=>{const v=[...h.branches].some(f=>f==null?void 0:f.contains(i.target));!c.value||v||(o("pointerDownOutside",i),o("interactOutside",i),await C(),i.defaultPrevented||o("dismiss"))},a),u=de(i=>{[...h.branches].some(f=>f==null?void 0:f.contains(i.target))||(o("focusOutside",i),o("interactOutside",i),i.defaultPrevented||o("dismiss"))},a);Z("Escape",i=>{p.value===d.value.size-1&&(o("escapeKeyDown",i),i.defaultPrevented||o("dismiss"))});let y;return P(i=>{a.value&&(n.disableOutsidePointerEvents&&(h.layersWithOutsidePointerEventsDisabled.size===0&&(y=l.value.body.style.pointerEvents,l.value.body.style.pointerEvents="none"),h.layersWithOutsidePointerEventsDisabled.add(a.value)),d.value.add(a.value),i(()=>{n.disableOutsidePointerEvents&&h.layersWithOutsidePointerEventsDisabled.size===1&&(l.value.body.style.pointerEvents=y)}))}),P(i=>{i(()=>{a.value&&(d.value.delete(a.value),h.layersWithOutsidePointerEventsDisabled.delete(a.value))})}),(i,v)=>(j(),H(S(q),{ref:S(r),"as-child":i.asChild,as:i.as,"data-dismissable-layer":"",style:re({pointerEvents:m.value?c.value?"auto":"none":void 0}),onFocusCapture:S(u).onFocusCapture,onBlurCapture:S(u).onBlurCapture,onPointerdownCapture:S(s).onPointerDownCapture},{default:$(()=>[z(i.$slots,"default")]),_:3},8,["as-child","as","style","onFocusCapture","onBlurCapture","onPointerdownCapture"]))}});function g(){let e=document.activeElement;if(e==null)return null;for(;e!=null&&e.shadowRoot!=null&&e.shadowRoot.activeElement!=null;)e=e.shadowRoot.activeElement;return e}function _e(e){return e?"open":"closed"}function Ie(e){const t=g();for(const n of e)if(n===t||(n.focus(),g()!==t))return}const A="focusScope.autoFocusOnMount",k="focusScope.autoFocusOnUnmount",W={bubbles:!1,cancelable:!0};function fe(e,{select:t=!1}={}){const n=g();for(const o of e)if(b(o,{select:t}),g()!==n)return!0}function ve(e){const t=G(e),n=x(t,e),o=x(t.reverse(),e);return[n,o]}function G(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:o=>{const r=o.tagName==="INPUT"&&o.type==="hidden";return o.disabled||o.hidden||r?NodeFilter.FILTER_SKIP:o.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function x(e,t){for(const n of e)if(!pe(n,{upTo:t}))return n}function pe(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function ye(e){return e instanceof HTMLInputElement&&"select"in e}function b(e,{select:t=!1}={}){if(e&&e.focus){const n=g();e.focus({preventScroll:!0}),e!==n&&ye(e)&&t&&e.select()}}const me=ne(()=>w([]));function he(){const e=me();return{add(t){const n=e.value[0];t!==n&&(n==null||n.pause()),e.value=M(e.value,t),e.value.unshift(t)},remove(t){var n;e.value=M(e.value,t),(n=e.value[0])==null||n.resume()}}}function M(e,t){const n=[...e],o=n.indexOf(t);return o!==-1&&n.splice(o,1),n}function Ee(e){return e.filter(t=>t.tagName!=="A")}const Ne=U({__name:"FocusScope",props:{loop:{type:Boolean,default:!1},trapped:{type:Boolean,default:!1},asChild:{type:Boolean},as:{}},emits:["mountAutoFocus","unmountAutoFocus"],setup(e,{emit:t}){const n=e,o=t,{currentRef:r,currentElement:a}=R(),l=w(null),d=he(),p=K({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}});P(c=>{if(!T)return;const s=a.value;if(!n.trapped)return;function u(f){if(p.paused||!s)return;const E=f.target;s.contains(E)?l.value=E:b(l.value,{select:!0})}function y(f){if(p.paused||!s)return;const E=f.relatedTarget;E!==null&&(s.contains(E)||b(l.value,{select:!0}))}function i(f){s.contains(l.value)||b(s)}document.addEventListener("focusin",u),document.addEventListener("focusout",y);const v=new MutationObserver(i);s&&v.observe(s,{childList:!0,subtree:!0}),c(()=>{document.removeEventListener("focusin",u),document.removeEventListener("focusout",y),v.disconnect()})}),P(async c=>{const s=a.value;if(await C(),!s)return;d.add(p);const u=g();if(!s.contains(u)){const i=new CustomEvent(A,W);s.addEventListener(A,v=>o("mountAutoFocus",v)),s.dispatchEvent(i),i.defaultPrevented||(fe(Ee(G(s)),{select:!0}),g()===u&&b(s))}c(()=>{s.removeEventListener(A,f=>o("mountAutoFocus",f));const i=new CustomEvent(k,W),v=f=>{o("unmountAutoFocus",f)};s.addEventListener(k,v),s.dispatchEvent(i),setTimeout(()=>{i.defaultPrevented||b(u??document.body,{select:!0}),s.removeEventListener(k,v),d.remove(p)},0)})});function m(c){if(!n.loop&&!n.trapped||p.paused)return;const s=c.key==="Tab"&&!c.altKey&&!c.ctrlKey&&!c.metaKey,u=g();if(s&&u){const y=c.currentTarget,[i,v]=ve(y);i&&v?!c.shiftKey&&u===v?(c.preventDefault(),n.loop&&b(i,{select:!0})):c.shiftKey&&u===i&&(c.preventDefault(),n.loop&&b(v,{select:!0})):u===y&&c.preventDefault()}}return(c,s)=>(j(),H(S(q),{ref_key:"currentRef",ref:r,tabindex:"-1","as-child":c.asChild,as:c.as,onKeydown:m},{default:$(()=>[z(c.$slots,"default")]),_:3},8,["as-child","as"]))}});var be=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},D=new WeakMap,L=new WeakMap,F={},B=0,J=function(e){return e&&(e.host||J(e.parentNode))},we=function(e,t){return t.map(function(n){if(e.contains(n))return n;var o=J(n);return o&&e.contains(o)?o:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},ge=function(e,t,n,o){var r=we(t,Array.isArray(e)?e:[e]);F[n]||(F[n]=new WeakMap);var a=F[n],l=[],d=new Set,p=new Set(r),m=function(s){!s||d.has(s)||(d.add(s),m(s.parentNode))};r.forEach(m);var c=function(s){!s||p.has(s)||Array.prototype.forEach.call(s.children,function(u){if(d.has(u))c(u);else try{var y=u.getAttribute(o),i=y!==null&&y!=="false",v=(D.get(u)||0)+1,f=(a.get(u)||0)+1;D.set(u,v),a.set(u,f),l.push(u),v===1&&i&&L.set(u,!0),f===1&&u.setAttribute(n,"true"),i||u.setAttribute(o,"true")}catch(E){console.error("aria-hidden: cannot operate on ",u,E)}})};return c(t),d.clear(),B++,function(){l.forEach(function(s){var u=D.get(s)-1,y=a.get(s)-1;D.set(s,u),a.set(s,y),u||(L.has(s)||s.removeAttribute(o),L.delete(s)),y||s.removeAttribute(n)}),B--,B||(D=new WeakMap,D=new WeakMap,L=new WeakMap,F={})}},Oe=function(e,t,n){n===void 0&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),r=be(e);return r?(o.push.apply(o,Array.from(r.querySelectorAll("[aria-live]"))),ge(o,r,n,"aria-hidden")):function(){return null}};function We(e){let t;Y(()=>ee(e),n=>{n?t=Oe(n):t&&t()}),ie(()=>{t&&t()})}function _(e){if(e===null||typeof e!="object")return!1;const t=Object.getPrototypeOf(e);return t!==null&&t!==Object.prototype&&Object.getPrototypeOf(t)!==null||Symbol.iterator in e?!1:Symbol.toStringTag in e?Object.prototype.toString.call(e)==="[object Module]":!0}function I(e,t,n=".",o){if(!_(t))return I(e,{},n,o);const r=Object.assign({},t);for(const a in e){if(a==="__proto__"||a==="constructor")continue;const l=e[a];l!=null&&(o&&o(r,a,l,n)||(Array.isArray(l)&&Array.isArray(r[a])?r[a]=[...l,...r[a]]:_(l)&&_(r[a])?r[a]=I(l,r[a],(n?`${n}.`:"")+a.toString(),o):r[a]=l))}return r}function De(e){return(...t)=>t.reduce((n,o)=>I(n,o,"",e),{})}const Se=De(),Pe=oe(()=>{const e=w(new Map),t=w(),n=O(()=>{for(const l of e.value.values())if(l)return!0;return!1}),o=ae({scrollBody:w(!0)});let r=null;const a=()=>{document.body.style.paddingRight="",document.body.style.marginRight="",document.body.style.pointerEvents="",document.body.style.removeProperty("--scrollbar-width"),document.body.style.overflow=t.value??"",N&&(r==null||r()),t.value=void 0};return Y(n,(l,d)=>{var s;if(!T)return;if(!l){d&&a();return}t.value===void 0&&(t.value=document.body.style.overflow);const p=window.innerWidth-document.documentElement.clientWidth,m={padding:p,margin:0},c=(s=o.scrollBody)!=null&&s.value?typeof o.scrollBody.value=="object"?Se({padding:o.scrollBody.value.padding===!0?p:o.scrollBody.value.padding,margin:o.scrollBody.value.margin===!0?p:o.scrollBody.value.margin},m):m:{padding:0,margin:0};p>0&&(document.body.style.paddingRight=typeof c.padding=="number"?`${c.padding}px`:String(c.padding),document.body.style.marginRight=typeof c.margin=="number"?`${c.margin}px`:String(c.margin),document.body.style.setProperty("--scrollbar-width",`${p}px`),document.body.style.overflow="hidden"),N&&(r=te(document,"touchmove",u=>Ce(u),{passive:!1})),C(()=>{document.body.style.pointerEvents="none",document.body.style.overflow="hidden"})},{immediate:!0,flush:"sync"}),e});function xe(e){const t=Math.random().toString(36).substring(2,7),n=Pe();n.value.set(t,e??!1);const o=O({get:()=>n.value.get(t)??!1,set:r=>n.value.set(t,r)});return se(()=>{n.value.delete(t)}),o}function Q(e){const t=window.getComputedStyle(e);if(t.overflowX==="scroll"||t.overflowY==="scroll"||t.overflowX==="auto"&&e.clientWidth<e.scrollWidth||t.overflowY==="auto"&&e.clientHeight<e.scrollHeight)return!0;{const n=e.parentNode;return!(n instanceof Element)||n.tagName==="BODY"?!1:Q(n)}}function Ce(e){const t=e||window.event,n=t.target;return n instanceof Element&&Q(n)?!1:t.touches.length>1?!0:(t.preventDefault&&t.cancelable&&t.preventDefault(),!1)}export{Ne as _,We as a,Be as b,_e as c,Ie as f,g,V as h,xe as u};
