import{J as we,r as o,z as ke,o as le,H as Ie,d as l,e as n,f as _,i as t,g as D,u as S,m as _e,n as f,A as q,I as T,y as Se,F as z,l as M,v as ie,t as J,x as C,P as N,p as re,q as Te,O,a as j,C as y}from"./vendor-BhKTHoN5.js";import{C as de}from"./ChatInput-DdW2-31K.js";import{_ as Me}from"./_plugin-vue_export-helper-DlAUqK2U.js";const Ce={class:"w-full lg:w-1/2 bg-gray-50 flex items-center justify-center px-8 lg:px-16 py-8 lg:py-16 min-h-screen lg:min-h-0"},Ae={class:"max-w-lg w-full"},De={class:"space-y-4"},je={key:0,class:"space-y-4"},Le={class:"space-y-3"},Ve=["disabled"],$e={key:0,class:"text-red-600 text-sm mt-2"},Ee={class:"text-xs text-gray-500 mt-3"},Pe={key:1,class:"space-y-4"},We={key:2,class:"space-y-4"},He={class:"text-lg text-gray-500"},Re={class:"max-w-lg mx-auto w-full"},qe={key:0,class:"space-y-6 mb-10"},ze={key:1,class:"space-y-6 mb-6 chat-messages-container"},Ne={key:0,class:"flex items-start space-x-3 mb-4"},Ue={class:"flex-1"},Fe={key:0,class:"text-gray-900 leading-relaxed text-sm formatted-message"},Be=["innerHTML"],Je=["innerHTML"],Oe={key:2,class:"inline-flex items-center ml-1"},Ye={key:1,class:"flex justify-end mb-4"},Ge={class:"bg-medroid-orange text-white px-4 py-3 rounded-2xl rounded-br-md shadow-lg max-w-xs lg:max-w-md text-sm"},Ke={key:0,class:"flex items-start space-x-3 mb-4"},Ze={key:2},Qe={key:0,class:"fixed inset-0 bg-white bg-opacity-20 backdrop-blur-sm flex items-center justify-center z-50"},Xe={class:"bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl"},et={class:"space-y-4"},tt={class:"flex space-x-4"},at={class:"flex items-center"},st={class:"flex items-center"},ot={class:"flex items-center"},nt={class:"flex space-x-3 mt-8"},lt={key:1,class:"fixed inset-0 bg-white bg-opacity-20 backdrop-blur-sm flex items-center justify-center z-50"},it={class:"bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl"},rt={key:2,class:"fixed bottom-0 left-0 right-0 lg:left-1/2 lg:right-0 bg-white border-t border-gray-100 z-20"},dt={class:"px-8 lg:px-16 py-4"},ut={class:"max-w-lg mx-auto w-full"},ct=we({__name:"Welcome",setup(mt){var oe;const Y=o([]),L=o(0),d=o(null),V=o(!1),ue=(oe=ke().props.auth)==null?void 0:oe.user,$=o(!1),ce=async()=>{try{const s=await j.get("/api/waitlist/status");$.value=s.data.enabled||!1}catch(s){console.error("Error fetching waitlist status:",s),$.value=!1}},u=o(!1),x=o(!1),r=o(""),h=o(null),m=o(""),v=o([]),E=o(!1),P=o(!1),W=o(""),g=o(""),G=o(!1),U=o(0),c=o(null),K=o(null),w=o(null),k=o(""),H=o(""),R=o(!1),F=o(!1),A=o(""),Z=[{id:1,type:"ai",content:"Hi! I'm your AI Doctor. How are you feeling today?",timestamp:new Date},{id:2,type:"user",content:"I've been having trouble sleeping lately. Any suggestions?",timestamp:new Date},{id:3,type:"ai",content:"Sleep issues can be challenging. Try maintaining a consistent bedtime routine and avoiding screens 1 hour before sleep.",timestamp:new Date},{id:4,type:"user",content:"That makes sense. I'll try that tonight. Thank you!",timestamp:new Date},{id:5,type:"ai",content:"You're welcome! Also consider keeping your bedroom cool and dark. Would you like tips for managing stress?",timestamp:new Date},{id:6,type:"user",content:"Yes, that would be helpful. I think stress might be affecting my sleep.",timestamp:new Date},{id:7,type:"ai",content:"Try deep breathing exercises before bed. Inhale for 4 counts, hold for 7, exhale for 8. This activates your parasympathetic nervous system.",timestamp:new Date},{id:8,type:"user",content:"I'll definitely try that breathing technique. Thank you for the personalized advice!",timestamp:new Date}],B=()=>{Y.value=[],L.value=0,me()},me=()=>{d.value&&clearInterval(d.value),d.value=setInterval(()=>{if(L.value<Z.length){const s={...Z[L.value]};s.timestamp=new Date,Y.value.push(s),L.value++}else setTimeout(()=>{V.value&&B()},4e3),d.value&&(clearInterval(d.value),d.value=null)},1500)};le(()=>{V.value=!0,setTimeout(()=>{B()},1e3)});const Q=()=>"anon_"+Math.random().toString(36).substring(2,11)+"_"+Date.now(),X=async()=>{var s;if(r.value.trim())try{x.value=!0,console.log("Starting anonymous chat..."),m.value||(m.value=Q()),console.log("Anonymous ID:",m.value),console.log("Initial message:",r.value.trim());const e=await j.post("/api/anonymous/chat/start");console.log("Start API Response:",e.data),h.value=e.data.conversation_id,m.value=e.data.anonymous_id,v.value.push({id:Date.now().toString(),type:"user",content:r.value.trim(),timestamp:new Date});const a=r.value.trim();r.value="",await fe();const i=await j.post("/api/anonymous/chat/message",{conversation_id:String(h.value),anonymous_id:m.value,message:a,gender:g.value||null,age:W.value||null,request_full_response:!1,generate_title:!0});if(console.log("Message API Response:",i.data),i.data.message){const b=(Date.now()+1).toString();v.value.push({id:b,type:"ai",content:i.data.message,timestamp:new Date,isStreaming:!1,displayedContent:""}),await y(),p(),te(b,i.data.message)}U.value++,U.value===1&&!G.value&&setTimeout(()=>{E.value=!0},1e3),i.data.requires_auth&&setTimeout(()=>{P.value=!0},1e3),await y(),p(),setTimeout(()=>{c.value&&c.value.focus&&c.value.focus()},100)}catch(e){console.error("Error starting chat:",e),console.error("Error details:",(s=e.response)==null?void 0:s.data),alert("Failed to start chat. Please try again.")}finally{x.value=!1}},ee=async()=>{var s;if(!(!r.value.trim()||!h.value))try{x.value=!0,await y(),p(),console.log("Sending message..."),v.value.push({id:Date.now().toString(),type:"user",content:r.value.trim(),timestamp:new Date});const e=r.value.trim();r.value="",console.log("Message to send:",e),console.log("Conversation ID:",h.value);const a=await j.post("/api/anonymous/chat/message",{conversation_id:String(h.value),message:e,anonymous_id:m.value,gender:g.value||null,age:W.value||null,request_full_response:!1,generate_title:!0});if(console.log("Message API Response:",a.data),a.data.message){const i=(Date.now()+1).toString();v.value.push({id:i,type:"ai",content:a.data.message,timestamp:new Date,isStreaming:!1,displayedContent:""}),await y(),p(),te(i,a.data.message)}U.value++,a.data.requires_auth&&setTimeout(()=>{P.value=!0},1e3),await y(),p(),setTimeout(()=>{c.value&&c.value.focus&&c.value.focus()},100)}catch(e){console.error("Error sending message:",e),console.error("Error details:",(s=e.response)==null?void 0:s.data),alert("Failed to send message. Please try again.")}finally{x.value=!1}},fe=async()=>{u.value=!0,d.value&&(clearInterval(d.value),d.value=null),await y(),setTimeout(()=>{c.value&&c.value.focus&&c.value.focus()},300),p()},p=()=>{u.value&&window.scrollTo({top:document.documentElement.scrollHeight,behavior:"smooth"})},te=(s,e)=>{const a=v.value.find(I=>I.id===s);if(!a)return;a.isStreaming=!0,a.displayedContent="",a.streamingLines=[],K.value=s;const i=e.split(`
`).filter(I=>I.trim()!=="");let b=0;const ne=()=>{if(b<i.length){const I=i[b];a.streamingLines.push({content:I,id:`line-${b}`,fadeIn:!0}),a.displayedContent=a.streamingLines.map(be=>be.content).join(`
`),b++,y(()=>p());const he=I.length>100?800:500;w.value=setTimeout(ne,he)}else xe()},xe=()=>{a.isStreaming=!1,a.displayedContent=e,a.streamingLines=[],K.value=null,w.value&&(clearTimeout(w.value),w.value=null),y(()=>p())};setTimeout(()=>{ne()},200)},ve=()=>{G.value=!0,E.value=!1},ae=s=>{let e=s;return e=e.replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>"),e=e.replace(/^(\d+\.\s+)(.+)$/gm,'<div class="list-item"><span class="list-number">$1</span>$2</div>'),e=e.replace(/^-\s+(.+)$/gm,'<div class="bullet-item">$1</div>'),e=e.replace(/^##\s+(.+)$/gm,'<h2 class="main-section-header">$1</h2>'),e=e.replace(/^###\s+(.+)$/gm,'<h3 class="section-header">$1</h3>'),e=e.replace(/^####\s+(.+)$/gm,'<h4 class="subsection-header">$1</h4>'),e=e.replace(/\*\*([A-Z\s]+:)\*\*/g,'<h4 class="subsection-header">$1</h4>'),e=e.replace(/\n/g,"<br>"),e},ge=()=>{localStorage.setItem("anonymous_conversation",JSON.stringify({conversation_id:h.value,anonymous_id:m.value,messages:v.value})),window.location.href="/login"},pe=()=>{localStorage.setItem("anonymous_conversation",JSON.stringify({conversation_id:h.value,anonymous_id:m.value,messages:v.value})),window.location.href="/register"},ye=async()=>{var s,e;if(k.value.trim())try{R.value=!0,A.value="";const a=await j.post("/api/waitlist/join",{email:k.value.trim(),name:H.value.trim()||null});a.data.success?(F.value=!0,k.value="",H.value=""):A.value=a.data.message||"Failed to join waitlist"}catch(a){console.error("Error joining waitlist:",a),A.value=((e=(s=a.response)==null?void 0:s.data)==null?void 0:e.message)||"Failed to join waitlist. Please try again."}finally{R.value=!1}},se=s=>{s.key==="Enter"&&!s.shiftKey&&(s.preventDefault(),u.value?ee():X())};return le(()=>{V.value=!0,m.value=Q(),ce(),setTimeout(()=>{B()},1e3)}),Ie(()=>{V.value=!1,d.value&&clearInterval(d.value),w.value&&clearTimeout(w.value)}),(s,e)=>(n(),l(z,null,[_(S(_e),{title:"Medroid AI Doctor"},{default:D(()=>e[10]||(e[10]=[t("link",{rel:"preconnect",href:"https://rsms.me/"},null,-1),t("link",{rel:"stylesheet",href:"https://rsms.me/inter/inter.css"},null,-1),t("link",{href:"https://fonts.googleapis.com/css2?family=Material+Icons",rel:"stylesheet"},null,-1)])),_:1}),t("div",{class:q(["min-h-screen bg-white font-sans overflow-x-hidden",{"chat-mode":u.value}])},[t("header",{class:q(["flex justify-between items-center px-8 py-6",{"fixed top-0 left-0 right-0 z-10 bg-white border-b border-gray-200":u.value}])},e[11]||(e[11]=[t("div",{class:"flex items-center space-x-2"},[t("div",{class:"w-8 h-8 rounded-lg flex items-center justify-center"},[t("img",{src:"/medroid_logo.png",alt:"Medroid Logo",class:"w-8 h-8 object-contain"})]),t("span",{class:"text-xl font-semibold text-gray-900"},"Medroid")],-1)]),2),t("div",{class:q(["flex flex-col lg:flex-row min-h-screen",{"pt-20":u.value}])},[t("div",Ce,[t("div",Ae,[e[20]||(e[20]=T('<div class="mb-12" data-v-f3917cf9><h1 class="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6" data-v-f3917cf9> Your free<br data-v-f3917cf9> personal<br data-v-f3917cf9><span class="text-teal-400" data-v-f3917cf9>AI Doctor</span> awaits<br data-v-f3917cf9> you. </h1><p class="text-gray-600 text-lg leading-relaxed" data-v-f3917cf9> Fast, free, and private medical consultations powered by AI. </p></div><div class="mb-12 space-y-5" data-v-f3917cf9><div class="flex items-center space-x-4" data-v-f3917cf9><div class="w-1 h-7 bg-teal-400 rounded-full" data-v-f3917cf9></div><span class="text-gray-800 text-lg font-medium" data-v-f3917cf9>100% free and private</span></div><div class="flex items-center space-x-4" data-v-f3917cf9><div class="w-1 h-7 bg-orange-400 rounded-full" data-v-f3917cf9></div><span class="text-gray-800 text-lg font-medium" data-v-f3917cf9>Instant medical advice</span></div><div class="flex items-center space-x-4" data-v-f3917cf9><div class="w-1 h-7 bg-orange-400 rounded-full" data-v-f3917cf9></div><span class="text-gray-800 text-lg font-medium" data-v-f3917cf9>Book real doctor appointments</span></div></div>',2)),t("div",De,[S(ue)?(n(),Se(S(N),{key:1,href:s.route("dashboard"),class:"w-full max-w-xs bg-gray-900 text-white font-semibold text-lg py-4 px-8 rounded-xl hover:bg-gray-800 transition-colors duration-200 text-center block"},{default:D(()=>e[19]||(e[19]=[C(" Go to Dashboard ")])),_:1},8,["href"])):(n(),l(z,{key:0},[$.value&&!F.value?(n(),l("div",je,[e[14]||(e[14]=t("div",{class:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4"},[t("h3",{class:"text-lg font-semibold text-blue-900 mb-2"},"🎉 Join the Waitlist"),t("p",{class:"text-blue-700 text-sm"}," Medroid is currently in invitation-only mode. Join our waitlist and we'll send you an exclusive invitation to experience the future of healthcare! ")],-1)),t("div",Le,[M(t("input",{"onUpdate:modelValue":e[0]||(e[0]=a=>H.value=a),type:"text",placeholder:"Your name (optional)",class:"w-full max-w-xs px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-400 focus:border-teal-400"},null,512),[[ie,H.value]]),M(t("input",{"onUpdate:modelValue":e[1]||(e[1]=a=>k.value=a),type:"email",placeholder:"Enter your email address",required:"",class:"w-full max-w-xs px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-400 focus:border-teal-400"},null,512),[[ie,k.value]]),t("button",{onClick:ye,disabled:R.value||!k.value.trim(),class:"w-full max-w-xs bg-teal-500 hover:bg-teal-600 disabled:bg-gray-400 text-white font-semibold text-lg py-3 px-8 rounded-xl transition-colors duration-200"},J(R.value?"Joining...":"Join Waitlist"),9,Ve)]),A.value?(n(),l("div",$e,J(A.value),1)):f("",!0),t("p",Ee,[e[13]||(e[13]=C(" Already have an invitation? ")),_(S(N),{href:s.route("login"),class:"text-teal-500 hover:text-teal-600 font-medium"},{default:D(()=>e[12]||(e[12]=[C(" Sign in here ")])),_:1},8,["href"])])])):$.value&&F.value?(n(),l("div",Pe,e[15]||(e[15]=[T('<div class="bg-green-50 border border-green-200 rounded-lg p-6 text-center" data-v-f3917cf9><div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4" data-v-f3917cf9><svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-f3917cf9><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" data-v-f3917cf9></path></svg></div><h3 class="text-lg font-semibold text-green-900 mb-2" data-v-f3917cf9>🎉 You&#39;re on the list!</h3><p class="text-green-700 text-sm" data-v-f3917cf9> Thank you for joining our waitlist! We&#39;ll send you an exclusive invitation soon. Keep an eye on your inbox! </p></div>',1)]))):(n(),l("div",We,[_(S(N),{href:s.route("login"),class:"w-full max-w-xs bg-gray-900 text-white font-semibold text-lg py-4 px-8 rounded-xl hover:bg-gray-800 transition-colors duration-200 text-center block"},{default:D(()=>e[16]||(e[16]=[C(" Sign In ")])),_:1},8,["href"]),t("p",He,[e[18]||(e[18]=C(" Don't have an account? ")),_(S(N),{href:s.route("register"),class:"text-teal-400 hover:text-teal-500 font-medium ml-1"},{default:D(()=>e[17]||(e[17]=[C(" Sign up here ")])),_:1},8,["href"])])]))],64))])])]),t("div",{class:q(["w-full lg:w-1/2 bg-white flex flex-col px-8 lg:px-16 justify-center py-8 lg:py-16 min-h-screen lg:min-h-0",{"pb-32 lg:pb-40":u.value}])},[t("div",Re,[e[25]||(e[25]=T('<div class="mb-8" data-v-f3917cf9><div class="flex items-start space-x-3 mb-8" data-v-f3917cf9><div class="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0" data-v-f3917cf9><img src="/medroid_logo.png" alt="Medroid AI" class="w-8 h-8 object-contain rounded-full" data-v-f3917cf9></div><div class="flex-1" data-v-f3917cf9><p class="text-gray-900 font-medium" data-v-f3917cf9>Hi, I&#39;m Medroid, your personal AI doctor.</p></div></div></div>',1)),u.value?f("",!0):(n(),l("div",qe,e[21]||(e[21]=[t("div",{class:"bg-white rounded-3xl rounded-tl-lg p-5 shadow-sm border border-gray-100"},[t("p",{class:"text-gray-900 leading-relaxed"}," As an AI doctor, I'm fast and free. I've already conducted 1,000,000+ consultations! ")],-1),t("div",{class:"bg-white rounded-3xl rounded-tl-lg p-5 shadow-sm border border-gray-100"},[t("p",{class:"text-gray-900 leading-relaxed"}," When you're done, you can have a video consultation with a world class doctor, if you want, for just £55 ($75). ")],-1)]))),u.value?(n(),l("div",ze,[(n(!0),l(z,null,re(v.value,a=>(n(),l("div",{key:a.id,class:"animate-fade-in-up mb-6"},[a.type==="ai"?(n(),l("div",Ne,[e[23]||(e[23]=t("div",{class:"w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0"},[t("img",{src:"/medroid_logo.png",alt:"Medroid AI",class:"w-8 h-8 object-contain rounded-full"})],-1)),t("div",Ue,[a.isStreaming&&a.streamingLines?(n(),l("div",Fe,[(n(!0),l(z,null,re(a.streamingLines,i=>(n(),l("div",{key:i.id,class:"animate-fade-in-line",innerHTML:ae(i.content)},null,8,Be))),128))])):(n(),l("div",{key:1,class:"text-gray-900 leading-relaxed text-sm formatted-message",innerHTML:ae(a.content)},null,8,Je)),a.isStreaming?(n(),l("span",Oe,e[22]||(e[22]=[T('<span class="w-0.5 h-4 bg-gradient-to-t from-medroid-orange to-orange-400 animate-futuristic-pulse rounded-full shadow-sm" data-v-f3917cf9></span><span class="ml-1 flex space-x-0.5" data-v-f3917cf9><span class="w-1 h-1 bg-medroid-orange rounded-full animate-modern-bounce shadow-sm" style="animation-delay:0ms;" data-v-f3917cf9></span><span class="w-1 h-1 bg-medroid-orange rounded-full animate-modern-bounce shadow-sm" style="animation-delay:150ms;" data-v-f3917cf9></span><span class="w-1 h-1 bg-medroid-orange rounded-full animate-modern-bounce shadow-sm" style="animation-delay:300ms;" data-v-f3917cf9></span></span>',2)]))):f("",!0)])])):(n(),l("div",Ye,[t("div",Ge,J(a.content),1)]))]))),128)),x.value?(n(),l("div",Ke,e[24]||(e[24]=[T('<div class="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0" data-v-f3917cf9><img src="/medroid_logo.png" alt="Medroid AI" class="w-8 h-8 object-contain rounded-full" data-v-f3917cf9></div><div class="flex-1" data-v-f3917cf9><div class="flex items-center space-x-1" data-v-f3917cf9><span class="text-sm text-gray-600" data-v-f3917cf9>AI is thinking</span><div class="flex space-x-1 ml-2" data-v-f3917cf9><div class="w-1.5 h-1.5 bg-medroid-orange rounded-full animate-modern-bounce" data-v-f3917cf9></div><div class="w-1.5 h-1.5 bg-medroid-orange rounded-full animate-modern-bounce" style="animation-delay:0.15s;" data-v-f3917cf9></div><div class="w-1.5 h-1.5 bg-medroid-orange rounded-full animate-modern-bounce" style="animation-delay:0.3s;" data-v-f3917cf9></div></div></div></div>',2)]))):f("",!0)])):f("",!0),u.value?f("",!0):(n(),l("div",Ze,[_(de,{ref_key:"chatInputRef",ref:c,modelValue:r.value,"onUpdate:modelValue":e[2]||(e[2]=a=>r.value=a),placeholder:"Type your health question...","is-loading":x.value,onSend:X,onKeydown:se},null,8,["modelValue","is-loading"])]))])],2)],2),E.value?(n(),l("div",Qe,[t("div",Xe,[e[32]||(e[32]=t("h3",{class:"text-2xl font-bold text-medroid-navy mb-2"},"Help us personalize your care",-1)),e[33]||(e[33]=t("p",{class:"text-medroid-slate mb-6"},"This information helps us provide better health recommendations.",-1)),t("div",et,[t("div",null,[e[27]||(e[27]=t("label",{class:"block text-sm font-medium text-medroid-navy mb-2"},"Age (Optional)",-1)),M(t("select",{"onUpdate:modelValue":e[3]||(e[3]=a=>W.value=a),class:"w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange bg-white"},e[26]||(e[26]=[T('<option value="" data-v-f3917cf9>Select age range</option><option value="18-25" data-v-f3917cf9>18-25</option><option value="26-35" data-v-f3917cf9>26-35</option><option value="36-45" data-v-f3917cf9>36-45</option><option value="46-60" data-v-f3917cf9>46-60</option><option value="61-75" data-v-f3917cf9>61-75</option><option value="75+" data-v-f3917cf9>75+</option>',7)]),512),[[Te,W.value]])]),t("div",null,[e[31]||(e[31]=t("label",{class:"block text-sm font-medium text-medroid-navy mb-3"},"Gender (Optional)",-1)),t("div",tt,[t("label",at,[M(t("input",{"onUpdate:modelValue":e[4]||(e[4]=a=>g.value=a),type:"radio",value:"male",class:"w-4 h-4 text-medroid-orange border-medroid-border focus:ring-medroid-orange focus:ring-2"},null,512),[[O,g.value]]),e[28]||(e[28]=t("span",{class:"ml-2 text-sm text-medroid-navy"},"Male",-1))]),t("label",st,[M(t("input",{"onUpdate:modelValue":e[5]||(e[5]=a=>g.value=a),type:"radio",value:"female",class:"w-4 h-4 text-medroid-orange border-medroid-border focus:ring-medroid-orange focus:ring-2"},null,512),[[O,g.value]]),e[29]||(e[29]=t("span",{class:"ml-2 text-sm text-medroid-navy"},"Female",-1))]),t("label",ot,[M(t("input",{"onUpdate:modelValue":e[6]||(e[6]=a=>g.value=a),type:"radio",value:"other",class:"w-4 h-4 text-medroid-orange border-medroid-border focus:ring-medroid-orange focus:ring-2"},null,512),[[O,g.value]]),e[30]||(e[30]=t("span",{class:"ml-2 text-sm text-medroid-navy"},"Other",-1))])])])]),t("div",nt,[t("button",{onClick:ve,class:"flex-1 bg-medroid-orange hover:bg-medroid-orange/90 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200"}," Continue "),t("button",{onClick:e[7]||(e[7]=a=>E.value=!1),class:"px-6 py-3 text-medroid-slate hover:text-medroid-navy transition-colors duration-200"}," Skip ")])])])):f("",!0),P.value?(n(),l("div",lt,[t("div",it,[e[34]||(e[34]=t("h3",{class:"text-2xl font-bold text-medroid-navy mb-2"},"Continue with an account",-1)),e[35]||(e[35]=t("p",{class:"text-medroid-slate mb-6"},"To book an appointment, please sign in or create an account. Your chat will continue after login.",-1)),t("div",{class:"space-y-3"},[t("button",{onClick:ge,class:"w-full bg-medroid-orange hover:bg-medroid-orange/90 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200"}," Sign In "),t("button",{onClick:pe,class:"w-full border border-medroid-border text-medroid-navy hover:bg-medroid-sage font-medium py-3 px-4 rounded-lg transition-colors duration-200"}," Create Account ")]),t("button",{onClick:e[8]||(e[8]=a=>P.value=!1),class:"w-full mt-4 text-medroid-slate hover:text-medroid-navy transition-colors duration-200"}," Continue without account ")])])):f("",!0),u.value?(n(),l("div",rt,[t("div",dt,[t("div",ut,[_(de,{ref_key:"chatInputRef",ref:c,modelValue:r.value,"onUpdate:modelValue":e[9]||(e[9]=a=>r.value=a),placeholder:"Type your health question...","is-loading":x.value,"show-version":!0,onSend:ee,onKeydown:se},null,8,["modelValue","is-loading"])])])])):f("",!0)],2)],64))}}),yt=Me(ct,[["__scopeId","data-v-f3917cf9"]]);export{yt as default};
