import{r as x,B as F,c as Ne,w as Vt,o as Bs,C as je,H as Vs,Q as Et,d as r,e as n,f as ce,u as Es,m as Us,g as Is,n as i,i as e,t as d,l as W,v as re,F as z,p as P,A as E,N as Ut,x as B,j as ue,R as Ts,S as It}from"./vendor-BhKTHoN5.js";import{_ as Rs}from"./AppLayout.vue_vue_type_script_setup_true_lang-DwGvFXKf.js";import{_ as Fs}from"./ComingSoon-Bg3W8jN1.js";import{_ as Ds}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const Hs={key:0},Ns={key:2,class:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50"},Os={class:"bg-white border-b border-gray-200 sticky top-0 z-40"},Ws={class:"max-w-6xl mx-auto px-3 sm:px-4 lg:px-8"},qs={class:"flex items-center justify-between py-3 sm:py-4"},Gs={class:"flex items-center space-x-2 sm:space-x-3"},Ys={key:0,class:"absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center"},Ks={class:"max-w-6xl mx-auto px-2 sm:px-4 lg:px-8 py-4 sm:py-6"},Js={class:"flex gap-3 xl:gap-6"},Qs={class:"w-56 xl:w-72 flex-shrink-0 hidden lg:block"},Xs={class:"fixed top-28 w-56 xl:w-72 h-[calc(100vh-7rem)] overflow-y-auto pr-4"},Zs={class:"bg-white rounded-2xl shadow-sm border border-gray-100 p-4 mb-6"},eo={class:"text-center"},to=["src"],so={class:"bg-white rounded-2xl shadow-sm border border-gray-100 p-4"},oo={class:"space-y-3"},ro={class:"relative"},no={key:0,class:"space-y-2"},lo={class:"flex flex-wrap gap-1"},ao=["onClick"],io={key:1,class:"text-center py-2"},co={class:"flex-1 max-w-2xl mx-auto lg:mx-0"},uo={key:0,class:"bg-white rounded-2xl shadow-sm border border-gray-100 p-4 mb-6"},go={class:"flex items-center justify-between"},mo={class:"text-lg font-semibold text-gray-900"},po={class:"text-sm text-gray-500"},ho={key:1,class:"bg-white rounded-2xl shadow-sm border border-gray-100 p-8 mb-6 text-center"},vo={class:"bg-white rounded-2xl shadow-sm border border-gray-100 p-4 mb-6"},fo={class:"flex items-center justify-between mb-3"},xo={class:"flex space-x-3 overflow-x-auto pb-2"},yo={class:"flex-shrink-0"},bo=["onClick"],wo={class:"relative"},ko={key:0,class:"w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 via-pink-500 to-orange-400 p-0.5"},_o={class:"w-full h-full rounded-full bg-white p-0.5"},Co=["src","alt"],Mo={key:1,class:"w-12 h-12 rounded-full bg-gradient-to-br from-pink-400 to-purple-500 p-0.5"},jo={class:"w-full h-full rounded-full bg-white p-0.5"},$o=["src","alt"],So={key:2,class:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center"},zo={key:3,class:"absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full border-2 border-white"},Lo={key:4,class:"absolute -bottom-1 -right-1 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full p-1"},Po={class:"text-xs text-gray-700 text-center mt-1 max-w-12 truncate"},Ao={key:0,class:"flex-1 text-center py-4"},Bo={key:2,class:"bg-white rounded-2xl shadow-sm border border-gray-100 p-4 mb-6"},Vo={class:"flex items-center justify-between mb-3"},Eo={key:0,class:"flex items-center space-x-2"},Uo={key:0,class:"text-center py-6"},Io={class:"w-16 h-16 bg-gradient-to-br from-pink-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-4"},To={key:0,class:"w-8 h-8 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ro={key:1,class:"w-8 h-8 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Fo={key:2,class:"w-8 h-8 text-pink-500 animate-spin",fill:"none",viewBox:"0 0 24 24"},Do={class:"text-lg font-medium text-gray-900 mb-2"},Ho={class:"text-sm text-gray-600 mb-4"},No={key:0,class:"w-full max-w-md mx-auto mb-4"},Oo={class:"flex justify-between text-xs text-gray-500 mb-1"},Wo={class:"w-full bg-gray-200 rounded-full h-2"},qo={key:1,class:"text-sm text-green-600 font-medium"},Go={key:1,class:"text-center py-6"},Yo=["disabled"],Ko={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",fill:"none",viewBox:"0 0 24 24"},Jo={key:1,class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 24 24"},Qo={key:2,class:"space-y-4"},Xo={class:"flex items-center justify-between p-3 bg-gray-50 rounded-lg"},Zo={class:"flex items-center space-x-3"},er={class:"font-medium text-gray-900"},tr={class:"text-sm text-gray-500"},sr={class:"flex items-center space-x-2"},or=["disabled"],rr={key:0,class:"animate-spin -ml-1 mr-1 h-3 w-3 text-white",fill:"none",viewBox:"0 0 24 24"},nr=["disabled"],lr={key:0,class:"text-xs text-gray-500 text-center"},ar={class:"space-y-6"},ir={key:0,class:"text-center py-12"},dr={key:1,class:"text-center py-16"},cr={key:2},ur={class:"p-4 sm:p-6 pb-3 sm:pb-4"},gr={class:"flex items-center justify-between mb-3 sm:mb-4"},mr={class:"flex items-center space-x-3"},pr=["onClick"],hr=["src","alt"],vr={class:"flex-1 min-w-0"},fr={class:"flex items-center space-x-2"},xr=["onClick"],yr={key:0,class:"inline-flex items-center justify-center w-6 h-6 rounded-full bg-gradient-to-br from-purple-500 to-pink-500",title:"Instagram Post"},br={class:"text-xs sm:text-sm text-gray-500 mt-1"},wr={key:0,class:"ml-1"},kr={class:"relative post-menu-container"},_r=["onClick"],Cr={key:0,class:"absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-10"},Mr=["onClick"],jr=["onClick"],$r={key:0,class:"px-6 pb-4"},Sr={class:"relative rounded-xl overflow-hidden bg-gray-100"},zr={key:0,class:"relative"},Lr=["data-post-id","src","poster","onClick","onLoadedmetadata","onPlay","onPause"],Pr=["onClick"],Ar={key:0,class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},Br={key:1,class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},Vr=["src","alt"],Er={key:2,class:"absolute top-3 right-3"},Ur=["href"],Ir={class:"px-4 sm:px-6 py-3 border-t border-gray-50"},Tr={class:"flex items-center justify-between"},Rr={class:"flex items-center space-x-6"},Fr=["onClick"],Dr=["fill"],Hr=["onClick"],Nr=["onClick"],Or=["onClick"],Wr=["fill"],qr={key:1,class:"px-4 sm:px-6 pb-3 sm:pb-4"},Gr={class:"text-gray-800 leading-relaxed text-sm sm:text-base"},Yr={key:0,class:"line-clamp-3",style:{display:"-webkit-box","-webkit-line-clamp":"3","-webkit-box-orient":"vertical",overflow:"hidden"}},Kr={key:1,class:"whitespace-pre-wrap"},Jr=["onClick"],Qr={key:0,class:"flex flex-wrap gap-2 mt-3"},Xr=["onClick"],Zr=["onClick"],en={key:2,class:"border-t border-gray-50 bg-gray-50"},tn={class:"p-4 border-b border-gray-100"},sn={class:"flex space-x-3"},on={class:"w-8 h-8 rounded-full overflow-hidden border border-gray-200 flex-shrink-0"},rn=["src","alt"],nn={class:"flex-1"},ln=["onKeydown"],an={class:"flex justify-end mt-2"},dn=["onClick","disabled"],cn={class:"p-4 space-y-4 max-h-96 overflow-y-auto"},un={class:"flex space-x-3"},gn={class:"w-8 h-8 rounded-full overflow-hidden border border-gray-200 flex-shrink-0"},mn=["src","alt"],pn={class:"flex-1"},hn={class:"bg-white rounded-lg p-3"},vn={class:"flex items-center justify-between mb-1"},fn={class:"text-sm font-medium text-gray-900"},xn={class:"flex items-center space-x-2"},yn={class:"text-xs text-gray-500"},bn={key:0,class:"relative comment-menu-container"},wn=["onClick"],kn={key:0,class:"absolute right-0 top-full mt-1 w-32 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-10"},_n=["onClick"],Cn={class:"text-sm text-gray-800 mb-2"},Mn={class:"flex items-center space-x-4 text-xs"},jn=["onClick"],$n=["fill"],Sn=["onClick"],zn={key:0,class:"mt-3 ml-4"},Ln={class:"flex space-x-2"},Pn=["onUpdate:modelValue","onKeydown"],An={class:"flex flex-col space-y-1"},Bn=["onClick","disabled"],Vn=["onClick"],En={key:1,class:"mt-3 ml-4 space-y-2"},Un={class:"w-6 h-6 rounded-full overflow-hidden border border-gray-200 flex-shrink-0"},In=["src","alt"],Tn={class:"flex-1"},Rn={class:"bg-gray-50 rounded-lg p-2"},Fn={class:"flex items-center justify-between mb-1"},Dn={class:"text-xs font-medium text-gray-900"},Hn={class:"flex items-center space-x-2"},Nn={class:"text-xs text-gray-500"},On={key:0,class:"relative comment-menu-container"},Wn=["onClick"],qn={key:0,class:"absolute right-0 top-full mt-1 w-32 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-10"},Gn=["onClick"],Yn={class:"text-xs text-gray-800 mb-1"},Kn={class:"flex items-center space-x-3 text-xs"},Jn=["onClick"],Qn=["fill"],Xn={key:0,class:"text-center py-6"},Zn={key:0,class:"flex items-center justify-center"},el={key:1,class:"text-gray-400 text-sm"},tl={key:4,class:"text-center py-8"},sl={class:"w-48 xl:w-56 flex-shrink-0 hidden lg:block"},ol={class:"sticky top-28 space-y-4"},rl={class:"flex items-center space-x-3"},nl={class:"text-sm font-medium text-gray-900"},ll={key:0,class:"bg-white rounded-2xl shadow-sm border border-gray-100 p-4"},al={class:"text-xs text-gray-600 mb-3"},il={class:"space-y-2"},dl=["disabled"],cl=["disabled"],ul={key:3,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"},gl={class:"bg-white rounded-2xl w-full max-w-lg mx-4 max-h-[90vh] overflow-y-auto"},ml={class:"p-6"},pl={class:"flex items-center justify-between mb-6"},hl={class:"space-y-4"},vl={key:0},fl={class:"flex flex-wrap gap-2 mb-3"},xl=["onClick","disabled"],yl={key:1},bl={class:"flex flex-wrap gap-2 mb-3"},wl=["onClick"],kl={class:"border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-blue-400 transition-colors"},_l={for:"post-media-input",class:"cursor-pointer"},Cl={key:0,class:"mb-4"},Ml=["src"],jl={class:"flex items-center justify-end space-x-3 mt-6 pt-6 border-t border-gray-100"},$l=["disabled"],Sl={key:4,class:"fixed inset-0 z-50 overflow-y-auto"},zl={class:"flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0"},Ll={class:"inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl"},Pl={class:"space-y-4"},Al={class:"flex items-center space-x-3"},Bl=["src","alt"],Vl={class:"text-sm text-gray-500"},El={key:0,class:"rounded-xl overflow-hidden bg-gray-100"},Ul=["src","alt"],Il={class:"prose max-w-none"},Tl={class:"text-gray-800 leading-relaxed whitespace-pre-wrap"},Rl={key:1,class:"flex flex-wrap gap-2"},Fl={class:"flex items-center justify-between pt-4 border-t border-gray-100"},Dl={class:"flex items-center space-x-6"},Hl=["fill"],Nl=["fill"],Ol={key:5,class:"fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50"},Wl={class:"relative w-full max-w-md mx-4 h-[80vh] bg-black rounded-2xl overflow-hidden"},ql={class:"absolute top-4 left-4 right-16 z-10 flex space-x-1"},Gl={class:"absolute top-12 left-4 right-4 z-10 flex items-center space-x-3 text-white"},Yl={class:"w-8 h-8 rounded-full overflow-hidden border-2 border-white"},Kl=["src","alt"],Jl={class:"font-semibold text-sm"},Ql={class:"text-xs text-gray-300"},Xl=["src","alt"],Zl={key:0,class:"absolute bottom-4 left-4 right-4 z-10"},ea={class:"bg-black bg-opacity-50 rounded-lg p-3"},ta={class:"text-white text-sm"},sa={class:"absolute inset-0 flex"},oa={key:0,class:"w-8 h-8 bg-black bg-opacity-50 rounded-full flex items-center justify-center text-white opacity-0 hover:opacity-100 transition-opacity"},ra={key:0,class:"w-8 h-8 bg-black bg-opacity-50 rounded-full flex items-center justify-center text-white opacity-0 hover:opacity-100 transition-opacity"},na={key:6,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"},la={class:"bg-white rounded-2xl w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto"},aa={class:"p-6"},ia={class:"flex items-center justify-between mb-6"},da={class:"space-y-4"},ca={key:0,class:"border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-blue-400 transition-colors"},ua={key:1,class:"relative"},ga=["src"],ma={class:"text-xs text-gray-500 mt-1"},pa={class:"flex items-center justify-end space-x-3 mt-6 pt-6 border-t border-gray-100"},ha=["disabled"],va={key:7,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"},fa={class:"bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"},xa={class:"p-6"},ya={class:"space-y-4"},ba={class:"flex space-x-3"},wa=["disabled"],ka={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4",fill:"none",viewBox:"0 0 24 24"},_a={key:0,class:"space-y-4"},Ca={class:"p-4 bg-gray-50 border border-gray-200 rounded-xl"},Ma={class:"text-gray-800 whitespace-pre-wrap"},ja={class:"flex space-x-3"},$a=["disabled"],Sa={key:8,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"},za={class:"bg-white rounded-2xl w-full max-w-md mx-4 max-h-[80vh] overflow-hidden"},La={class:"p-4 border-b border-gray-200 flex items-center justify-between"},Pa={class:"flex items-center space-x-2"},Aa={class:"overflow-y-auto max-h-96"},Ba={key:0,class:"p-8 text-center"},Va={key:1,class:"divide-y divide-gray-100"},Ea=["onClick"],Ua={class:"flex items-start space-x-3"},Ia={class:"flex-shrink-0 mt-1"},Ta={key:0,class:"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center"},Ra={key:1,class:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center"},Fa={key:2,class:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center"},Da={key:3,class:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center"},Ha={class:"flex-1 min-w-0"},Na={class:"text-sm text-gray-900 font-medium"},Oa={class:"text-sm text-gray-500 truncate"},Wa={class:"text-xs text-gray-400 mt-1"},qa={key:0,class:"flex-shrink-0"},Ga=["src"],Ya={key:1,class:"flex-shrink-0"},Ka={class:"fixed top-4 right-4 z-50 space-y-2"},Ja={class:"p-4"},Qa={class:"flex items-start"},Xa={class:"flex-shrink-0"},Za={key:0,class:"h-6 w-6 text-green-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},ei={key:1,class:"h-6 w-6 text-red-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},ti={key:2,class:"h-6 w-6 text-blue-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},si={key:3,class:"h-6 w-6 text-yellow-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},oi={class:"ml-3 w-0 flex-1 pt-0.5"},ri={class:"text-sm font-medium text-gray-900"},ni={class:"ml-4 flex-shrink-0 flex"},li=["onClick"],ai={key:9,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"},ii={class:"bg-white rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto"},di={key:0,class:"p-6 border-b border-gray-100"},ci={class:"bg-gray-50 rounded-xl p-4"},ui={class:"flex items-center space-x-3 mb-3"},gi={class:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center"},mi={class:"text-white font-medium text-sm"},pi={class:"font-medium text-gray-900 text-sm"},hi={class:"text-xs text-gray-500"},vi={key:0,class:"mb-3"},fi=["src","alt"],xi={key:1,class:"text-sm text-gray-700 line-clamp-3"},yi={__name:"Discover",props:{initialFeed:{type:Object,default:()=>({data:[],current_page:1,last_page:1,total:0})},availableTopics:{type:Array,default:()=>[]},initialStories:{type:Array,default:()=>[]}},setup(Tt){const q=Tt,Rt=[{title:"Discover",href:"/discover"}],Ft=x(!0),Dt=["Health and wellness content discovery","Personalized health recommendations","Community health discussions","Expert health tips and articles"],g=F({posts:q.initialFeed.data||[],currentPage:q.initialFeed.current_page||1,lastPage:q.initialFeed.last_page||1,total:q.initialFeed.total||0,loading:!1,selectedTopic:null,sortBy:"relevance",stories:q.initialStories||[]}),f=F({show:!1,post:null,shareUrl:"",shareText:""}),G=x(!1),Y=x(!1),ge=x(!1),$e=x(!1),Se=x(null),ze=x(!1),y=x(null),me=x({}),U=x({}),ne=x({}),pe=x({}),$=x({}),Le=x(null),he=x([]);let Ht=0;const Pe=x(!1),I=x([]),ve=Ne(()=>I.value.filter(s=>!s.read).length),fe=x(null),V=x({}),K=x({}),D=x({}),le=x(0),xe=x(!1),ye=x([]),m=F({caption:"",hashtags:[],suggestedHashtags:[],media:null,mediaPreview:null}),w=F({isGenerating:!1,showAiModal:!1,prompt:"",generatedContent:""}),h=F({query:"",results:[],isSearching:!1,recentSearches:[]}),N=F({content:"",postId:null}),A=F({}),J=x(null),Q=x(null),T=x(!0),Ae=Ne(()=>g.currentPage<g.lastPage),Nt=async()=>{if(!(g.loading||!Ae.value)){g.loading=!0;try{const s={page:g.currentPage+1,sort_by:g.sortBy};g.selectedTopic&&(s.content_type=g.selectedTopic);const l=(await axios.get("/web-api/feed",{params:s})).data;g.posts.push(...l.data||[]),g.currentPage=l.current_page,g.lastPage=l.last_page,g.total=l.total}catch(s){console.error("Error loading more posts:",s)}finally{g.loading=!1}}},Ot=async()=>{if(m.caption.trim())try{const s=new FormData;s.append("caption",m.caption),s.append("hashtags",JSON.stringify(m.hashtags)),s.append("source","internal"),m.media&&s.append("media",m.media);const t=await axios.post("/web-api/feed/create",s,{headers:{"Content-Type":"multipart/form-data"}});g.posts.unshift(t.data.post),Wt(),G.value=!1}catch(s){console.error("Error creating post:",s)}},Wt=()=>{m.caption="",m.hashtags=[],m.suggestedHashtags=[],m.media=null,m.mediaPreview=null},qt=()=>{w.showAiModal=!0,w.prompt="",w.generatedContent=""},Oe=async()=>{var s,t,l;if(!w.prompt.trim()){alert("Please enter a prompt for AI to generate content");return}w.isGenerating=!0;try{const a=(s=document.head.querySelector('meta[name="csrf-token"]'))==null?void 0:s.getAttribute("content");console.log("Current CSRF token:",a);const v=await axios.post("/web-api/ai/generate-content",{prompt:w.prompt,type:"social_post"},{headers:{"X-CSRF-TOKEN":a,Accept:"application/json","Content-Type":"application/json"}});if(v.data.success)w.generatedContent=v.data.content;else throw new Error(v.data.message||"Failed to generate content")}catch(a){console.error("Error generating AI content:",a),((t=a.response)==null?void 0:t.status)===419?alert("Session expired. Please refresh the page and try again."):((l=a.response)==null?void 0:l.status)===422?alert("Invalid input. Please check your prompt and try again."):alert("Failed to generate content. Please try again.")}finally{w.isGenerating=!1}},Gt=()=>{w.generatedContent&&(m.caption=w.generatedContent,ae(w.generatedContent),w.showAiModal=!1)},Yt=()=>{w.showAiModal=!1,w.prompt="",w.generatedContent=""},ae=s=>{if(!s||s.length<10){m.suggestedHashtags=[],m.hashtags=[];return}const t=[],l=/#\w+/g,a=s.match(l);a?(m.hashtags=[],a.forEach(M=>{t.push(M.toLowerCase()),m.hashtags.some(S=>S.toLowerCase()===M.toLowerCase())||m.hashtags.push(M)})):m.hashtags=[];const v=["health","wellness","fitness","nutrition","mental","physical","exercise","diet","meditation","yoga","sleep","stress","anxiety","depression","therapy","mindfulness","cardio","strength","weight","muscle","protein","vitamins","supplements","organic","natural","healing","recovery","prevention","immune","energy","balance","lifestyle","doctor","medical","treatment","symptoms","diagnosis","medicine","healthcare","selfcare","skincare","beauty","aging","longevity","chronic","pain","injury"],k=s.toLowerCase().split(/\s+/),p=[];k.forEach(M=>{const _=M.replace(/[^\w]/g,"");if(_.length>3&&v.some(S=>_.includes(S)||S.includes(_))){const S=`#${_}`,O=S.toLowerCase();!t.includes(O)&&!p.some(Me=>Me.toLowerCase()===O)&&p.length<8&&p.push(S)}}),p.length<3&&["#health","#wellness","#lifestyle","#selfcare","#mindfulness"].forEach(_=>{const S=_.toLowerCase();!t.includes(S)&&!p.some(O=>O.toLowerCase()===S)&&p.length<5&&p.push(_)}),m.suggestedHashtags=p},Kt=s=>{const t=s.toLowerCase(),l=[],a=/#\w+/g,v=m.caption.match(a);if(v&&v.forEach(p=>{l.push(p.toLowerCase())}),!l.includes(t)){let p=m.caption.trim();p&&!p.match(/#\w+\s*$/)?l.length>0?p+=" ":p+=`

`:p&&l.length>0&&(p+=" "),p+=s,m.caption=p,ae(m.caption)}},Jt=s=>{let t=m.caption;const l=new RegExp(`\\s*${s.replace("#","#")}\\s*`,"gi");t=t.replace(l," "),t=t.replace(/\s+/g," ").trim(),m.caption=t,ae(m.caption)};Vt(()=>m.caption,s=>{ae(s)},{debounce:500});const Qt=async s=>{if(!s||s.length<2){h.results=[];return}h.isSearching=!0;try{const t=await axios.get("/web-api/feed/search",{params:{q:s}});h.results=t.data.posts||[],h.recentSearches.includes(s)||(h.recentSearches.unshift(s),h.recentSearches.length>5&&h.recentSearches.pop())}catch(t){console.error("Search error:",t),h.results=[]}finally{h.isSearching=!1}};let Be=null;const We=s=>{h.query=s,Be&&clearTimeout(Be),Be=setTimeout(()=>{Qt(s)},300)},Xt=()=>{h.query="",h.results=[],h.isSearching=!1},qe=async s=>{try{const t=await axios.post(`/web-api/feed/like/${s.id}`);s.liked=t.data.liked,s.engagement_metrics.likes=t.data.like_count,t.data.original_instagram_likes!==void 0&&(s.original_instagram_likes=t.data.original_instagram_likes),X(`Post ${t.data.liked?"liked":"unliked"}!`,"success"),t.data.liked&&Ve("like",s,"You liked this post")}catch(t){console.error("Error toggling like:",t),X("Failed to like post","error")}},Ge=async s=>{try{const t=await axios.post(`/web-api/feed/save/${s.id}`);if(s.saved=t.data.saved,s.engagement_metrics.saves=t.data.save_count,t.data.original_instagram_saves!==void 0&&(s.original_instagram_saves=t.data.original_instagram_saves),t.data.saved)le.value+=1;else if(le.value=Math.max(0,le.value-1),xe.value){const l=ye.value.findIndex(a=>a.id===s.id);l>-1&&ye.value.splice(l,1)}X(`Post ${t.data.saved?"saved":"unsaved"}!`,"success"),t.data.saved&&Ve("save",s,"You saved this post")}catch(t){console.error("Error toggling save:",t),X("Failed to save post","error")}},Ye=s=>{me.value[s]=!me.value[s],me.value[s]&&!$.value[s]&&ss(s)},Ke=s=>{ne.value[s]=!ne.value[s]},Zt=s=>{pe.value[s]=!pe.value[s]},X=(s,t="info")=>{const l=++Ht,a={id:l,message:s,type:t,timestamp:Date.now()};he.value.push(a),setTimeout(()=>{Je(l)},3e3)},Je=s=>{const t=he.value.findIndex(l=>l.id===s);t>-1&&he.value.splice(t,1)},Ve=(s,t,l)=>{const a={id:Date.now()+Math.random(),type:s,post_id:t.id,post_title:t.caption?t.caption.substring(0,50)+"...":"Post",post_image:t.media_url,message:l,timestamp:new Date,read:!1};I.value.unshift(a),I.value.length>50&&(I.value=I.value.slice(0,50))},es=s=>{const t=I.value.find(l=>l.id===s);t&&(t.read=!0)},ts=()=>{I.value.forEach(s=>s.read=!0)},Qe=()=>{if(!Le.value)return;const s=new IntersectionObserver(t=>{const[l]=t;l.isIntersecting&&Ae.value&&!g.loading&&(console.log("🔄 Loading more posts via infinite scroll..."),Nt())},{root:null,rootMargin:"100px",threshold:.1});s.observe(Le.value),Q.value=s},ss=async s=>{try{const t=await axios.get(`/web-api/feed/${s}/comments`);$.value[s]=t.data.comments||[]}catch(t){console.error("Error loading comments:",t),$.value[s]=[]}},Xe=async s=>{var t;if(N.content.trim())try{const l=await axios.post(`/web-api/feed/${s}/comments`,{content:N.content});$.value[s]||($.value[s]=[]),$.value[s].unshift(l.data.comment);const a=(h.query?h.results:g.posts).find(k=>k.id===s);if(a)if(!a.original_instagram_comments&&a.source==="instagram"&&(a.original_instagram_comments=((t=a.engagement_metrics)==null?void 0:t.comments)||0),a.source==="instagram"){const k=a.original_instagram_comments||0,p=l.data.local_comment_count||a.engagement_metrics.comments-k+1;a.engagement_metrics.comments=k+p}else a.engagement_metrics.comments=(a.engagement_metrics.comments||0)+1;N.content="",N.postId=null,X("Comment added successfully!","success");const v=(h.query?h.results:g.posts).find(k=>k.id===s);v&&Ve("comment",v,"You commented on this post")}catch(l){console.error("Error adding comment:",l),X("Failed to add comment","error")}},Ze=s=>{Object.keys(V.value).forEach(t=>{t!==s.toString()&&(V.value[t]=!1)}),V.value[s]=!V.value[s]},et=async(s,t)=>{if(confirm("Are you sure you want to delete this comment?"))try{if(await axios.delete(`/web-api/feed/${s}/comments/${t}`),$.value[s]){const a=$.value[s].findIndex(v=>v.id===t);a>-1&&$.value[s].splice(a,1)}const l=(h.query?h.results:g.posts).find(a=>a.id===s);l&&(l.engagement_metrics.comments=Math.max(0,(l.engagement_metrics.comments||0)-1)),alert("Comment deleted successfully.")}catch(l){console.error("Error deleting comment:",l),alert("Failed to delete comment. Please try again.")}},tt=async(s,t)=>{try{const l=await axios.post(`/web-api/feed/${s}/comments/${t}/react`,{reaction_type:"like"}),a=$.value[s]||[],v=k=>{k.forEach(p=>{p.id===t&&(p.user_reaction=l.data.user_reaction,p.reaction_counts=l.data.reaction_counts),p.replies&&v(p.replies)})};v(a)}catch(l){console.error("Error toggling comment like:",l)}},os=s=>{K.value[s]=!K.value[s],K.value[s]||(D.value[s]="")},st=async(s,t)=>{var l;if((l=D.value[t])!=null&&l.trim())try{const a=await axios.post(`/web-api/feed/${s}/comments/${t}/reply`,{content:D.value[t]}),v=$.value[s]||[];(M=>{M.forEach(_=>{_.id===t&&(_.replies||(_.replies=[]),_.replies.push(a.data.comment))})})(v);const p=(h.query?h.results:g.posts).find(M=>M.id===s);p&&(p.engagement_metrics.comments=(p.engagement_metrics.comments||0)+1),D.value[t]="",K.value[t]=!1}catch(a){console.error("Error adding reply:",a)}},rs=s=>{D.value[s]="",K.value[s]=!1},ns=async()=>{if(!xe.value)try{const s=await axios.get("/web-api/saved-posts");ye.value=s.data.posts||[]}catch(s){console.error("Error loading saved posts:",s),ye.value=[]}xe.value=!xe.value},ls=async s=>{if(confirm("Are you sure you want to report this post?"))try{await axios.post(`/web-api/feed/${s.id}/report`,{reason:"inappropriate_content"}),alert("Post reported successfully. Thank you for helping keep our community safe.")}catch(t){console.error("Error reporting post:",t),alert("Failed to report post. Please try again.")}},as=async s=>{if(confirm("Are you sure you want to delete this post? This action cannot be undone."))try{await axios.delete(`/web-api/feed/${s.id}`);const t=h.query?h.results:g.posts,l=t.findIndex(a=>a.id===s.id);l>-1&&t.splice(l,1),alert("Post deleted successfully.")}catch(t){console.error("Error deleting post:",t),alert("Failed to delete post. Please try again.")}},is=s=>{f.post=s,f.shareUrl=cs(s),f.shareText=us(s),f.show=!0},ds=()=>{f.show=!1,f.post=null,f.shareUrl="",f.shareText=""},cs=s=>`${window.location.origin}/discover?post=${s.id}`,us=s=>{var v;const t=s.source==="instagram"?"Instagram":"Medroid AI",l=s.source==="instagram"?`@${s.instagram_username||s.username||"Instagram User"}`:`${((v=s.user)==null?void 0:v.name)||"Medroid AI User"}`;return`${s.caption?s.caption.substring(0,100)+"...":"Check out this health post"}

Shared from ${t} by ${l} on Medroid AI Health Community`},gs=async()=>{const s=encodeURIComponent(`${f.shareText}

${f.shareUrl}`);window.open(`https://wa.me/?text=${s}`,"_blank"),await Z("whatsapp")},ms=async()=>{const s=encodeURIComponent(f.shareUrl),t=encodeURIComponent("Health Post from Medroid AI Community"),l=encodeURIComponent(f.shareText);window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${s}&title=${t}&summary=${l}`,"_blank"),await Z("linkedin")},ps=async()=>{const s=encodeURIComponent("Health Post from Medroid AI Community"),t=encodeURIComponent(`${f.shareText}

View post: ${f.shareUrl}`);window.open(`mailto:?subject=${s}&body=${t}`),await Z("email")},hs=async()=>{const s=encodeURIComponent(`${f.shareText}

${f.shareUrl}`);window.open(`sms:?body=${s}`),await Z("sms")},vs=async()=>{try{await navigator.clipboard.writeText(f.shareUrl),alert("Link copied to clipboard!"),await Z("copy")}catch(s){console.error("Failed to copy link:",s);const t=document.createElement("textarea");t.value=f.shareUrl,document.body.appendChild(t),t.select(),document.execCommand("copy"),document.body.removeChild(t),alert("Link copied to clipboard!"),await Z("copy")}},Z=async s=>{var t;try{if(!f.post)return;await axios.post(`/web-api/feed/${f.post.id}/share`,{platform:s});const l=g.posts.findIndex(a=>a.id===f.post.id);if(l!==-1){const a=((t=g.posts[l].engagement_metrics)==null?void 0:t.shares)||0;g.posts[l].engagement_metrics={...g.posts[l].engagement_metrics,shares:a+1}}console.log(`Share tracked: ${s}`)}catch(l){console.error("Failed to track share:",l)}},fs=s=>{Object.keys(U.value).forEach(t=>{t!==s.toString()&&(U.value[t]=!1)}),U.value[s]=!U.value[s]},xs=s=>{const t=s.target.files[0];t&&(t.size>1024*1024?Te(t,l=>{ot(l)}):ot(t))},ot=s=>{const t=new Image;t.onload=()=>{const l=t.width/t.height,a=4/3;if(Math.abs(l-a)>.05)fe.value=s,ge.value=!0;else{m.media=s;const k=new FileReader;k.onload=p=>{m.mediaPreview=p.target.result},k.readAsDataURL(s)}},t.src=URL.createObjectURL(s)},ys=s=>{if(s){const t=new File([s],"cropped-image.jpg",{type:"image/jpeg"});if(t.size>1024*1024)Te(t,l=>{m.media=l;const a=new FileReader;a.onload=v=>{m.mediaPreview=v.target.result},a.readAsDataURL(l)});else{m.media=t;const l=new FileReader;l.onload=a=>{m.mediaPreview=a.target.result},l.readAsDataURL(t)}}ge.value=!1,fe.value=null},bs=()=>{ge.value=!1,fe.value=null},ie=x(!1),ee=x(null),L=x(0),R=x([]),be=x(null),de=x(0),ws=async s=>{console.log("Viewing story:",s),ee.value=s,L.value=0;try{const t=await axios.get(`/web-api/stories/user/${s.user_id}`);R.value=t.data.stories||[],ie.value=!0,we(),document.addEventListener("keydown",lt)}catch(t){console.error("Error loading user stories:",t)}},Ee=()=>{ie.value=!1,ee.value=null,L.value=0,R.value=[],Ie(),document.removeEventListener("keydown",lt)},rt=()=>{Ie()},nt=()=>{ie.value&&we()},lt=s=>{if(ie.value)switch(s.key){case"ArrowLeft":s.preventDefault(),at();break;case"ArrowRight":case" ":s.preventDefault(),Ue();break;case"Escape":s.preventDefault(),Ee();break}},Ue=()=>{R.value&&L.value<R.value.length-1?(L.value++,we()):Ee()},at=()=>{L.value>0&&(L.value--,we())},we=()=>{Ie(),de.value=0;const s=5e3,t=50,l=t/s*100;be.value=setInterval(()=>{de.value+=l,de.value>=100&&Ue()},t)},Ie=()=>{be.value&&(clearInterval(be.value),be.value=null),de.value=0},te=Ne(()=>R.value&&R.value[L.value]?R.value[L.value]:null),se=(s=null)=>{Se.value=s,$e.value=!0},ks=()=>{$e.value=!1,Se.value=null},_s=s=>{y.value=s,ze.value=!0},ke=()=>{ze.value=!1,y.value=null},C=F({media:null,mediaPreview:null,caption:""}),Cs=async()=>{var s,t;if(!C.media){alert("Please select an image or video for your story");return}try{const l=new FormData;l.append("media",C.media),C.caption.trim()&&l.append("caption",C.caption),console.log("Creating story with:",{media:C.media.name,caption:C.caption});const a=await axios.post("/web-api/stories",l,{headers:{"Content-Type":"multipart/form-data"}});console.log("Story creation response:",a.data),await ut(),it(),Y.value=!1,alert("Story created successfully!")}catch(l){console.error("Error creating story:",l),(t=(s=l.response)==null?void 0:s.data)!=null&&t.message?alert("Failed to create story: "+l.response.data.message):alert("Failed to create story. Please try again.")}},it=()=>{C.media=null,C.mediaPreview=null,C.caption=""},Ms=s=>{const t=s.target.files[0];if(t){console.log("Story file selected:",t.name,t.type,t.size);const l=10*1024*1024;if(t.size>l){alert("File size too large. Please choose a file smaller than 10MB.");return}if(t.type.startsWith("image/"))Te(t,a=>{console.log("Image compressed:",a.name,a.size),C.media=a;const v=new FileReader;v.onload=k=>{C.mediaPreview=k.target.result,console.log("Story preview created")},v.readAsDataURL(a)});else{console.log("Using video file as-is"),C.media=t;const a=new FileReader;a.onload=v=>{C.mediaPreview=v.target.result,console.log("Video preview created")},a.readAsDataURL(t)}}},Te=(s,t,l=.8,a=1080)=>{const v=document.createElement("canvas"),k=v.getContext("2d"),p=new Image;p.onload=()=>{let{width:M,height:_}=p;M>a&&(_=_*a/M,M=a),v.width=M,v.height=_,k.drawImage(p,0,0,M,_),v.toBlob(t,"image/jpeg",l)},p.src=URL.createObjectURL(s)},oe=s=>{const t=new Date(s),a=Math.floor((new Date-t)/(1e3*60*60));return a<1?"Just now":a<24?`${a}h ago`:a<168?`${Math.floor(a/24)}d ago`:t.toLocaleDateString()},dt=s=>{s.target.closest(".post-menu-container")||Object.keys(U.value).forEach(t=>{U.value[t]=!1}),s.target.closest(".comment-menu-container")||Object.keys(V.value).forEach(t=>{V.value[t]=!1})},ct=async()=>{g.loading=!0;try{const t=(await axios.get("/web-api/feed")).data;g.posts=t.data||[],g.currentPage=t.current_page||1,g.lastPage=t.last_page||1,g.total=t.total||0,le.value=g.posts.filter(l=>l.saved).length,console.log("Feed loaded with",g.posts.length,"posts"),je(()=>{De()})}catch(s){console.error("Error loading feed:",s),g.posts=[]}finally{g.loading=!1}},ut=async()=>{try{const s=await axios.get("/web-api/stories");console.log("Stories response:",s.data);const t=s.data.stories||[],l=await js();g.stories=[...l,...t],console.log("Updated stories state:",g.stories)}catch(s){console.error("Error loading stories:",s),g.stories=[]}},js=async()=>{try{const s=await axios.get("/web-api/instagram/stories");return s.data.success?s.data.stories.map(t=>({...t,source:"instagram",user_id:t.user_id||"instagram_user",username:t.username||"Instagram User",profile_image:t.profile_image||"/images/instagram-avatar.svg",story_count:t.story_count||1,latest_story_time:t.latest_story_time||t.created_at})):[]}catch(s){return console.error("Error loading Instagram stories:",s),[]}},c=F({connected:!1,account:null,loading:!1,syncing:!1,connecting:!1,progress:{status:"idle",step:"",progress:0,message:"",imported_count:0}}),gt=async()=>{try{const s=await axios.get("/web-api/instagram/account-status");s.data.success&&(c.connected=s.data.connected,c.account=s.data.account)}catch(s){console.error("Error checking Instagram status:",s)}},Re=async()=>{try{const s=await axios.get("/web-api/instagram/connection-progress");s.data.success&&(c.progress=s.data.progress,["connecting","importing","syncing"].includes(c.progress.status)?setTimeout(Re,1e3):c.progress.status==="completed"?(await gt(),c.connecting=!1,c.progress.imported_count>0?alert(`Instagram connected successfully! Imported ${c.progress.imported_count} health-related posts.`):alert("Instagram connected successfully! No health-related posts found to import.")):c.progress.status==="error"&&(c.connecting=!1,alert(`Instagram connection failed: ${c.progress.message}`)))}catch(s){console.error("Error checking connection progress:",s)}},$s=async()=>{try{c.loading=!0,c.connecting=!0;const s=await axios.get("/web-api/instagram/auth-url");s.data.success&&(setTimeout(Re,2e3),window.location.href=s.data.auth_url)}catch(s){console.error("Error getting Instagram auth URL:",s),alert("Failed to connect Instagram. Please try again."),c.connecting=!1}finally{c.loading=!1}},mt=async()=>{if(confirm("Are you sure you want to disconnect your Instagram account?"))try{c.loading=!0,(await axios.post("/web-api/instagram/disconnect")).data.success&&(c.connected=!1,c.account=null,alert("Instagram account disconnected successfully"))}catch(s){console.error("Error disconnecting Instagram:",s),alert("Failed to disconnect Instagram. Please try again.")}finally{c.loading=!1}},pt=async()=>{var s;try{c.syncing=!0;const t=await axios.post("/web-api/instagram/sync");t.data.success&&(alert(`Successfully imported ${t.data.imported_count} health-related posts`),ct())}catch(t){console.error("Error syncing Instagram content:",t),((s=t.response)==null?void 0:s.status)===401?alert("Instagram access token has expired. Please reconnect your account."):alert("Failed to sync Instagram content. Please try again.")}finally{c.syncing=!1}},_e=s=>{A[s]||(A[s]={playing:!1,loaded:!1})},Ce=s=>document.querySelector(`video[data-post-id="${s}"]`),ht=s=>{console.log("Toggle video play for post:",s);const t=Ce(s);if(console.log("Video element found:",t),!t){console.error("Video element not found for post:",s);return}_e(s),A[s].playing?Fe(s):vt(s)},vt=s=>{const t=Ce(s);if(!t){console.error("Video element not found for play:",s);return}J.value&&J.value!==s&&Fe(J.value),_e(s),t.muted=T.value,console.log("Attempting to play video:",s,"muted:",t.muted),t.play().then(()=>{console.log("Video playing successfully:",s),A[s].playing=!0,J.value=s}).catch(l=>{console.error("Video play failed:",l)})},Fe=s=>{const t=Ce(s);if(!t){console.error("Video element not found for pause:",s);return}_e(s),console.log("Pausing video:",s),t.pause(),A[s].playing=!1,J.value===s&&(J.value=null)},Ss=()=>{console.log("Toggle global mute state from:",T.value),T.value=!T.value;const s=document.querySelectorAll("video[data-post-id]");s.forEach(t=>{t.muted=T.value}),console.log("Global mute state changed to:",T.value),console.log("Applied to",s.length,"videos")},zs=s=>{var t;return((t=A[s])==null?void 0:t.playing)||!1},Ls=()=>T.value,Ps=s=>{console.log("Video loaded for post:",s),_e(s),A[s].loaded=!0;const t=Ce(s);t&&(t.muted=T.value)},As=()=>{Q.value=new IntersectionObserver(s=>{s.forEach(t=>{const a=t.target.getAttribute("data-post-id");console.log(`Video ${a} intersection:`,{isIntersecting:t.isIntersecting,intersectionRatio:t.intersectionRatio}),t.isIntersecting&&t.intersectionRatio>.5?(console.log(`Auto-playing video ${a}`),setTimeout(()=>{t.isIntersecting&&vt(a)},200)):(console.log(`Auto-pausing video ${a}`),Fe(a))})},{threshold:[.5],rootMargin:"0px"})},De=()=>{je(()=>{Q.value&&Q.value.disconnect(),As();const s=document.querySelectorAll("video[data-post-id]");console.log(`Found ${s.length} videos to observe`),s.forEach(t=>{var a;const l=t.getAttribute("data-post-id");console.log(`Observing video for post ${l}`),t.muted=T.value,(a=Q.value)==null||a.observe(t)})})};return Bs(()=>{console.log("🚀 Discover component mounted"),console.log("Initial stories from props:",q.initialStories),console.log("Initial stories state:",g.stories),ct().then(()=>{console.log("📺 Feed loaded, setting up video auto-play..."),setTimeout(()=>{console.log("🔍 Calling observeVideos..."),De()},1e3)}),ut(),gt();const s=new URLSearchParams(window.location.search);if(s.get("instagram_success"))c.connecting=!0,Re(),window.history.replaceState({},document.title,window.location.pathname);else if(s.get("instagram_error")){const t=s.get("instagram_error");alert(`Instagram connection failed: ${decodeURIComponent(t)}`),window.history.replaceState({},document.title,window.location.pathname)}document.addEventListener("click",dt),je(()=>{Qe()})}),Vt(()=>g.posts,()=>{je(()=>{De(),Qe()})},{deep:!0,flush:"post"}),Vs(()=>{var s;document.removeEventListener("click",dt),(s=Q.value)==null||s.disconnect()}),(s,t)=>{const l=Et("ImageCropper"),a=Et("UserProfile");return n(),r(z,null,[ce(Es(Us),{title:"Discover - Medroid"}),ce(Rs,{breadcrumbs:Rt},{default:Is(()=>{var v,k,p,M,_,S,O,Me,ft,xt;return[Ft.value?(n(),r("div",Hs,[ce(Fs,{description:"We're working hard to bring you an amazing discovery experience with health content, community posts, and social feeds.",features:Dt,"action-button":{text:"Continue Chatting with AI Doctor",action:"/chat"},"action-description":"Get instant health advice while we prepare the discover feature","min-height":"min-h-screen",onAction:t[0]||(t[0]=o=>s.window.location.href=o)})])):(n(),r("div",Ns,[e("div",Os,[e("div",Ws,[e("div",qs,[t[47]||(t[47]=e("div",{class:"flex items-center space-x-3 sm:space-x-4"},[e("div",{class:"flex items-center space-x-2 sm:space-x-3"},[e("div",{class:"w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-teal-500 to-cyan-500 rounded-full flex items-center justify-center",style:{background:"linear-gradient(135deg, #17C3B2 0%, #8BE9C8 100%)"}},[e("svg",{class:"w-4 h-4 sm:w-6 sm:h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})])]),e("div",null,[e("h1",{class:"text-lg sm:text-xl font-bold text-gray-900"},"Discover"),e("p",{class:"text-xs text-gray-500 hidden sm:block"},"Health insights & community")])])],-1)),e("div",Gs,[e("button",{onClick:t[3]||(t[3]=o=>Pe.value=!0),class:"relative p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-full transition-all duration-200"},[t[45]||(t[45]=e("svg",{class:"w-5 h-5 sm:w-6 sm:h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 17h5l-3.5-3.5a8.38 8.38 0 01-1.5-5V6a6 6 0 10-12 0v2.5a8.38 8.38 0 01-1.5 5L5 17h5m5 0v1a3 3 0 11-6 0v-1m6 0H9"})],-1)),ve.value>0?(n(),r("span",Ys,d(ve.value>99?"99+":ve.value),1)):i("",!0)]),e("button",{onClick:t[4]||(t[4]=o=>G.value=!0),class:"medroid-create-btn inline-flex items-center px-3 sm:px-4 py-1.5 sm:py-2 text-white text-xs sm:text-sm font-medium rounded-full transition-all duration-200 shadow-lg hover:shadow-xl"},t[46]||(t[46]=[e("svg",{class:"w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"})],-1),e("span",{class:"hidden sm:inline"},"Create Post",-1),e("span",{class:"sm:hidden"},"Post",-1)]))])])])]),e("div",Ks,[e("div",Js,[e("div",Qs,[e("div",Xs,[e("div",Zs,[e("div",eo,[e("button",{onClick:t[5]||(t[5]=o=>se()),class:"w-12 h-12 mx-auto mb-2 rounded-full overflow-hidden border-2 border-gray-200 hover:border-teal-400 transition-colors"},[e("img",{src:(v=s.$page.props.auth.user)!=null&&v.profile_image?s.$page.props.auth.user.profile_image.startsWith("http")?s.$page.props.auth.user.profile_image:`/storage/${s.$page.props.auth.user.profile_image}`:"/images/default-avatar.svg",alt:"Profile",class:"w-full h-full object-cover"},null,8,to)]),t[48]||(t[48]=e("h3",{class:"text-sm font-semibold text-gray-900 mb-1"},"Your Profile",-1)),t[49]||(t[49]=e("p",{class:"text-xs text-gray-500 mb-3"},"View your posts & stats",-1)),e("button",{onClick:t[6]||(t[6]=o=>se()),class:"medroid-profile-btn w-full py-2 text-white text-sm font-medium rounded-lg transition-all duration-200"}," View Profile ")])]),e("div",so,[t[54]||(t[54]=e("h3",{class:"text-sm font-semibold text-gray-900 mb-3"},"Search Posts",-1)),e("div",oo,[e("div",ro,[W(e("input",{"onUpdate:modelValue":t[7]||(t[7]=o=>h.query=o),onInput:t[8]||(t[8]=o=>We(o.target.value)),type:"text",placeholder:"Search by hashtags, keywords...",class:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent text-sm"},null,544),[[re,h.query]]),t[51]||(t[51]=e("svg",{class:"absolute left-3 top-2.5 w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)),h.query?(n(),r("button",{key:0,onClick:Xt,class:"absolute right-3 top-2.5 w-4 h-4 text-gray-400 hover:text-gray-600"},t[50]||(t[50]=[e("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))):i("",!0)]),h.recentSearches.length>0&&!h.query?(n(),r("div",no,[t[52]||(t[52]=e("p",{class:"text-xs text-gray-500 font-medium"},"Recent Searches",-1)),e("div",lo,[(n(!0),r(z,null,P(h.recentSearches,o=>(n(),r("button",{key:o,onClick:j=>We(o),class:"px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full hover:bg-gray-200 transition-colors"},d(o),9,ao))),128))])])):i("",!0),h.isSearching?(n(),r("div",io,t[53]||(t[53]=[e("div",{class:"w-4 h-4 border-2 border-teal-200 border-t-teal-500 rounded-full animate-spin mx-auto"},null,-1)]))):i("",!0)])])])]),e("div",co,[h.query&&h.results.length>0?(n(),r("div",uo,[e("div",go,[e("h3",mo,' Search Results for "'+d(h.query)+'" ',1),e("span",po,d(h.results.length)+" posts found",1)])])):i("",!0),h.query&&h.results.length===0&&!h.isSearching?(n(),r("div",ho,t[55]||(t[55]=[e("svg",{class:"w-12 h-12 text-gray-300 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1),e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"No posts found",-1),e("p",{class:"text-gray-500"},"Try searching with different keywords or hashtags",-1)]))):i("",!0),e("div",vo,[e("div",fo,[t[56]||(t[56]=e("h3",{class:"text-sm font-semibold text-gray-900"},"Stories",-1)),e("button",{onClick:t[9]||(t[9]=o=>Y.value=!0),class:"text-xs font-medium",style:{color:"#17C3B2"}}," Add Story ")]),e("div",xo,[e("div",yo,[e("button",{onClick:t[10]||(t[10]=o=>Y.value=!0),class:"w-12 h-12 rounded-full flex items-center justify-center border-2 border-dashed transition-colors",style:{background:"linear-gradient(135deg, #17C3B2 0.1, #8BE9C8 0.1)","border-color":"#17C3B2"}},t[57]||(t[57]=[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4v16m8-8H4"})],-1)])),t[58]||(t[58]=e("p",{class:"text-xs text-gray-500 text-center mt-1"},"Add Story",-1))]),(n(!0),r(z,null,P(g.stories,o=>{var j;return n(),r("div",{key:`${o.source||"local"}-${o.user_id}`,class:"flex-shrink-0 cursor-pointer",onClick:He=>ws(o)},[e("div",wo,[o.source==="instagram"?(n(),r("div",ko,[e("div",_o,[e("img",{src:o.profile_image||o.user_avatar||"/images/default-avatar.svg",alt:o.username,class:"w-full h-full rounded-full object-cover"},null,8,Co)])])):(n(),r("div",Mo,[e("div",jo,[e("img",{src:o.user_avatar||o.profile_image||"/images/default-avatar.svg",alt:o.username,class:"w-full h-full rounded-full object-cover"},null,8,$o)])])),o.story_count>1?(n(),r("div",So,d(o.story_count),1)):o.has_unviewed?(n(),r("div",zo)):i("",!0),o.source==="instagram"?(n(),r("div",Lo,t[59]||(t[59]=[e("svg",{class:"w-3 h-3 text-white",fill:"currentColor",viewBox:"0 0 24 24"},[e("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"})],-1)]))):i("",!0)]),e("p",Po,d(o.username||((j=o.user)==null?void 0:j.name)||"Anonymous"),1)],8,bo)}),128)),g.stories.length===0?(n(),r("div",Ao,t[60]||(t[60]=[e("svg",{class:"w-8 h-8 text-gray-300 mx-auto mb-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"})],-1),e("p",{class:"text-xs text-gray-500"},"No stories yet",-1),e("p",{class:"text-xs text-gray-400 mt-1"},"Be the first to share a story!",-1)]))):i("",!0)])]),c.connected?i("",!0):(n(),r("div",Bo,[e("div",Vo,[t[61]||(t[61]=e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-5 h-5 text-pink-500",fill:"currentColor",viewBox:"0 0 24 24"},[e("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"})]),e("h3",{class:"text-sm font-semibold text-gray-900"},"Instagram Integration")],-1)),c.connected&&c.account?(n(),r("div",Eo,[e("span",{class:E(["px-2 py-1 text-xs font-medium rounded-full",c.account.account_type==="BUSINESS"?"bg-blue-100 text-blue-800":"bg-purple-100 text-purple-800"])},d(c.account.account_type_display),3)])):i("",!0)]),c.connecting&&c.progress.status!=="idle"?(n(),r("div",Uo,[e("div",Io,[c.progress.status==="error"?(n(),r("svg",To,t[62]||(t[62]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"},null,-1)]))):c.progress.status==="completed"?(n(),r("svg",Ro,t[63]||(t[63]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)]))):(n(),r("svg",Fo,t[64]||(t[64]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)])))]),e("h4",Do,d(c.progress.status==="completed"?"Connection Successful!":c.progress.status==="error"?"Connection Failed":"Connecting Instagram..."),1),e("p",Ho,d(c.progress.message),1),["connecting","importing","syncing"].includes(c.progress.status)?(n(),r("div",No,[e("div",Oo,[t[65]||(t[65]=e("span",null,"Progress",-1)),e("span",null,d(c.progress.progress)+"%",1)]),e("div",Wo,[e("div",{class:"bg-gradient-to-r from-pink-500 to-purple-600 h-2 rounded-full transition-all duration-500",style:Ut({width:c.progress.progress+"%"})},null,4)])])):i("",!0),c.progress.imported_count>0?(n(),r("div",qo," ✅ Imported "+d(c.progress.imported_count)+" health-related posts ",1)):i("",!0)])):c.connected?(n(),r("div",Qo,[e("div",Xo,[e("div",Zo,[t[72]||(t[72]=e("div",{class:"w-10 h-10 bg-gradient-to-br from-pink-400 to-purple-500 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"currentColor",viewBox:"0 0 24 24"},[e("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069z"})])],-1)),e("div",null,[e("p",er,"@"+d(c.account.username),1),e("p",tr,d(c.account.media_count)+" posts",1)])]),e("div",sr,[e("button",{onClick:pt,disabled:c.syncing,class:"px-3 py-1 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"},[c.syncing?(n(),r("svg",rr,t[73]||(t[73]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):i("",!0),B(" "+d(c.syncing?"Syncing...":"Sync"),1)],8,or),e("button",{onClick:mt,disabled:c.loading,class:"px-3 py-1 bg-gray-200 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"}," Disconnect ",8,nr)])]),c.account.last_synced_at?(n(),r("div",lr," Last synced: "+d(c.account.last_synced_at),1)):i("",!0)])):(n(),r("div",Go,[t[68]||(t[68]=e("div",{class:"w-16 h-16 bg-gradient-to-br from-pink-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-8 h-8 text-pink-500",fill:"currentColor",viewBox:"0 0 24 24"},[e("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"})])],-1)),t[69]||(t[69]=e("h4",{class:"text-lg font-medium text-gray-900 mb-2"},"Connect Your Instagram",-1)),t[70]||(t[70]=e("p",{class:"text-sm text-gray-500 mb-4 max-w-md mx-auto"}," Connect your Instagram Business or Creator account to share your health-related posts with the community. ",-1)),e("button",{onClick:$s,disabled:c.loading,class:"inline-flex items-center px-4 py-2 bg-gradient-to-r from-pink-500 to-purple-600 text-white font-medium rounded-lg hover:from-pink-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"},[c.loading?(n(),r("svg",Ko,t[66]||(t[66]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(n(),r("svg",Jo,t[67]||(t[67]=[e("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069z"},null,-1)]))),B(" "+d(c.loading?"Connecting...":"Connect Instagram"),1)],8,Yo),t[71]||(t[71]=e("p",{class:"text-xs text-gray-400 mt-2"}," Only Business and Creator accounts are supported ",-1))]))])),e("div",ar,[g.loading&&g.posts.length===0?(n(),r("div",ir,t[74]||(t[74]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"},null,-1),e("p",{class:"text-gray-500"},"Loading amazing content...",-1)]))):g.posts.length===0?(n(),r("div",dr,[t[76]||(t[76]=e("div",{class:"w-24 h-24 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-6"},[e("svg",{class:"w-12 h-12 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})])],-1)),t[77]||(t[77]=e("h3",{class:"text-xl font-semibold text-gray-900 mb-3"},"No posts yet",-1)),t[78]||(t[78]=e("p",{class:"text-gray-500 mb-6 max-w-md mx-auto"},"Be the first to share health insights with the community!",-1)),e("button",{onClick:t[11]||(t[11]=o=>G.value=!0),class:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 text-white font-medium rounded-full hover:from-blue-600 hover:to-cyan-600 transition-all duration-200 shadow-lg hover:shadow-xl"},t[75]||(t[75]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"})],-1),B(" Create First Post ")]))])):(n(),r("div",cr,[(n(!0),r(z,null,P(h.query?h.results:g.posts,o=>{var j,He,yt,bt,wt,kt,_t,Ct;return n(),r("article",{key:o.id,class:"bg-white rounded-xl sm:rounded-2xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-all duration-200 mb-4 sm:mb-6"},[e("div",ur,[e("div",gr,[e("div",mr,[e("button",{onClick:u=>{var H;return se((H=o.user)==null?void 0:H.id)},class:"w-10 h-10 sm:w-12 sm:h-12 rounded-full overflow-hidden border-2 border-gray-200 hover:border-blue-400 transition-colors"},[e("img",{src:(j=o.user)!=null&&j.profile_image?o.user.profile_image.startsWith("http")?o.user.profile_image:`/storage/${o.user.profile_image}`:"/images/default-avatar.svg",alt:((He=o.user)==null?void 0:He.name)||"Anonymous",class:"w-full h-full object-cover"},null,8,hr)],8,pr),e("div",vr,[e("div",fr,[e("button",{onClick:u=>{var H;return se((H=o.user)==null?void 0:H.id)},class:"font-semibold text-gray-900 hover:text-blue-600 transition-colors text-left text-sm sm:text-base"},d(((yt=o.user)==null?void 0:yt.name)||"Anonymous"),9,xr),o.source==="instagram"?(n(),r("div",yr,t[79]||(t[79]=[e("svg",{class:"w-3.5 h-3.5 text-white",fill:"currentColor",viewBox:"0 0 24 24"},[e("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"})],-1)]))):i("",!0)]),e("p",br,[B(d(oe(o.published_at||o.created_at))+" ",1),o.source==="instagram"&&(o.instagram_username||o.username)?(n(),r("span",wr," @"+d(o.instagram_username||o.username),1)):i("",!0)])])]),e("div",kr,[e("button",{onClick:u=>fs(o.id),class:"p-1.5 sm:p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors"},t[80]||(t[80]=[e("svg",{class:"w-4 h-4 sm:w-5 sm:h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"})],-1)]),8,_r),U.value[o.id]?(n(),r("div",Cr,[o.can_delete?(n(),r("button",{key:0,onClick:u=>{as(o),U.value[o.id]=!1},class:"w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"},t[81]||(t[81]=[e("svg",{class:"w-4 h-4 inline mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),B(" Delete Post ")]),8,Mr)):i("",!0),o.can_delete?i("",!0):(n(),r("button",{key:1,onClick:u=>{ls(o),U.value[o.id]=!1},class:"w-full text-left px-4 py-2 text-sm text-orange-600 hover:bg-orange-50 transition-colors"},t[82]||(t[82]=[e("svg",{class:"w-4 h-4 inline mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"})],-1),B(" Report Post ")]),8,jr))])):i("",!0)])])]),o.media_url?(n(),r("div",$r,[e("div",Sr,[o.content_type==="video"?(n(),r("div",zr,[e("video",{"data-post-id":o.id,src:o.video_url||o.media_url,poster:o.thumbnail_url||o.media_url,class:"w-full object-cover cursor-pointer",style:{"aspect-ratio":"9/16",height:"85vh","min-height":"600px"},muted:"",loop:"",playsinline:"",preload:"metadata",onClick:ue(u=>ht(o.id),["stop"]),onLoadedmetadata:u=>Ps(o.id),onPlay:u=>A[o.id]&&(A[o.id].playing=!0),onPause:u=>A[o.id]&&(A[o.id].playing=!1)}," Your browser does not support the video tag. ",40,Lr),W(e("div",{class:"absolute inset-0 flex items-center justify-center",onClick:ue(u=>ht(o.id),["stop"])},t[83]||(t[83]=[e("div",{class:"bg-black bg-opacity-50 text-white rounded-full p-4 hover:bg-opacity-70 transition-all cursor-pointer"},[e("svg",{class:"w-8 h-8",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z"})])],-1)]),8,Pr),[[Ts,!zs(o.id)]]),t[86]||(t[86]=e("div",{class:"absolute top-2 left-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs font-medium"},[e("svg",{class:"w-3 h-3 inline mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M2 6a2 2 0 012-2h6l2 2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"})]),B(" Video ")],-1)),e("button",{onClick:t[12]||(t[12]=ue(u=>Ss(),["stop"])),class:"absolute bottom-2 right-2 bg-black bg-opacity-70 text-white rounded-full p-2 hover:bg-opacity-80 transition-all z-10"},[Ls()?(n(),r("svg",Ar,t[84]||(t[84]=[e("path",{"fill-rule":"evenodd",d:"M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.793L4.828 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.828l3.555-3.793A1 1 0 019.383 3.076zM12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z","clip-rule":"evenodd"},null,-1)]))):(n(),r("svg",Br,t[85]||(t[85]=[e("path",{"fill-rule":"evenodd",d:"M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.793L4.828 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.828l3.555-3.793A1 1 0 019.383 3.076zM14.657 2.929a1 1 0 011.414 0A9.972 9.972 0 0119 10a9.972 9.972 0 01-2.929 7.071 1 1 0 01-1.414-1.414A7.971 7.971 0 0017 10c0-2.21-.894-4.208-2.343-5.657a1 1 0 010-1.414zm-2.829 2.828a1 1 0 011.415 0A5.983 5.983 0 0115 10a5.984 5.984 0 01-1.757 4.243 1 1 0 01-1.415-1.414A3.984 3.984 0 0013 10a3.983 3.983 0 00-1.172-2.828 1 1 0 010-1.415z","clip-rule":"evenodd"},null,-1)])))])])):(n(),r("img",{key:1,src:o.media_url,alt:o.caption,class:"w-full object-cover",style:{"max-height":"600px","min-height":"300px"}},null,8,Vr)),o.source==="instagram"&&o.permalink?(n(),r("div",Er,[e("a",{href:o.permalink,target:"_blank",rel:"noopener noreferrer",class:"inline-flex items-center px-2 py-1 bg-black bg-opacity-70 text-white text-xs font-medium rounded-lg hover:bg-opacity-80 transition-all"},t[87]||(t[87]=[e("svg",{class:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"})],-1),B(" View on Instagram ")]),8,Ur)])):i("",!0)])])):i("",!0),e("div",Ir,[e("div",Tr,[e("div",Rr,[e("button",{onClick:u=>qe(o),class:E(["flex items-center space-x-2 text-sm font-medium transition-all duration-200",o.liked?"text-red-600 hover:text-red-700":"text-gray-500 hover:text-red-600"])},[(n(),r("svg",{class:"w-6 h-6",fill:o.liked?"currentColor":"none",stroke:"currentColor",viewBox:"0 0 24 24"},t[88]||(t[88]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"},null,-1)]),8,Dr)),e("span",null,d(((bt=o.engagement_metrics)==null?void 0:bt.likes)||0),1)],10,Fr),e("button",{onClick:u=>Ye(o.id),class:"flex items-center space-x-2 text-sm font-medium text-gray-500 hover:text-blue-600 transition-all duration-200"},[t[89]||(t[89]=e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})],-1)),e("span",null,d(((wt=o.engagement_metrics)==null?void 0:wt.comments)||0),1)],8,Hr),e("button",{onClick:u=>is(o),class:"flex items-center space-x-2 text-sm font-medium text-gray-500 hover:text-purple-600 transition-all duration-200"},t[90]||(t[90]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"})],-1),e("span",null,"Share",-1)]),8,Nr),e("button",{onClick:u=>Ge(o),class:E(["flex items-center space-x-2 text-sm font-medium transition-all duration-200",o.saved?"text-green-600 hover:text-green-700":"text-gray-500 hover:text-green-600"])},[(n(),r("svg",{class:"w-6 h-6",fill:o.saved?"currentColor":"none",stroke:"currentColor",viewBox:"0 0 24 24"},t[91]||(t[91]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"},null,-1)]),8,Wr)),e("span",null,d(((kt=o.engagement_metrics)==null?void 0:kt.saves)||0),1)],10,Or)])])]),o.caption?(n(),r("div",qr,[e("div",Gr,[pe.value[o.id]?(n(),r("p",Kr,d(o.caption),1)):(n(),r("p",Yr,d(o.caption),1)),o.caption&&o.caption.length>150?(n(),r("button",{key:2,onClick:u=>Zt(o.id),class:"text-gray-500 hover:text-gray-700 text-sm font-medium mt-1 transition-colors"},d(pe.value[o.id]?"Show less":"Show more"),9,Jr)):i("",!0)]),o.source!=="instagram"&&o.health_topics&&o.health_topics.length>0?(n(),r("div",Qr,[(n(!0),r(z,null,P(ne.value[o.id]?o.health_topics:o.health_topics.slice(0,2),u=>(n(),r("span",{key:u,class:"px-3 py-1 bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 text-xs font-medium rounded-full"}," #"+d(u),1))),128)),o.health_topics.length>2&&!ne.value[o.id]?(n(),r("button",{key:0,onClick:u=>Ke(o.id),class:"px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-600 text-xs font-medium rounded-full transition-colors cursor-pointer"}," +"+d(o.health_topics.length-2)+" more ",9,Xr)):i("",!0),o.health_topics.length>2&&ne.value[o.id]?(n(),r("button",{key:1,onClick:u=>Ke(o.id),class:"px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-600 text-xs font-medium rounded-full transition-colors cursor-pointer"}," Show less ",8,Zr)):i("",!0)])):i("",!0)])):i("",!0),me.value[o.id]?(n(),r("div",en,[e("div",tn,[e("div",sn,[e("div",on,[e("img",{src:(_t=s.$page.props.auth.user)!=null&&_t.profile_image?s.$page.props.auth.user.profile_image.startsWith("http")?s.$page.props.auth.user.profile_image:`/storage/${s.$page.props.auth.user.profile_image}`:"/images/default-avatar.svg",alt:((Ct=s.$page.props.auth.user)==null?void 0:Ct.name)||"You",class:"w-full h-full object-cover"},null,8,rn)]),e("div",nn,[W(e("textarea",{"onUpdate:modelValue":t[13]||(t[13]=u=>N.content=u),onKeydown:It(ue(u=>Xe(o.id),["prevent"]),["enter"]),placeholder:"Add a comment...",class:"w-full p-3 border border-gray-200 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm",rows:"2"},null,40,ln),[[re,N.content]]),e("div",an,[e("button",{onClick:u=>Xe(o.id),disabled:!N.content.trim(),class:"px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"}," Comment ",8,dn)])])])]),e("div",cn,[(n(!0),r(z,null,P($.value[o.id],u=>{var H,Mt,jt,$t,St;return n(),r("div",{key:u.id,class:"space-y-3"},[e("div",un,[e("div",gn,[e("img",{src:(H=u.user)!=null&&H.profile_image?u.user.profile_image.startsWith("http")?u.user.profile_image:`/storage/${u.user.profile_image}`:"/images/default-avatar.svg",alt:((Mt=u.user)==null?void 0:Mt.name)||"Anonymous",class:"w-full h-full object-cover"},null,8,mn)]),e("div",pn,[e("div",hn,[e("div",vn,[e("p",fn,d(((jt=u.user)==null?void 0:jt.name)||"Anonymous"),1),e("div",xn,[e("p",yn,d(oe(u.created_at)),1),u.user_id===s.$page.props.auth.user.id?(n(),r("div",bn,[e("button",{onClick:b=>Ze(u.id),class:"p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors"},t[92]||(t[92]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"})],-1)]),8,wn),V.value[u.id]?(n(),r("div",kn,[e("button",{onClick:b=>{et(o.id,u.id),V.value[u.id]=!1},class:"w-full text-left px-3 py-1 text-sm text-red-600 hover:bg-red-50 transition-colors"}," Delete ",8,_n)])):i("",!0)])):i("",!0)])]),e("p",Cn,d(u.content),1),e("div",Mn,[e("button",{onClick:b=>tt(o.id,u.id),class:E(["flex items-center space-x-1 transition-colors",u.user_reaction==="like"?"text-red-600 hover:text-red-700":"text-gray-500 hover:text-red-600"])},[(n(),r("svg",{class:"w-4 h-4",fill:u.user_reaction==="like"?"currentColor":"none",stroke:"currentColor",viewBox:"0 0 24 24"},t[93]||(t[93]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"},null,-1)]),8,$n)),e("span",null,d((($t=u.reaction_counts)==null?void 0:$t.like)||0),1)],10,jn),e("button",{onClick:b=>os(u.id),class:"text-gray-500 hover:text-blue-600 transition-colors"}," Reply ",8,Sn)])]),K.value[u.id]?(n(),r("div",zn,[e("div",Ln,[W(e("textarea",{"onUpdate:modelValue":b=>D.value[u.id]=b,onKeydown:It(ue(b=>st(o.id,u.id),["prevent"]),["enter"]),placeholder:"Write a reply...",class:"flex-1 p-2 border border-gray-200 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm",rows:"2"},null,40,Pn),[[re,D.value[u.id]]]),e("div",An,[e("button",{onClick:b=>st(o.id,u.id),disabled:!((St=D.value[u.id])!=null&&St.trim()),class:"px-3 py-1 bg-blue-600 text-white text-xs font-medium rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"}," Reply ",8,Bn),e("button",{onClick:b=>rs(u.id),class:"px-3 py-1 bg-gray-200 text-gray-700 text-xs font-medium rounded-lg hover:bg-gray-300 transition-colors"}," Cancel ",8,Vn)])])])):i("",!0),u.replies&&u.replies.length>0?(n(),r("div",En,[(n(!0),r(z,null,P(u.replies,b=>{var zt,Lt,Pt,At;return n(),r("div",{key:b.id,class:"flex space-x-2"},[e("div",Un,[e("img",{src:(zt=b.user)!=null&&zt.profile_image?b.user.profile_image.startsWith("http")?b.user.profile_image:`/storage/${b.user.profile_image}`:"/images/default-avatar.svg",alt:((Lt=b.user)==null?void 0:Lt.name)||"Anonymous",class:"w-full h-full object-cover"},null,8,In)]),e("div",Tn,[e("div",Rn,[e("div",Fn,[e("p",Dn,d(((Pt=b.user)==null?void 0:Pt.name)||"Anonymous"),1),e("div",Hn,[e("p",Nn,d(oe(b.created_at)),1),b.user_id===s.$page.props.auth.user.id?(n(),r("div",On,[e("button",{onClick:Bt=>Ze(b.id),class:"p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors"},t[94]||(t[94]=[e("svg",{class:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"})],-1)]),8,Wn),V.value[b.id]?(n(),r("div",qn,[e("button",{onClick:Bt=>{et(o.id,b.id),V.value[b.id]=!1},class:"w-full text-left px-3 py-1 text-sm text-red-600 hover:bg-red-50 transition-colors"}," Delete ",8,Gn)])):i("",!0)])):i("",!0)])]),e("p",Yn,d(b.content),1),e("div",Kn,[e("button",{onClick:Bt=>tt(o.id,b.id),class:E(["flex items-center space-x-1 transition-colors",b.user_reaction==="like"?"text-red-600 hover:text-red-700":"text-gray-500 hover:text-red-600"])},[(n(),r("svg",{class:"w-3 h-3",fill:b.user_reaction==="like"?"currentColor":"none",stroke:"currentColor",viewBox:"0 0 24 24"},t[95]||(t[95]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"},null,-1)]),8,Qn)),e("span",null,d(((At=b.reaction_counts)==null?void 0:At.like)||0),1)],10,Jn)])])])])}),128))])):i("",!0)])])])}),128)),!$.value[o.id]||$.value[o.id].length===0?(n(),r("div",Xn,t[96]||(t[96]=[e("svg",{class:"w-8 h-8 text-gray-300 mx-auto mb-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})],-1),e("p",{class:"text-sm text-gray-500"},"No comments yet",-1)]))):i("",!0)])])):i("",!0)])}),128))])),Ae.value?(n(),r("div",{key:3,class:"text-center py-8",ref_key:"loadMoreTrigger",ref:Le},[g.loading?(n(),r("div",Zn,t[97]||(t[97]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),e("span",{class:"ml-3 text-gray-500"},"Loading more posts...",-1)]))):(n(),r("div",el," Scroll to load more posts "))],512)):g.posts.length>0?(n(),r("div",tl,t[98]||(t[98]=[e("div",{class:"text-gray-400 text-sm"}," 🎉 You've reached the end! No more posts to load. ",-1)]))):i("",!0)])]),e("div",sl,[e("div",ol,[e("button",{onClick:ns,class:"w-full bg-white rounded-2xl shadow-sm border border-gray-100 p-4 hover:bg-gray-50 transition-colors flex items-center justify-between"},[e("div",rl,[t[99]||(t[99]=e("svg",{class:"w-4 h-4 text-blue-600",fill:"currentColor",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"})],-1)),e("span",nl,"Saved "+d(le.value),1)]),t[100]||(t[100]=e("svg",{class:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1))]),c.connected&&c.account?(n(),r("div",ll,[t[101]||(t[101]=e("div",{class:"flex items-center space-x-2 mb-3"},[e("svg",{class:"w-4 h-4 text-pink-500",fill:"currentColor",viewBox:"0 0 24 24"},[e("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069z"})]),e("span",{class:"text-sm font-medium text-gray-900"},"Instagram")],-1)),e("div",al," @"+d(c.account.username),1),e("div",il,[e("button",{onClick:pt,disabled:c.syncing,class:"w-full px-3 py-2 bg-blue-600 text-white text-xs font-medium rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"},d(c.syncing?"Syncing...":"Sync Content"),9,dl),e("button",{onClick:mt,disabled:c.loading,class:"w-full px-3 py-2 bg-gray-200 text-gray-700 text-xs font-medium rounded-lg hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"}," Disconnect ",8,cl)])])):i("",!0)])])])])])),G.value?(n(),r("div",ul,[e("div",gl,[e("div",ml,[e("div",pl,[t[103]||(t[103]=e("h3",{class:"text-xl font-bold text-gray-900"},"Create Post",-1)),e("button",{onClick:t[14]||(t[14]=o=>G.value=!1),class:"text-gray-400 hover:text-gray-600 transition-colors"},t[102]||(t[102]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",hl,[e("div",null,[e("div",{class:"flex items-center justify-between mb-2"},[t[105]||(t[105]=e("label",{class:"block text-sm font-medium text-gray-700"},"What's on your mind?",-1)),e("button",{onClick:qt,class:"flex items-center space-x-2 px-3 py-1.5 text-sm text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors"},t[104]||(t[104]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})],-1),e("span",null,"Write with AI",-1)]))]),W(e("textarea",{"onUpdate:modelValue":t[15]||(t[15]=o=>m.caption=o),onInput:t[16]||(t[16]=o=>ae(o.target.value)),placeholder:"Share your health insights, tips, or experiences...",class:"w-full p-4 border border-gray-300 rounded-xl resize-none focus:ring-2 focus:ring-teal-500 focus:border-transparent",rows:"4"},null,544),[[re,m.caption]])]),m.suggestedHashtags.length>0?(n(),r("div",vl,[t[106]||(t[106]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Suggested Hashtags",-1)),e("div",fl,[(n(!0),r(z,null,P(m.suggestedHashtags,o=>(n(),r("button",{key:o,onClick:j=>Kt(o),disabled:m.hashtags.includes(o),class:E(["px-3 py-1 text-sm rounded-full border transition-colors",m.hashtags.includes(o)?"bg-teal-100 text-teal-700 border-teal-300 cursor-not-allowed":"bg-gray-100 text-gray-700 border-gray-300 hover:bg-teal-50 hover:border-teal-300 cursor-pointer"])},d(o),11,xl))),128))])])):i("",!0),m.hashtags.length>0?(n(),r("div",yl,[t[108]||(t[108]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Selected Hashtags",-1)),e("div",bl,[(n(!0),r(z,null,P(m.hashtags,o=>(n(),r("span",{key:o,class:"inline-flex items-center px-3 py-1 text-sm bg-teal-100 text-teal-700 rounded-full"},[B(d(o)+" ",1),e("button",{onClick:j=>Jt(o),class:"ml-2 w-4 h-4 text-teal-500 hover:text-teal-700"},t[107]||(t[107]=[e("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,wl)]))),128))])])):i("",!0),e("div",null,[t[111]||(t[111]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Add Image (Optional)",-1)),e("div",kl,[e("input",{type:"file",onChange:xs,accept:"image/*",class:"hidden",id:"post-media-input"},null,32),e("label",_l,[m.mediaPreview?(n(),r("div",Cl,[e("img",{src:m.mediaPreview,alt:"Preview",class:"max-h-32 mx-auto rounded-lg"},null,8,Ml)])):i("",!0),t[109]||(t[109]=e("svg",{class:"w-8 h-8 text-gray-400 mx-auto mb-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),t[110]||(t[110]=e("p",{class:"text-sm text-gray-500"},"Click to upload an image",-1))])])])]),e("div",jl,[e("button",{onClick:t[17]||(t[17]=o=>G.value=!1),class:"px-6 py-2 text-gray-700 font-medium rounded-lg hover:bg-gray-100 transition-colors"}," Cancel "),e("button",{onClick:Ot,disabled:!m.caption.trim(),class:"medroid-post-btn px-6 py-2 text-white font-medium rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"}," Share Post ",8,$l)])])])])):i("",!0),ce(l,{show:ge.value,imageFile:fe.value,onCropped:ys,onCancel:bs},null,8,["show","imageFile"]),ce(a,{show:$e.value,userId:Se.value,onClose:ks,onOpenPostDetail:_s},null,8,["show","userId"]),ze.value&&y.value?(n(),r("div",Sl,[e("div",zl,[e("div",{class:"fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75",onClick:ke}),e("div",Ll,[e("div",{class:"flex items-center justify-between mb-6"},[t[113]||(t[113]=e("h3",{class:"text-lg font-medium text-gray-900"},"Post Details",-1)),e("button",{onClick:ke,class:"text-gray-400 hover:text-gray-600 transition-colors"},t[112]||(t[112]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",Pl,[e("div",Al,[e("button",{onClick:t[18]||(t[18]=o=>{var j;se((j=y.value.user)==null?void 0:j.id),ke()}),class:"w-12 h-12 rounded-full overflow-hidden border-2 border-gray-200 hover:border-blue-400 transition-colors"},[e("img",{src:(k=y.value.user)!=null&&k.profile_image?y.value.user.profile_image.startsWith("http")?y.value.user.profile_image:`/storage/${y.value.user.profile_image}`:"/images/default-avatar.svg",alt:((p=y.value.user)==null?void 0:p.name)||"Anonymous",class:"w-full h-full object-cover"},null,8,Bl)]),e("div",null,[e("button",{onClick:t[19]||(t[19]=o=>{var j;se((j=y.value.user)==null?void 0:j.id),ke()}),class:"font-semibold text-gray-900 hover:text-blue-600 transition-colors text-left"},d(((M=y.value.user)==null?void 0:M.name)||"Anonymous"),1),e("p",Vl,d(oe(y.value.created_at)),1)])]),y.value.media_url?(n(),r("div",El,[e("img",{src:y.value.media_url,alt:y.value.caption,class:"w-full max-h-96 object-contain"},null,8,Ul)])):i("",!0),e("div",Il,[e("p",Tl,d(y.value.caption),1)]),(_=y.value.health_topics)!=null&&_.length?(n(),r("div",Rl,[(n(!0),r(z,null,P(y.value.health_topics,o=>(n(),r("span",{key:o,class:"px-3 py-1 bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 text-xs font-medium rounded-full"},d(o),1))),128))])):i("",!0),e("div",Fl,[e("div",Dl,[e("button",{onClick:t[20]||(t[20]=o=>qe(y.value)),class:E(["flex items-center space-x-2 text-sm font-medium transition-all duration-200",y.value.liked?"text-red-600 hover:text-red-700":"text-gray-500 hover:text-red-600"])},[(n(),r("svg",{class:"w-5 h-5",fill:y.value.liked?"currentColor":"none",stroke:"currentColor",viewBox:"0 0 24 24"},t[114]||(t[114]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"},null,-1)]),8,Hl)),e("span",null,d(((S=y.value.engagement_metrics)==null?void 0:S.likes)||0),1)],2),e("button",{onClick:t[21]||(t[21]=o=>Ye(y.value.id)),class:"flex items-center space-x-2 text-sm font-medium text-gray-500 hover:text-blue-600 transition-colors"},[t[115]||(t[115]=e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})],-1)),e("span",null,d(((O=y.value.engagement_metrics)==null?void 0:O.comments)||0),1)]),e("button",{onClick:t[22]||(t[22]=o=>Ge(y.value)),class:E(["flex items-center space-x-2 text-sm font-medium transition-colors",y.value.saved?"text-blue-600 hover:text-blue-700":"text-gray-500 hover:text-blue-600"])},[(n(),r("svg",{class:"w-5 h-5",fill:y.value.saved?"currentColor":"none",stroke:"currentColor",viewBox:"0 0 24 24"},t[116]||(t[116]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"},null,-1)]),8,Nl))],2)])])])])])])):i("",!0),ie.value&&ee.value&&te.value?(n(),r("div",Ol,[e("div",Wl,[e("button",{onClick:Ee,class:"absolute top-4 right-4 z-10 w-8 h-8 bg-black bg-opacity-50 rounded-full flex items-center justify-center text-white hover:bg-opacity-70 transition-colors"},t[117]||(t[117]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)])),e("div",ql,[(n(!0),r(z,null,P(R.value,(o,j)=>(n(),r("div",{key:o.id,class:"flex-1 h-1 bg-white bg-opacity-30 rounded-full overflow-hidden"},[e("div",{class:"h-full bg-white transition-all duration-100",style:Ut({width:j<L.value?"100%":j===L.value?`${de.value}%`:"0%"})},null,4)]))),128))]),e("div",Gl,[e("div",Yl,[e("img",{src:ee.value.user_avatar||"/images/default-avatar.svg",alt:ee.value.username,class:"w-full h-full object-cover"},null,8,Kl)]),e("div",null,[e("p",Jl,d(ee.value.username||"Anonymous"),1),e("p",Ql,d(oe(te.value.created_at)),1)])]),e("div",{class:"w-full h-full flex items-center justify-center bg-gray-900",onMousedown:rt,onMouseup:nt,onTouchstart:rt,onTouchend:nt},[e("img",{src:te.value.media_url,alt:te.value.caption||"Story",class:"w-full h-full object-contain",onError:t[23]||(t[23]=o=>o.target.src="/images/default-avatar.svg")},null,40,Xl)],32),te.value.caption?(n(),r("div",Zl,[e("div",ea,[e("p",ta,d(te.value.caption),1)])])):i("",!0),e("div",sa,[e("div",{onClick:at,class:"w-1/2 h-full cursor-pointer flex items-center justify-start pl-4"},[L.value>0?(n(),r("div",oa,t[118]||(t[118]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)]))):i("",!0)]),e("div",{onClick:Ue,class:"w-1/2 h-full cursor-pointer flex items-center justify-end pr-4"},[L.value<R.value.length-1?(n(),r("div",ra,t[119]||(t[119]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)]))):i("",!0)])])])])):i("",!0),Y.value?(n(),r("div",na,[e("div",la,[e("div",aa,[e("div",ia,[t[121]||(t[121]=e("h3",{class:"text-xl font-bold text-gray-900"},"Create Story",-1)),e("button",{onClick:t[24]||(t[24]=o=>Y.value=!1),class:"text-gray-400 hover:text-gray-600 transition-colors"},t[120]||(t[120]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",da,[e("div",null,[t[124]||(t[124]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Add Image/Video",-1)),C.mediaPreview?i("",!0):(n(),r("div",ca,[e("input",{type:"file",accept:"image/*,video/*",class:"hidden",id:"story-media-input",onChange:Ms},null,32),t[122]||(t[122]=e("label",{for:"story-media-input",class:"cursor-pointer"},[e("svg",{class:"w-12 h-12 text-gray-400 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})]),e("p",{class:"text-gray-500 mb-2"},"Click to upload media"),e("p",{class:"text-xs text-gray-400"},"Images and videos up to 10MB")],-1))])),C.mediaPreview?(n(),r("div",ua,[e("img",{src:C.mediaPreview,alt:"Story preview",class:"w-full h-64 object-cover rounded-xl"},null,8,ga),e("button",{onClick:it,class:"absolute top-2 right-2 w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors"},t[123]||(t[123]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])):i("",!0)]),e("div",null,[t[125]||(t[125]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Caption (Optional)",-1)),W(e("textarea",{"onUpdate:modelValue":t[25]||(t[25]=o=>C.caption=o),placeholder:"Add a caption to your story...",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",rows:"3",maxlength:"500"},null,512),[[re,C.caption]]),e("p",ma,d(C.caption.length)+"/500 characters",1)])]),e("div",pa,[e("button",{onClick:t[26]||(t[26]=o=>Y.value=!1),class:"px-6 py-2 text-gray-700 font-medium rounded-lg hover:bg-gray-100 transition-colors"}," Cancel "),e("button",{onClick:Cs,disabled:!C.media,class:"medroid-story-btn px-6 py-2 text-white font-medium rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"}," Share Story ",8,ha)])])])])):i("",!0),w.showAiModal?(n(),r("div",va,[e("div",fa,[e("div",xa,[e("div",{class:"flex items-center justify-between mb-6"},[t[127]||(t[127]=e("h3",{class:"text-xl font-semibold text-gray-900 flex items-center"},[e("svg",{class:"w-6 h-6 text-blue-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})]),B(" Write with AI ")],-1)),e("button",{onClick:Yt,class:"text-gray-400 hover:text-gray-600"},t[126]||(t[126]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",ya,[e("div",null,[t[128]||(t[128]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"What would you like to write about?",-1)),W(e("textarea",{"onUpdate:modelValue":t[27]||(t[27]=o=>w.prompt=o),placeholder:"e.g., 'Write a motivational post about staying healthy during winter' or 'Share tips for managing stress at work'",class:"w-full p-4 border border-gray-300 rounded-xl resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:"3"},null,512),[[re,w.prompt]])]),e("div",ba,[e("button",{onClick:Oe,disabled:!w.prompt.trim()||w.isGenerating,class:"flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"},[w.isGenerating?(n(),r("svg",ka,t[129]||(t[129]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):i("",!0),B(" "+d(w.isGenerating?"Generating...":"Generate Content"),1)],8,wa)]),w.generatedContent?(n(),r("div",_a,[e("div",null,[t[130]||(t[130]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Generated Content",-1)),e("div",Ca,[e("p",Ma,d(w.generatedContent),1)])]),e("div",ja,[e("button",{onClick:Gt,class:"flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"}," Use This Content "),e("button",{onClick:Oe,disabled:w.isGenerating,class:"flex-1 bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 disabled:opacity-50 transition-colors"}," Regenerate ",8,$a)])])):i("",!0)])])])])):i("",!0),Pe.value?(n(),r("div",Sa,[e("div",za,[e("div",La,[t[132]||(t[132]=e("h3",{class:"text-lg font-semibold text-gray-900"},"Notifications",-1)),e("div",Pa,[ve.value>0?(n(),r("button",{key:0,onClick:ts,class:"text-sm text-blue-600 hover:text-blue-700 font-medium"}," Mark all read ")):i("",!0),e("button",{onClick:t[28]||(t[28]=o=>Pe.value=!1),class:"text-gray-400 hover:text-gray-600 transition-colors"},t[131]||(t[131]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]),e("div",Aa,[I.value.length===0?(n(),r("div",Ba,t[133]||(t[133]=[e("svg",{class:"w-12 h-12 text-gray-300 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 17h5l-3.5-3.5a8.38 8.38 0 01-1.5-5V6a6 6 0 10-12 0v2.5a8.38 8.38 0 01-1.5 5L5 17h5m5 0v1a3 3 0 11-6 0v-1m6 0H9"})],-1),e("p",{class:"text-gray-500"},"No notifications yet",-1),e("p",{class:"text-sm text-gray-400 mt-1"},"Your activity will appear here",-1)]))):(n(),r("div",Va,[(n(!0),r(z,null,P(I.value,o=>(n(),r("div",{key:o.id,onClick:j=>es(o.id),class:E(["p-4 hover:bg-gray-50 cursor-pointer transition-colors",{"bg-blue-50":!o.read}])},[e("div",Ua,[e("div",Ia,[o.type==="like"?(n(),r("div",Ta,t[134]||(t[134]=[e("svg",{class:"w-4 h-4 text-red-600",fill:"currentColor",viewBox:"0 0 24 24"},[e("path",{d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})],-1)]))):o.type==="comment"?(n(),r("div",Ra,t[135]||(t[135]=[e("svg",{class:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})],-1)]))):o.type==="save"?(n(),r("div",Fa,t[136]||(t[136]=[e("svg",{class:"w-4 h-4 text-purple-600",fill:"currentColor",viewBox:"0 0 24 24"},[e("path",{d:"M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"})],-1)]))):o.type==="share"?(n(),r("div",Da,t[137]||(t[137]=[e("svg",{class:"w-4 h-4 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"})],-1)]))):i("",!0)]),e("div",Ha,[e("p",Na,d(o.message),1),e("p",Oa,d(o.post_title),1),e("p",Wa,d(oe(o.timestamp)),1)]),o.post_image?(n(),r("div",qa,[e("img",{src:o.post_image,alt:"Post",class:"w-10 h-10 rounded object-cover"},null,8,Ga)])):i("",!0),o.read?i("",!0):(n(),r("div",Ya,t[138]||(t[138]=[e("div",{class:"w-2 h-2 bg-blue-600 rounded-full"},null,-1)])))])],10,Ea))),128))]))])])])):i("",!0),e("div",Ka,[(n(!0),r(z,null,P(he.value,o=>(n(),r("div",{key:o.id,class:E(["max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden transform transition-all duration-300 ease-in-out",{"border-l-4 border-green-500":o.type==="success","border-l-4 border-red-500":o.type==="error","border-l-4 border-blue-500":o.type==="info","border-l-4 border-yellow-500":o.type==="warning"}])},[e("div",Ja,[e("div",Qa,[e("div",Xa,[o.type==="success"?(n(),r("svg",Za,t[139]||(t[139]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)]))):o.type==="error"?(n(),r("svg",ei,t[140]||(t[140]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)]))):o.type==="info"?(n(),r("svg",ti,t[141]||(t[141]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)]))):(n(),r("svg",si,t[142]||(t[142]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"},null,-1)])))]),e("div",oi,[e("p",ri,d(o.message),1)]),e("div",ni,[e("button",{onClick:j=>Je(o.id),class:"bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"},t[143]||(t[143]=[e("span",{class:"sr-only"},"Close",-1),e("svg",{class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),8,li)])])])],2))),128))]),f.show?(n(),r("div",ai,[e("div",ii,[e("div",{class:"flex items-center justify-between p-6 border-b border-gray-100"},[t[145]||(t[145]=e("h3",{class:"text-lg font-semibold text-gray-900"},"Share Post",-1)),e("button",{onClick:ds,class:"p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors"},t[144]||(t[144]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),f.post?(n(),r("div",di,[e("div",ci,[e("div",ui,[e("div",gi,[e("span",mi,d(f.post.source==="instagram"?"IG":((ft=(Me=f.post.user)==null?void 0:Me.name)==null?void 0:ft.charAt(0))||"U"),1)]),e("div",null,[e("p",pi,d(f.post.source==="instagram"?`@${f.post.instagram_username||f.post.username}`:((xt=f.post.user)==null?void 0:xt.name)||"Anonymous"),1),e("p",hi,d(f.post.source==="instagram"?"Instagram":"Medroid AI Community"),1)])]),f.post.media_url?(n(),r("div",vi,[e("img",{src:f.post.media_url,alt:f.post.caption||"Post image",class:"w-full h-32 object-cover rounded-lg"},null,8,fi)])):i("",!0),f.post.caption?(n(),r("p",xi,d(f.post.caption),1)):i("",!0)])])):i("",!0),e("div",{class:"p-6"},[t[151]||(t[151]=e("h4",{class:"text-sm font-medium text-gray-900 mb-4"},"Share via",-1)),e("div",{class:"grid grid-cols-2 gap-3 mb-4"},[e("button",{onClick:gs,class:"flex items-center justify-center space-x-3 p-4 bg-green-50 hover:bg-green-100 rounded-xl transition-colors group"},t[146]||(t[146]=[e("div",{class:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"currentColor",viewBox:"0 0 24 24"},[e("path",{d:"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.700"})])],-1),e("span",{class:"text-sm font-medium text-gray-700 group-hover:text-green-700"},"WhatsApp",-1)])),e("button",{onClick:ms,class:"flex items-center justify-center space-x-3 p-4 bg-blue-50 hover:bg-blue-100 rounded-xl transition-colors group"},t[147]||(t[147]=[e("div",{class:"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"currentColor",viewBox:"0 0 24 24"},[e("path",{d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})])],-1),e("span",{class:"text-sm font-medium text-gray-700 group-hover:text-blue-700"},"LinkedIn",-1)]))]),e("div",{class:"grid grid-cols-2 gap-3 mb-4"},[e("button",{onClick:ps,class:"flex items-center justify-center space-x-3 p-4 bg-gray-50 hover:bg-gray-100 rounded-xl transition-colors group"},t[148]||(t[148]=[e("div",{class:"w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})])],-1),e("span",{class:"text-sm font-medium text-gray-700 group-hover:text-gray-800"},"Email",-1)])),e("button",{onClick:hs,class:"flex items-center justify-center space-x-3 p-4 bg-gray-50 hover:bg-gray-100 rounded-xl transition-colors group"},t[149]||(t[149]=[e("div",{class:"w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a9.863 9.863 0 01-4.255-.949L5 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"})])],-1),e("span",{class:"text-sm font-medium text-gray-700 group-hover:text-gray-800"},"SMS",-1)]))]),e("button",{onClick:vs,class:"w-full flex items-center justify-center space-x-3 p-4 bg-purple-50 hover:bg-purple-100 rounded-xl transition-colors group mb-4"},t[150]||(t[150]=[e("svg",{class:"w-5 h-5 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})],-1),e("span",{class:"text-sm font-medium text-purple-700"},"Copy Link",-1)])),t[152]||(t[152]=e("div",{class:"text-center"},[e("button",{class:"text-sm text-gray-500 hover:text-gray-700 transition-colors"}," More sharing options ")],-1))])])])):i("",!0)]}),_:1})],64)}}},ji=Ds(yi,[["__scopeId","data-v-f87f74e5"]]);export{ji as default};
