<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ProductCategory;
use App\Models\Product;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class UpdateProductCategories extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'categories:update-products 
                            {--force : Force update even if products are assigned to categories}
                            {--backup : Create backup of existing categories}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Safely update product categories with new health-focused categories';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting product categories update...');

        // Check if it's safe to proceed
        if (!$this->option('force') && !$this->isSafeToUpdate()) {
            $this->error('Cannot update categories: Products are assigned to existing categories.');
            $this->info('Options:');
            $this->info('1. Use --force flag to proceed anyway (will unassign products from categories)');
            $this->info('2. Manually reassign products first');
            $this->info('3. Use --backup flag to create backup before updating');
            return 1;
        }

        // Create backup if requested
        if ($this->option('backup')) {
            $this->createBackup();
        }

        // Show warning for force option
        if ($this->option('force')) {
            $productsCount = Product::whereNotNull('category_id')->count();
            if ($productsCount > 0) {
                $this->warn("WARNING: {$productsCount} products will be unassigned from their categories!");
                if (!$this->confirm('Do you want to continue?')) {
                    $this->info('Operation cancelled.');
                    return 1;
                }
                
                // Unassign products from categories
                Product::whereNotNull('category_id')->update(['category_id' => null]);
                $this->info("Unassigned {$productsCount} products from categories.");
            }
        }

        // Clear existing categories
        $this->clearExistingCategories();

        // Create new categories
        $this->createNewCategories();

        $this->info('Product categories updated successfully!');
        $this->displayNewCategories();

        return 0;
    }

    /**
     * Check if it's safe to update categories.
     */
    private function isSafeToUpdate(): bool
    {
        $productsWithCategories = Product::whereNotNull('category_id')->count();
        
        if ($productsWithCategories > 0) {
            $this->warn("Found {$productsWithCategories} products assigned to existing categories.");
            return false;
        }
        
        return true;
    }

    /**
     * Create backup of existing categories.
     */
    private function createBackup(): void
    {
        $timestamp = now()->format('Y_m_d_H_i_s');
        $backupTable = "product_categories_backup_{$timestamp}";
        
        DB::statement("CREATE TABLE {$backupTable} AS SELECT * FROM product_categories");
        $this->info("Backup created: {$backupTable}");
    }

    /**
     * Clear existing product categories.
     */
    private function clearExistingCategories(): void
    {
        $existingCount = ProductCategory::count();
        
        if ($existingCount > 0) {
            ProductCategory::query()->delete();
            $this->info("Cleared {$existingCount} existing product categories.");
        } else {
            $this->info('No existing categories to clear.');
        }
    }

    /**
     * Create new health-focused categories.
     */
    private function createNewCategories(): void
    {
        $categories = [
            ['name' => 'Blood Tests', 'description' => 'Blood testing kits and related products', 'sort_order' => 1],
            ['name' => 'Heart Health', 'description' => 'Products for cardiovascular health and heart care', 'sort_order' => 2],
            ['name' => 'Medication', 'description' => 'Prescription and over-the-counter medications', 'sort_order' => 3],
            ['name' => 'Weight Loss', 'description' => 'Weight management and weight loss products', 'sort_order' => 4],
            ['name' => 'Sexual Health', 'description' => 'Sexual wellness and reproductive health products', 'sort_order' => 5],
            ['name' => 'Womens Health', 'description' => 'Health products specifically for women', 'sort_order' => 6],
            ['name' => 'Mens Health', 'description' => 'Health products specifically for men', 'sort_order' => 7],
            ['name' => 'Kids Health', 'description' => 'Health products for children and pediatric care', 'sort_order' => 8],
            ['name' => 'Beauty', 'description' => 'Beauty and cosmetic products', 'sort_order' => 9],
            ['name' => 'Wellness', 'description' => 'General wellness and lifestyle products', 'sort_order' => 10],
            ['name' => 'Supplements and Vitamins', 'description' => 'Nutritional supplements and vitamin products', 'sort_order' => 11],
            ['name' => 'Baby Care', 'description' => 'Products for infant and baby care', 'sort_order' => 12],
            ['name' => 'Diet and Nutrition', 'description' => 'Dietary products and nutrition supplements', 'sort_order' => 13],
            ['name' => 'Ear Care', 'description' => 'Products for ear health and hearing care', 'sort_order' => 14],
            ['name' => 'Eye Care', 'description' => 'Products for eye health and vision care', 'sort_order' => 15],
            ['name' => 'Health Remedies', 'description' => 'Natural and alternative health remedies', 'sort_order' => 16],
            ['name' => 'Skin Care', 'description' => 'Skincare and dermatological products', 'sort_order' => 17],
            ['name' => 'Hair Care', 'description' => 'Hair care and scalp health products', 'sort_order' => 18],
        ];

        foreach ($categories as $categoryData) {
            ProductCategory::create([
                'name' => $categoryData['name'],
                'slug' => Str::slug($categoryData['name']),
                'description' => $categoryData['description'],
                'parent_id' => null,
                'sort_order' => $categoryData['sort_order'],
                'is_active' => true,
            ]);
        }

        $this->info('Created ' . count($categories) . ' new product categories.');
    }

    /**
     * Display the new categories.
     */
    private function displayNewCategories(): void
    {
        $this->info('');
        $this->info('New Product Categories:');
        $this->info('======================');
        
        $categories = ProductCategory::orderBy('sort_order')->get();
        foreach ($categories as $category) {
            $this->info("• {$category->name} ({$category->slug})");
        }
        
        $this->info('');
    }
}
