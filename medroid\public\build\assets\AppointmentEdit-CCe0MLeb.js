import{z as V,c as h,r as w,B as j,d as r,e as i,f as _,u as A,m as B,g as x,i as t,j as E,l as g,n as c,v as f,t as l,q as P,x as T,F as S,p as D,y as M,P as $,W as q}from"./vendor-BhKTHoN5.js";import{_ as O}from"./AppLayout.vue_vue_type_script_setup_true_lang-DwGvFXKf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const R={class:"flex items-center justify-between"},I={class:"flex mt-2","aria-label":"Breadcrumb"},L={class:"inline-flex items-center space-x-1 md:space-x-3"},W={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},X={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},z={class:"py-12"},H={class:"mx-auto max-w-4xl sm:px-6 lg:px-8"},J={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},K={class:"p-6"},G={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Q={class:"space-y-4"},Y=["min"],Z={key:0,class:"mt-1 text-sm text-red-600"},tt={key:0,class:"mt-1 text-sm text-red-600"},et={key:0,class:"mt-1 text-sm text-red-600"},at={class:"space-y-4"},st={class:"mt-1 text-sm text-gray-900 dark:text-gray-100"},ot={class:"mt-1 text-sm text-gray-900 dark:text-gray-100"},nt={class:"mt-1 text-sm text-gray-900 dark:text-gray-100"},it={key:0,class:"mt-1 text-sm text-red-600"},rt={class:"mt-6 flex justify-end space-x-3"},dt=["disabled"],lt={key:0,class:"fas fa-spinner fa-spin mr-2"},mt={key:1,class:"fas fa-save mr-2"},xt={__name:"AppointmentEdit",props:{appointment:{type:Object,required:!0}},setup(p){var v,b,k;const m=p,C=V(),F=h(()=>{var o;return(o=C.props.auth)==null?void 0:o.user}),y=h(()=>{var n;const o=(n=F.value)==null?void 0:n.role;let e="/dashboard";return o==="provider"?e="/provider/appointments":o==="patient"?e="/patient/appointments":(o==="admin"||o==="manager"||o==="super_admin")&&(e="/manage/appointments"),[{title:"Dashboard",href:"/dashboard"},{title:"Appointments",href:e},{title:"Edit Appointment",href:"#"}]}),u=w(!1),s=j({date:m.appointment.date||((v=m.appointment.scheduled_at)==null?void 0:v.split("T")[0])||"",time:m.appointment.time||((k=(b=m.appointment.scheduled_at)==null?void 0:b.split("T")[1])==null?void 0:k.substring(0,5))||"",notes:m.appointment.notes||"",type:m.appointment.is_telemedicine?"telemedicine":"in-person"}),d=w({}),N=async()=>{var o;u.value=!0,d.value={};try{const e=(o=document.querySelector('meta[name="csrf-token"]'))==null?void 0:o.getAttribute("content"),n=await fetch(`/save-appointment/${m.appointment.id}`,{method:"PUT",headers:{Accept:"application/json","Content-Type":"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":e||""},credentials:"same-origin",body:JSON.stringify({date:s.date,time_slot:{start_time:s.time,end_time:s.time},notes:s.notes,is_telemedicine:s.type==="telemedicine"})}),a=await n.json().catch(()=>({}));n.ok?(alert("Appointment updated successfully."),q.visit(`/appointments/${m.appointment.id}`)):a.errors?d.value=a.errors:alert(a.message||"Failed to update appointment. Please try again.")}catch(e){console.error("Error updating appointment:",e),alert("Failed to update appointment. Please try again.")}finally{u.value=!1}},U=()=>{q.visit(`/appointments/${m.appointment.id}`)};return(o,e)=>(i(),r(S,null,[_(A(B),{title:"Edit Appointment"}),_(O,null,{header:x(()=>[t("div",R,[t("div",null,[e[5]||(e[5]=t("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Edit Appointment ",-1)),t("nav",I,[t("ol",L,[(i(!0),r(S,null,D(y.value,(n,a)=>(i(),r("li",{key:a,class:"inline-flex items-center"},[a<y.value.length-1?(i(),M(A($),{key:0,href:n.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:x(()=>[T(l(n.title),1)]),_:2},1032,["href"])):(i(),r("span",W,l(n.title),1)),a<y.value.length-1?(i(),r("svg",X,e[4]||(e[4]=[t("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):c("",!0)]))),128))])])])])]),default:x(()=>{var n;return[t("div",z,[t("div",H,[t("div",J,[t("div",K,[t("form",{onSubmit:E(N,["prevent"])},[t("div",G,[t("div",null,[e[10]||(e[10]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4"}," Appointment Details ",-1)),t("div",Q,[t("div",null,[e[6]||(e[6]=t("label",{for:"date",class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Date * ",-1)),g(t("input",{id:"date","onUpdate:modelValue":e[0]||(e[0]=a=>s.date=a),type:"date",min:new Date().toISOString().split("T")[0],class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",required:""},null,8,Y),[[f,s.date]]),d.value.date?(i(),r("p",Z,l(d.value.date[0]),1)):c("",!0)]),t("div",null,[e[7]||(e[7]=t("label",{for:"time",class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Time * ",-1)),g(t("input",{id:"time","onUpdate:modelValue":e[1]||(e[1]=a=>s.time=a),type:"time",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",required:""},null,512),[[f,s.time]]),d.value.time_slot?(i(),r("p",tt,l(d.value.time_slot[0]),1)):c("",!0)]),t("div",null,[e[9]||(e[9]=t("label",{for:"type",class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Type * ",-1)),g(t("select",{id:"type","onUpdate:modelValue":e[2]||(e[2]=a=>s.type=a),class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",required:""},e[8]||(e[8]=[t("option",{value:"telemedicine"},"Telemedicine",-1),t("option",{value:"in-person"},"In-Person",-1)]),512),[[P,s.type]]),d.value.is_telemedicine?(i(),r("p",et,l(d.value.is_telemedicine[0]),1)):c("",!0)])])]),t("div",null,[e[15]||(e[15]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4"}," Additional Information ",-1)),t("div",at,[t("div",null,[e[11]||(e[11]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Service",-1)),t("p",st,l(p.appointment.service||p.appointment.reason||"Consultation"),1)]),t("div",null,[e[12]||(e[12]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Provider",-1)),t("p",ot,l(p.appointment.provider_name||((n=p.appointment.provider)==null?void 0:n.name)||"N/A"),1)]),t("div",null,[e[13]||(e[13]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Amount",-1)),t("p",nt,"$"+l(typeof p.appointment.amount=="number"?p.appointment.amount.toFixed(2):parseFloat(p.appointment.amount||0).toFixed(2)),1)]),t("div",null,[e[14]||(e[14]=t("label",{for:"notes",class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Notes ",-1)),g(t("textarea",{id:"notes","onUpdate:modelValue":e[3]||(e[3]=a=>s.notes=a),rows:"4",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"Add any additional notes..."},null,512),[[f,s.notes]]),d.value.notes?(i(),r("p",it,l(d.value.notes[0]),1)):c("",!0)])])])]),t("div",rt,[t("button",{type:"button",onClick:U,class:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200"}," Cancel "),t("button",{type:"submit",disabled:u.value,class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"},[u.value?(i(),r("i",lt)):(i(),r("i",mt)),T(" "+l(u.value?"Updating...":"Update Appointment"),1)],8,dt)])],32)])])])])]}),_:1})],64))}};export{xt as default};
