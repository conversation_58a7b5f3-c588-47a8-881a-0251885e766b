import{J as ve,G as Zt,y as ke,e as T,g as ie,K as Te,u as G,r as U,c as $e,w as Ce,C as Z,o as dt,f as oe,M as Be,n as ee,E as en,a as te,d as M,i as r,j as Fe,t as K,F as ae,p as de,A as Q,l as ct,v as tn,x as Vt,I as nn,H as on,m as rn,q as pt}from"./vendor-BhKTHoN5.js";import{M as qe,_ as sn}from"./AppLayout.vue_vue_type_script_setup_true_lang-DwGvFXKf.js";import{C as an}from"./ChatInput-DdW2-31K.js";import{c as ln,u as cn,P as dn,a as un}from"./useForwardPropsEmits-DFe9BlYF.js";import{u as fn,a as ut,b as gn}from"./useForwardExpose-DjhPD9_V.js";import{P as ft}from"./Primitive-DSQomZit.js";import{_ as Kt}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./createLucideIcon-YxmScYOV.js";import"./index-CGRqDMLC.js";const[Yt,mn]=ln("CollapsibleRoot"),pn=ve({__name:"CollapsibleRoot",props:{defaultOpen:{type:Boolean,default:!1},open:{type:Boolean,default:void 0},disabled:{type:Boolean},unmountOnHide:{type:Boolean,default:!0},asChild:{type:Boolean},as:{}},emits:["update:open"],setup(a,{expose:o,emit:s}){const t=a,n=fn(t,"open",s,{defaultValue:t.defaultOpen,passive:t.open===void 0}),{disabled:i,unmountOnHide:l}=Zt(t);return mn({contentId:"",disabled:i,open:n,unmountOnHide:l,onOpenToggle:()=>{n.value=!n.value}}),o({open:n}),ut(),(c,y)=>(T(),ke(G(ft),{as:c.as,"as-child":t.asChild,"data-state":G(n)?"open":"closed","data-disabled":G(i)?"":void 0},{default:ie(()=>[Te(c.$slots,"default",{open:G(n)})]),_:3},8,["as","as-child","data-state","data-disabled"]))}}),hn=ve({inheritAttrs:!1,__name:"CollapsibleContent",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["contentFound"],setup(a,{emit:o}){const s=a,t=o,e=Yt();e.contentId||(e.contentId=cn(void 0,"reka-collapsible-content"));const n=U(),{forwardRef:i,currentElement:l}=ut(),c=U(0),y=U(0),x=$e(()=>e.open.value),b=U(x.value),m=U();Ce(()=>{var p;return[x.value,(p=n.value)==null?void 0:p.present]},async()=>{await Z();const p=l.value;if(!p)return;m.value=m.value||{transitionDuration:p.style.transitionDuration,animationName:p.style.animationName},p.style.transitionDuration="0s",p.style.animationName="none";const w=p.getBoundingClientRect();y.value=w.height,c.value=w.width,b.value||(p.style.transitionDuration=m.value.transitionDuration,p.style.animationName=m.value.animationName)},{immediate:!0});const g=$e(()=>b.value&&e.open.value);return dt(()=>{requestAnimationFrame(()=>{b.value=!1})}),gn(l,"beforematch",p=>{requestAnimationFrame(()=>{e.onOpenToggle(),t("contentFound")})}),(p,w)=>(T(),ke(G(dn),{ref_key:"presentRef",ref:n,present:p.forceMount||G(e).open.value,"force-mount":!0},{default:ie(({present:O})=>{var I;return[oe(G(ft),Be(p.$attrs,{id:G(e).contentId,ref:G(i),"as-child":s.asChild,as:p.as,hidden:O?void 0:G(e).unmountOnHide.value?"":"until-found","data-state":g.value?void 0:G(e).open.value?"open":"closed","data-disabled":(I=G(e).disabled)!=null&&I.value?"":void 0,style:{"--reka-collapsible-content-height":`${y.value}px`,"--reka-collapsible-content-width":`${c.value}px`}}),{default:ie(()=>[!G(e).unmountOnHide.value||O?Te(p.$slots,"default",{key:0}):ee("",!0)]),_:2},1040,["id","as-child","as","hidden","data-state","data-disabled","style"])]}),_:3},8,["present"]))}}),vn=ve({__name:"CollapsibleTrigger",props:{asChild:{type:Boolean},as:{default:"button"}},setup(a){const o=a;ut();const s=Yt();return(t,e)=>{var n,i;return T(),ke(G(ft),{type:t.as==="button"?"button":void 0,as:t.as,"as-child":o.asChild,"aria-controls":G(s).contentId,"aria-expanded":G(s).open.value,"data-state":G(s).open.value?"open":"closed","data-disabled":(n=G(s).disabled)!=null&&n.value?"":void 0,disabled:(i=G(s).disabled)==null?void 0:i.value,onClick:G(s).onOpenToggle},{default:ie(()=>[Te(t.$slots,"default")]),_:3},8,["type","as","as-child","aria-controls","aria-expanded","data-state","data-disabled","disabled","onClick"])}}});var _e={},je,ht;function yn(){return ht||(ht=1,je=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then}),je}var Ue={},me={},vt;function ye(){if(vt)return me;vt=1;let a;const o=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];return me.getSymbolSize=function(t){if(!t)throw new Error('"version" cannot be null or undefined');if(t<1||t>40)throw new Error('"version" should be in range from 1 to 40');return t*4+17},me.getSymbolTotalCodewords=function(t){return o[t]},me.getBCHDigit=function(s){let t=0;for(;s!==0;)t++,s>>>=1;return t},me.setToSJISFunction=function(t){if(typeof t!="function")throw new Error('"toSJISFunc" is not a valid function.');a=t},me.isKanjiModeEnabled=function(){return typeof a<"u"},me.toSJIS=function(t){return a(t)},me}var He={},yt;function gt(){return yt||(yt=1,function(a){a.L={bit:1},a.M={bit:0},a.Q={bit:3},a.H={bit:2};function o(s){if(typeof s!="string")throw new Error("Param is not a string");switch(s.toLowerCase()){case"l":case"low":return a.L;case"m":case"medium":return a.M;case"q":case"quartile":return a.Q;case"h":case"high":return a.H;default:throw new Error("Unknown EC Level: "+s)}}a.isValid=function(t){return t&&typeof t.bit<"u"&&t.bit>=0&&t.bit<4},a.from=function(t,e){if(a.isValid(t))return t;try{return o(t)}catch{return e}}}(He)),He}var ze,wt;function wn(){if(wt)return ze;wt=1;function a(){this.buffer=[],this.length=0}return a.prototype={get:function(o){const s=Math.floor(o/8);return(this.buffer[s]>>>7-o%8&1)===1},put:function(o,s){for(let t=0;t<s;t++)this.putBit((o>>>s-t-1&1)===1)},getLengthInBits:function(){return this.length},putBit:function(o){const s=Math.floor(this.length/8);this.buffer.length<=s&&this.buffer.push(0),o&&(this.buffer[s]|=128>>>this.length%8),this.length++}},ze=a,ze}var Oe,bt;function bn(){if(bt)return Oe;bt=1;function a(o){if(!o||o<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=o,this.data=new Uint8Array(o*o),this.reservedBit=new Uint8Array(o*o)}return a.prototype.set=function(o,s,t,e){const n=o*this.size+s;this.data[n]=t,e&&(this.reservedBit[n]=!0)},a.prototype.get=function(o,s){return this.data[o*this.size+s]},a.prototype.xor=function(o,s,t){this.data[o*this.size+s]^=t},a.prototype.isReserved=function(o,s){return this.reservedBit[o*this.size+s]},Oe=a,Oe}var Ve={},xt;function xn(){return xt||(xt=1,function(a){const o=ye().getSymbolSize;a.getRowColCoords=function(t){if(t===1)return[];const e=Math.floor(t/7)+2,n=o(t),i=n===145?26:Math.ceil((n-13)/(2*e-2))*2,l=[n-7];for(let c=1;c<e-1;c++)l[c]=l[c-1]-i;return l.push(6),l.reverse()},a.getPositions=function(t){const e=[],n=a.getRowColCoords(t),i=n.length;for(let l=0;l<i;l++)for(let c=0;c<i;c++)l===0&&c===0||l===0&&c===i-1||l===i-1&&c===0||e.push([n[l],n[c]]);return e}}(Ve)),Ve}var Ke={},_t;function _n(){if(_t)return Ke;_t=1;const a=ye().getSymbolSize,o=7;return Ke.getPositions=function(t){const e=a(t);return[[0,0],[e-o,0],[0,e-o]]},Ke}var Ye={},Ct;function Cn(){return Ct||(Ct=1,function(a){a.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const o={N1:3,N2:3,N3:40,N4:10};a.isValid=function(e){return e!=null&&e!==""&&!isNaN(e)&&e>=0&&e<=7},a.from=function(e){return a.isValid(e)?parseInt(e,10):void 0},a.getPenaltyN1=function(e){const n=e.size;let i=0,l=0,c=0,y=null,x=null;for(let b=0;b<n;b++){l=c=0,y=x=null;for(let m=0;m<n;m++){let g=e.get(b,m);g===y?l++:(l>=5&&(i+=o.N1+(l-5)),y=g,l=1),g=e.get(m,b),g===x?c++:(c>=5&&(i+=o.N1+(c-5)),x=g,c=1)}l>=5&&(i+=o.N1+(l-5)),c>=5&&(i+=o.N1+(c-5))}return i},a.getPenaltyN2=function(e){const n=e.size;let i=0;for(let l=0;l<n-1;l++)for(let c=0;c<n-1;c++){const y=e.get(l,c)+e.get(l,c+1)+e.get(l+1,c)+e.get(l+1,c+1);(y===4||y===0)&&i++}return i*o.N2},a.getPenaltyN3=function(e){const n=e.size;let i=0,l=0,c=0;for(let y=0;y<n;y++){l=c=0;for(let x=0;x<n;x++)l=l<<1&2047|e.get(y,x),x>=10&&(l===1488||l===93)&&i++,c=c<<1&2047|e.get(x,y),x>=10&&(c===1488||c===93)&&i++}return i*o.N3},a.getPenaltyN4=function(e){let n=0;const i=e.data.length;for(let c=0;c<i;c++)n+=e.data[c];return Math.abs(Math.ceil(n*100/i/5)-10)*o.N4};function s(t,e,n){switch(t){case a.Patterns.PATTERN000:return(e+n)%2===0;case a.Patterns.PATTERN001:return e%2===0;case a.Patterns.PATTERN010:return n%3===0;case a.Patterns.PATTERN011:return(e+n)%3===0;case a.Patterns.PATTERN100:return(Math.floor(e/2)+Math.floor(n/3))%2===0;case a.Patterns.PATTERN101:return e*n%2+e*n%3===0;case a.Patterns.PATTERN110:return(e*n%2+e*n%3)%2===0;case a.Patterns.PATTERN111:return(e*n%3+(e+n)%2)%2===0;default:throw new Error("bad maskPattern:"+t)}}a.applyMask=function(e,n){const i=n.size;for(let l=0;l<i;l++)for(let c=0;c<i;c++)n.isReserved(c,l)||n.xor(c,l,s(e,c,l))},a.getBestMask=function(e,n){const i=Object.keys(a.Patterns).length;let l=0,c=1/0;for(let y=0;y<i;y++){n(y),a.applyMask(y,e);const x=a.getPenaltyN1(e)+a.getPenaltyN2(e)+a.getPenaltyN3(e)+a.getPenaltyN4(e);a.applyMask(y,e),x<c&&(c=x,l=y)}return l}}(Ye)),Ye}var De={},kt;function Gt(){if(kt)return De;kt=1;const a=gt(),o=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],s=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];return De.getBlocksCount=function(e,n){switch(n){case a.L:return o[(e-1)*4+0];case a.M:return o[(e-1)*4+1];case a.Q:return o[(e-1)*4+2];case a.H:return o[(e-1)*4+3];default:return}},De.getTotalCodewordsCount=function(e,n){switch(n){case a.L:return s[(e-1)*4+0];case a.M:return s[(e-1)*4+1];case a.Q:return s[(e-1)*4+2];case a.H:return s[(e-1)*4+3];default:return}},De}var Ge={},Ae={},Tt;function kn(){if(Tt)return Ae;Tt=1;const a=new Uint8Array(512),o=new Uint8Array(256);return function(){let t=1;for(let e=0;e<255;e++)a[e]=t,o[t]=e,t<<=1,t&256&&(t^=285);for(let e=255;e<512;e++)a[e]=a[e-255]}(),Ae.log=function(t){if(t<1)throw new Error("log("+t+")");return o[t]},Ae.exp=function(t){return a[t]},Ae.mul=function(t,e){return t===0||e===0?0:a[o[t]+o[e]]},Ae}var Et;function Tn(){return Et||(Et=1,function(a){const o=kn();a.mul=function(t,e){const n=new Uint8Array(t.length+e.length-1);for(let i=0;i<t.length;i++)for(let l=0;l<e.length;l++)n[i+l]^=o.mul(t[i],e[l]);return n},a.mod=function(t,e){let n=new Uint8Array(t);for(;n.length-e.length>=0;){const i=n[0];for(let c=0;c<e.length;c++)n[c]^=o.mul(e[c],i);let l=0;for(;l<n.length&&n[l]===0;)l++;n=n.slice(l)}return n},a.generateECPolynomial=function(t){let e=new Uint8Array([1]);for(let n=0;n<t;n++)e=a.mul(e,new Uint8Array([1,o.exp(n)]));return e}}(Ge)),Ge}var Je,St;function En(){if(St)return Je;St=1;const a=Tn();function o(s){this.genPoly=void 0,this.degree=s,this.degree&&this.initialize(this.degree)}return o.prototype.initialize=function(t){this.degree=t,this.genPoly=a.generateECPolynomial(this.degree)},o.prototype.encode=function(t){if(!this.genPoly)throw new Error("Encoder not initialized");const e=new Uint8Array(t.length+this.degree);e.set(t);const n=a.mod(e,this.genPoly),i=this.degree-n.length;if(i>0){const l=new Uint8Array(this.degree);return l.set(n,i),l}return n},Je=o,Je}var Qe={},We={},Xe={},Mt;function Jt(){return Mt||(Mt=1,Xe.isValid=function(o){return!isNaN(o)&&o>=1&&o<=40}),Xe}var le={},At;function Qt(){if(At)return le;At=1;const a="[0-9]+",o="[A-Z $%*+\\-./:]+";let s="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";s=s.replace(/u/g,"\\u");const t="(?:(?![A-Z0-9 $%*+\\-./:]|"+s+`)(?:.|[\r
]))+`;le.KANJI=new RegExp(s,"g"),le.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),le.BYTE=new RegExp(t,"g"),le.NUMERIC=new RegExp(a,"g"),le.ALPHANUMERIC=new RegExp(o,"g");const e=new RegExp("^"+s+"$"),n=new RegExp("^"+a+"$"),i=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");return le.testKanji=function(c){return e.test(c)},le.testNumeric=function(c){return n.test(c)},le.testAlphanumeric=function(c){return i.test(c)},le}var $t;function we(){return $t||($t=1,function(a){const o=Jt(),s=Qt();a.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},a.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},a.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},a.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},a.MIXED={bit:-1},a.getCharCountIndicator=function(n,i){if(!n.ccBits)throw new Error("Invalid mode: "+n);if(!o.isValid(i))throw new Error("Invalid version: "+i);return i>=1&&i<10?n.ccBits[0]:i<27?n.ccBits[1]:n.ccBits[2]},a.getBestModeForData=function(n){return s.testNumeric(n)?a.NUMERIC:s.testAlphanumeric(n)?a.ALPHANUMERIC:s.testKanji(n)?a.KANJI:a.BYTE},a.toString=function(n){if(n&&n.id)return n.id;throw new Error("Invalid mode")},a.isValid=function(n){return n&&n.bit&&n.ccBits};function t(e){if(typeof e!="string")throw new Error("Param is not a string");switch(e.toLowerCase()){case"numeric":return a.NUMERIC;case"alphanumeric":return a.ALPHANUMERIC;case"kanji":return a.KANJI;case"byte":return a.BYTE;default:throw new Error("Unknown mode: "+e)}}a.from=function(n,i){if(a.isValid(n))return n;try{return t(n)}catch{return i}}}(We)),We}var It;function Sn(){return It||(It=1,function(a){const o=ye(),s=Gt(),t=gt(),e=we(),n=Jt(),i=7973,l=o.getBCHDigit(i);function c(m,g,p){for(let w=1;w<=40;w++)if(g<=a.getCapacity(w,p,m))return w}function y(m,g){return e.getCharCountIndicator(m,g)+4}function x(m,g){let p=0;return m.forEach(function(w){const O=y(w.mode,g);p+=O+w.getBitsLength()}),p}function b(m,g){for(let p=1;p<=40;p++)if(x(m,p)<=a.getCapacity(p,g,e.MIXED))return p}a.from=function(g,p){return n.isValid(g)?parseInt(g,10):p},a.getCapacity=function(g,p,w){if(!n.isValid(g))throw new Error("Invalid QR Code version");typeof w>"u"&&(w=e.BYTE);const O=o.getSymbolTotalCodewords(g),I=s.getTotalCodewordsCount(g,p),z=(O-I)*8;if(w===e.MIXED)return z;const E=z-y(w,g);switch(w){case e.NUMERIC:return Math.floor(E/10*3);case e.ALPHANUMERIC:return Math.floor(E/11*2);case e.KANJI:return Math.floor(E/13);case e.BYTE:default:return Math.floor(E/8)}},a.getBestVersionForData=function(g,p){let w;const O=t.from(p,t.M);if(Array.isArray(g)){if(g.length>1)return b(g,O);if(g.length===0)return 1;w=g[0]}else w=g;return c(w.mode,w.getLength(),O)},a.getEncodedBits=function(g){if(!n.isValid(g)||g<7)throw new Error("Invalid QR Code version");let p=g<<12;for(;o.getBCHDigit(p)-l>=0;)p^=i<<o.getBCHDigit(p)-l;return g<<12|p}}(Qe)),Qe}var Ze={},Rt;function Mn(){if(Rt)return Ze;Rt=1;const a=ye(),o=1335,s=21522,t=a.getBCHDigit(o);return Ze.getEncodedBits=function(n,i){const l=n.bit<<3|i;let c=l<<10;for(;a.getBCHDigit(c)-t>=0;)c^=o<<a.getBCHDigit(c)-t;return(l<<10|c)^s},Ze}var et={},tt,Dt;function An(){if(Dt)return tt;Dt=1;const a=we();function o(s){this.mode=a.NUMERIC,this.data=s.toString()}return o.getBitsLength=function(t){return 10*Math.floor(t/3)+(t%3?t%3*3+1:0)},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(t){let e,n,i;for(e=0;e+3<=this.data.length;e+=3)n=this.data.substr(e,3),i=parseInt(n,10),t.put(i,10);const l=this.data.length-e;l>0&&(n=this.data.substr(e),i=parseInt(n,10),t.put(i,l*3+1))},tt=o,tt}var nt,Bt;function $n(){if(Bt)return nt;Bt=1;const a=we(),o=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function s(t){this.mode=a.ALPHANUMERIC,this.data=t}return s.getBitsLength=function(e){return 11*Math.floor(e/2)+6*(e%2)},s.prototype.getLength=function(){return this.data.length},s.prototype.getBitsLength=function(){return s.getBitsLength(this.data.length)},s.prototype.write=function(e){let n;for(n=0;n+2<=this.data.length;n+=2){let i=o.indexOf(this.data[n])*45;i+=o.indexOf(this.data[n+1]),e.put(i,11)}this.data.length%2&&e.put(o.indexOf(this.data[n]),6)},nt=s,nt}var ot,Pt;function In(){if(Pt)return ot;Pt=1;const a=we();function o(s){this.mode=a.BYTE,typeof s=="string"?this.data=new TextEncoder().encode(s):this.data=new Uint8Array(s)}return o.getBitsLength=function(t){return t*8},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(s){for(let t=0,e=this.data.length;t<e;t++)s.put(this.data[t],8)},ot=o,ot}var rt,Nt;function Rn(){if(Nt)return rt;Nt=1;const a=we(),o=ye();function s(t){this.mode=a.KANJI,this.data=t}return s.getBitsLength=function(e){return e*13},s.prototype.getLength=function(){return this.data.length},s.prototype.getBitsLength=function(){return s.getBitsLength(this.data.length)},s.prototype.write=function(t){let e;for(e=0;e<this.data.length;e++){let n=o.toSJIS(this.data[e]);if(n>=33088&&n<=40956)n-=33088;else if(n>=57408&&n<=60351)n-=49472;else throw new Error("Invalid SJIS character: "+this.data[e]+`
Make sure your charset is UTF-8`);n=(n>>>8&255)*192+(n&255),t.put(n,13)}},rt=s,rt}var st={exports:{}},Lt;function Dn(){return Lt||(Lt=1,function(a){var o={single_source_shortest_paths:function(s,t,e){var n={},i={};i[t]=0;var l=o.PriorityQueue.make();l.push(t,0);for(var c,y,x,b,m,g,p,w,O;!l.empty();){c=l.pop(),y=c.value,b=c.cost,m=s[y]||{};for(x in m)m.hasOwnProperty(x)&&(g=m[x],p=b+g,w=i[x],O=typeof i[x]>"u",(O||w>p)&&(i[x]=p,l.push(x,p),n[x]=y))}if(typeof e<"u"&&typeof i[e]>"u"){var I=["Could not find a path from ",t," to ",e,"."].join("");throw new Error(I)}return n},extract_shortest_path_from_predecessor_list:function(s,t){for(var e=[],n=t;n;)e.push(n),s[n],n=s[n];return e.reverse(),e},find_path:function(s,t,e){var n=o.single_source_shortest_paths(s,t,e);return o.extract_shortest_path_from_predecessor_list(n,e)},PriorityQueue:{make:function(s){var t=o.PriorityQueue,e={},n;s=s||{};for(n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);return e.queue=[],e.sorter=s.sorter||t.default_sorter,e},default_sorter:function(s,t){return s.cost-t.cost},push:function(s,t){var e={value:s,cost:t};this.queue.push(e),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};a.exports=o}(st)),st.exports}var Ft;function Bn(){return Ft||(Ft=1,function(a){const o=we(),s=An(),t=$n(),e=In(),n=Rn(),i=Qt(),l=ye(),c=Dn();function y(I){return unescape(encodeURIComponent(I)).length}function x(I,z,E){const R=[];let J;for(;(J=I.exec(E))!==null;)R.push({data:J[0],index:J.index,mode:z,length:J[0].length});return R}function b(I){const z=x(i.NUMERIC,o.NUMERIC,I),E=x(i.ALPHANUMERIC,o.ALPHANUMERIC,I);let R,J;return l.isKanjiModeEnabled()?(R=x(i.BYTE,o.BYTE,I),J=x(i.KANJI,o.KANJI,I)):(R=x(i.BYTE_KANJI,o.BYTE,I),J=[]),z.concat(E,R,J).sort(function(P,D){return P.index-D.index}).map(function(P){return{data:P.data,mode:P.mode,length:P.length}})}function m(I,z){switch(z){case o.NUMERIC:return s.getBitsLength(I);case o.ALPHANUMERIC:return t.getBitsLength(I);case o.KANJI:return n.getBitsLength(I);case o.BYTE:return e.getBitsLength(I)}}function g(I){return I.reduce(function(z,E){const R=z.length-1>=0?z[z.length-1]:null;return R&&R.mode===E.mode?(z[z.length-1].data+=E.data,z):(z.push(E),z)},[])}function p(I){const z=[];for(let E=0;E<I.length;E++){const R=I[E];switch(R.mode){case o.NUMERIC:z.push([R,{data:R.data,mode:o.ALPHANUMERIC,length:R.length},{data:R.data,mode:o.BYTE,length:R.length}]);break;case o.ALPHANUMERIC:z.push([R,{data:R.data,mode:o.BYTE,length:R.length}]);break;case o.KANJI:z.push([R,{data:R.data,mode:o.BYTE,length:y(R.data)}]);break;case o.BYTE:z.push([{data:R.data,mode:o.BYTE,length:y(R.data)}])}}return z}function w(I,z){const E={},R={start:{}};let J=["start"];for(let S=0;S<I.length;S++){const P=I[S],D=[];for(let k=0;k<P.length;k++){const h=P[k],u=""+S+k;D.push(u),E[u]={node:h,lastCount:0},R[u]={};for(let v=0;v<J.length;v++){const A=J[v];E[A]&&E[A].node.mode===h.mode?(R[A][u]=m(E[A].lastCount+h.length,h.mode)-m(E[A].lastCount,h.mode),E[A].lastCount+=h.length):(E[A]&&(E[A].lastCount=h.length),R[A][u]=m(h.length,h.mode)+4+o.getCharCountIndicator(h.mode,z))}}J=D}for(let S=0;S<J.length;S++)R[J[S]].end=0;return{map:R,table:E}}function O(I,z){let E;const R=o.getBestModeForData(I);if(E=o.from(z,R),E!==o.BYTE&&E.bit<R.bit)throw new Error('"'+I+'" cannot be encoded with mode '+o.toString(E)+`.
 Suggested mode is: `+o.toString(R));switch(E===o.KANJI&&!l.isKanjiModeEnabled()&&(E=o.BYTE),E){case o.NUMERIC:return new s(I);case o.ALPHANUMERIC:return new t(I);case o.KANJI:return new n(I);case o.BYTE:return new e(I)}}a.fromArray=function(z){return z.reduce(function(E,R){return typeof R=="string"?E.push(O(R,null)):R.data&&E.push(O(R.data,R.mode)),E},[])},a.fromString=function(z,E){const R=b(z,l.isKanjiModeEnabled()),J=p(R),S=w(J,E),P=c.find_path(S.map,"start","end"),D=[];for(let k=1;k<P.length-1;k++)D.push(S.table[P[k]].node);return a.fromArray(g(D))},a.rawSplit=function(z){return a.fromArray(b(z,l.isKanjiModeEnabled()))}}(et)),et}var qt;function Pn(){if(qt)return Ue;qt=1;const a=ye(),o=gt(),s=wn(),t=bn(),e=xn(),n=_n(),i=Cn(),l=Gt(),c=En(),y=Sn(),x=Mn(),b=we(),m=Bn();function g(S,P){const D=S.size,k=n.getPositions(P);for(let h=0;h<k.length;h++){const u=k[h][0],v=k[h][1];for(let A=-1;A<=7;A++)if(!(u+A<=-1||D<=u+A))for(let L=-1;L<=7;L++)v+L<=-1||D<=v+L||(A>=0&&A<=6&&(L===0||L===6)||L>=0&&L<=6&&(A===0||A===6)||A>=2&&A<=4&&L>=2&&L<=4?S.set(u+A,v+L,!0,!0):S.set(u+A,v+L,!1,!0))}}function p(S){const P=S.size;for(let D=8;D<P-8;D++){const k=D%2===0;S.set(D,6,k,!0),S.set(6,D,k,!0)}}function w(S,P){const D=e.getPositions(P);for(let k=0;k<D.length;k++){const h=D[k][0],u=D[k][1];for(let v=-2;v<=2;v++)for(let A=-2;A<=2;A++)v===-2||v===2||A===-2||A===2||v===0&&A===0?S.set(h+v,u+A,!0,!0):S.set(h+v,u+A,!1,!0)}}function O(S,P){const D=S.size,k=y.getEncodedBits(P);let h,u,v;for(let A=0;A<18;A++)h=Math.floor(A/3),u=A%3+D-8-3,v=(k>>A&1)===1,S.set(h,u,v,!0),S.set(u,h,v,!0)}function I(S,P,D){const k=S.size,h=x.getEncodedBits(P,D);let u,v;for(u=0;u<15;u++)v=(h>>u&1)===1,u<6?S.set(u,8,v,!0):u<8?S.set(u+1,8,v,!0):S.set(k-15+u,8,v,!0),u<8?S.set(8,k-u-1,v,!0):u<9?S.set(8,15-u-1+1,v,!0):S.set(8,15-u-1,v,!0);S.set(k-8,8,1,!0)}function z(S,P){const D=S.size;let k=-1,h=D-1,u=7,v=0;for(let A=D-1;A>0;A-=2)for(A===6&&A--;;){for(let L=0;L<2;L++)if(!S.isReserved(h,A-L)){let re=!1;v<P.length&&(re=(P[v]>>>u&1)===1),S.set(h,A-L,re),u--,u===-1&&(v++,u=7)}if(h+=k,h<0||D<=h){h-=k,k=-k;break}}}function E(S,P,D){const k=new s;D.forEach(function(L){k.put(L.mode.bit,4),k.put(L.getLength(),b.getCharCountIndicator(L.mode,S)),L.write(k)});const h=a.getSymbolTotalCodewords(S),u=l.getTotalCodewordsCount(S,P),v=(h-u)*8;for(k.getLengthInBits()+4<=v&&k.put(0,4);k.getLengthInBits()%8!==0;)k.putBit(0);const A=(v-k.getLengthInBits())/8;for(let L=0;L<A;L++)k.put(L%2?17:236,8);return R(k,S,P)}function R(S,P,D){const k=a.getSymbolTotalCodewords(P),h=l.getTotalCodewordsCount(P,D),u=k-h,v=l.getBlocksCount(P,D),A=k%v,L=v-A,re=Math.floor(k/v),pe=Math.floor(u/v),Pe=pe+1,ne=re-pe,Ee=new c(ne);let Se=0;const be=new Array(v),Ie=new Array(v);let ue=0;const Ne=new Uint8Array(S.buffer);for(let f=0;f<v;f++){const C=f<L?pe:Pe;be[f]=Ne.slice(Se,Se+C),Ie[f]=Ee.encode(be[f]),Se+=C,ue=Math.max(ue,C)}const xe=new Uint8Array(k);let Re=0,_,d;for(_=0;_<ue;_++)for(d=0;d<v;d++)_<be[d].length&&(xe[Re++]=be[d][_]);for(_=0;_<ne;_++)for(d=0;d<v;d++)xe[Re++]=Ie[d][_];return xe}function J(S,P,D,k){let h;if(Array.isArray(S))h=m.fromArray(S);else if(typeof S=="string"){let re=P;if(!re){const pe=m.rawSplit(S);re=y.getBestVersionForData(pe,D)}h=m.fromString(S,re||40)}else throw new Error("Invalid data");const u=y.getBestVersionForData(h,D);if(!u)throw new Error("The amount of data is too big to be stored in a QR Code");if(!P)P=u;else if(P<u)throw new Error(`
The chosen QR Code version cannot contain this amount of data.
Minimum version required to store current data is: `+u+`.
`);const v=E(P,D,h),A=a.getSymbolSize(P),L=new t(A);return g(L,P),p(L),w(L,P),I(L,D,0),P>=7&&O(L,P),z(L,v),isNaN(k)&&(k=i.getBestMask(L,I.bind(null,L,D))),i.applyMask(k,L),I(L,D,k),{modules:L,version:P,errorCorrectionLevel:D,maskPattern:k,segments:h}}return Ue.create=function(P,D){if(typeof P>"u"||P==="")throw new Error("No input text");let k=o.M,h,u;return typeof D<"u"&&(k=o.from(D.errorCorrectionLevel,o.M),h=y.from(D.version),u=i.from(D.maskPattern),D.toSJISFunc&&a.setToSJISFunction(D.toSJISFunc)),J(P,h,k,u)},Ue}var at={},it={},jt;function Wt(){return jt||(jt=1,function(a){function o(s){if(typeof s=="number"&&(s=s.toString()),typeof s!="string")throw new Error("Color should be defined as hex string");let t=s.slice().replace("#","").split("");if(t.length<3||t.length===5||t.length>8)throw new Error("Invalid hex color: "+s);(t.length===3||t.length===4)&&(t=Array.prototype.concat.apply([],t.map(function(n){return[n,n]}))),t.length===6&&t.push("F","F");const e=parseInt(t.join(""),16);return{r:e>>24&255,g:e>>16&255,b:e>>8&255,a:e&255,hex:"#"+t.slice(0,6).join("")}}a.getOptions=function(t){t||(t={}),t.color||(t.color={});const e=typeof t.margin>"u"||t.margin===null||t.margin<0?4:t.margin,n=t.width&&t.width>=21?t.width:void 0,i=t.scale||4;return{width:n,scale:n?4:i,margin:e,color:{dark:o(t.color.dark||"#000000ff"),light:o(t.color.light||"#ffffffff")},type:t.type,rendererOpts:t.rendererOpts||{}}},a.getScale=function(t,e){return e.width&&e.width>=t+e.margin*2?e.width/(t+e.margin*2):e.scale},a.getImageWidth=function(t,e){const n=a.getScale(t,e);return Math.floor((t+e.margin*2)*n)},a.qrToImageData=function(t,e,n){const i=e.modules.size,l=e.modules.data,c=a.getScale(i,n),y=Math.floor((i+n.margin*2)*c),x=n.margin*c,b=[n.color.light,n.color.dark];for(let m=0;m<y;m++)for(let g=0;g<y;g++){let p=(m*y+g)*4,w=n.color.light;if(m>=x&&g>=x&&m<y-x&&g<y-x){const O=Math.floor((m-x)/c),I=Math.floor((g-x)/c);w=b[l[O*i+I]?1:0]}t[p++]=w.r,t[p++]=w.g,t[p++]=w.b,t[p]=w.a}}}(it)),it}var Ut;function Nn(){return Ut||(Ut=1,function(a){const o=Wt();function s(e,n,i){e.clearRect(0,0,n.width,n.height),n.style||(n.style={}),n.height=i,n.width=i,n.style.height=i+"px",n.style.width=i+"px"}function t(){try{return document.createElement("canvas")}catch{throw new Error("You need to specify a canvas element")}}a.render=function(n,i,l){let c=l,y=i;typeof c>"u"&&(!i||!i.getContext)&&(c=i,i=void 0),i||(y=t()),c=o.getOptions(c);const x=o.getImageWidth(n.modules.size,c),b=y.getContext("2d"),m=b.createImageData(x,x);return o.qrToImageData(m.data,n,c),s(b,y,x),b.putImageData(m,0,0),y},a.renderToDataURL=function(n,i,l){let c=l;typeof c>"u"&&(!i||!i.getContext)&&(c=i,i=void 0),c||(c={});const y=a.render(n,i,c),x=c.type||"image/png",b=c.rendererOpts||{};return y.toDataURL(x,b.quality)}}(at)),at}var lt={},Ht;function Ln(){if(Ht)return lt;Ht=1;const a=Wt();function o(e,n){const i=e.a/255,l=n+'="'+e.hex+'"';return i<1?l+" "+n+'-opacity="'+i.toFixed(2).slice(1)+'"':l}function s(e,n,i){let l=e+n;return typeof i<"u"&&(l+=" "+i),l}function t(e,n,i){let l="",c=0,y=!1,x=0;for(let b=0;b<e.length;b++){const m=Math.floor(b%n),g=Math.floor(b/n);!m&&!y&&(y=!0),e[b]?(x++,b>0&&m>0&&e[b-1]||(l+=y?s("M",m+i,.5+g+i):s("m",c,0),c=0,y=!1),m+1<n&&e[b+1]||(l+=s("h",x),x=0)):c++}return l}return lt.render=function(n,i,l){const c=a.getOptions(i),y=n.modules.size,x=n.modules.data,b=y+c.margin*2,m=c.color.light.a?"<path "+o(c.color.light,"fill")+' d="M0 0h'+b+"v"+b+'H0z"/>':"",g="<path "+o(c.color.dark,"stroke")+' d="'+t(x,y,c.margin)+'"/>',p='viewBox="0 0 '+b+" "+b+'"',O='<svg xmlns="http://www.w3.org/2000/svg" '+(c.width?'width="'+c.width+'" height="'+c.width+'" ':"")+p+' shape-rendering="crispEdges">'+m+g+`</svg>
`;return typeof l=="function"&&l(null,O),O},lt}var zt;function Fn(){if(zt)return _e;zt=1;const a=yn(),o=Pn(),s=Nn(),t=Ln();function e(n,i,l,c,y){const x=[].slice.call(arguments,1),b=x.length,m=typeof x[b-1]=="function";if(!m&&!a())throw new Error("Callback required as last argument");if(m){if(b<2)throw new Error("Too few arguments provided");b===2?(y=l,l=i,i=c=void 0):b===3&&(i.getContext&&typeof y>"u"?(y=c,c=void 0):(y=c,c=l,l=i,i=void 0))}else{if(b<1)throw new Error("Too few arguments provided");return b===1?(l=i,i=c=void 0):b===2&&!i.getContext&&(c=l,l=i,i=void 0),new Promise(function(g,p){try{const w=o.create(l,c);g(n(w,i,c))}catch(w){p(w)}})}try{const g=o.create(l,c);y(null,n(g,i,c))}catch(g){y(g)}}return _e.create=o.create,_e.toCanvas=e.bind(null,s.render),_e.toDataURL=e.bind(null,s.renderToDataURL),_e.toString=e.bind(null,function(n,i,l){return t.render(n,l)}),_e}var qn=Fn();const jn=en(qn);function Un(a={}){const o=U(null),s=U(!1),t=U(null),{showErrorAlert:e=!1,retryOnCsrfError:n=!0}=a,i=async g=>{s.value=!0,t.value=null;try{const p=await te.get(g);return o.value=p.data,p.data}catch(p){const w=x(p);return t.value=w,e&&alert(w),null}finally{s.value=!1}},l=async(g,p={})=>{s.value=!0,t.value=null;try{const w=await te.post(g,p);return o.value=w.data,w.data}catch(w){const O=x(w);return t.value=O,e&&!g.includes("/logout")&&alert(O),g.includes("/logout")?{message:"Logged out successfully"}:null}finally{s.value=!1}},c=async(g,p={})=>{s.value=!0,t.value=null;try{const w=await te.put(g,p);return o.value=w.data,w.data}catch(w){const O=x(w);return t.value=O,e&&alert(O),null}finally{s.value=!1}},y=async g=>{s.value=!0,t.value=null;try{return await te.delete(g),!0}catch(p){const w=x(p);return t.value=w,e&&alert(w),!1}finally{s.value=!1}},x=g=>{if(console.error("API Error:",g),g.response){const p=g.response.status,w=g.response.data;switch(p){case 401:return"Authentication required. Please log in again.";case 403:return"You do not have permission to perform this action.";case 404:return"The requested resource was not found.";case 419:return"Security token expired. Please refresh the page and try again.";case 422:if(w!=null&&w.errors){const O=Object.values(w.errors)[0];return Array.isArray(O)?O[0]:String(O)}return(w==null?void 0:w.message)||"Validation failed.";case 429:return"Too many requests. Please wait a moment and try again.";case 500:return"Server error. Please try again later.";default:return(w==null?void 0:w.message)||`Server error: ${p}`}}else return g.request?"Network error. Please check your connection and try again.":"An unexpected error occurred. Please try again."};return{data:o,loading:s,error:t,get:i,post:l,put:c,delete:y,reset:()=>{o.value=null,s.value=!1,t.value=null},clearError:()=>{t.value=null},handleError:x}}function Hn(){const a=Un();return{...a,getReferralCode:()=>a.get("/referrals-code"),getReferralStats:()=>a.get("/referrals-my"),sendInvitation:n=>a.post("/send-referral-invite",{email:n}),getCreditBalance:()=>a.get("/credits-balance")}}const zn={key:0,class:"fixed inset-0 z-50 overflow-y-auto"},On={class:"flex items-center justify-between mb-6"},Vn={class:"flex items-center space-x-3"},Kn={class:"text-sm text-gray-500"},Yn={key:0,class:"text-center py-8"},Gn={key:1,class:"space-y-6"},Jn={class:"flex space-x-1 bg-gray-100 rounded-lg p-1"},Qn=["onClick"],Wn={key:0,class:"space-y-4"},Xn={class:"bg-green-50 border border-green-200 rounded-lg p-4"},Zn={class:"flex items-center space-x-2"},eo=["value"],to=["disabled"],no={class:"flex items-center space-x-2"},oo=["value"],ro=["disabled"],so={key:0,class:"p-3 bg-green-100 border border-green-400 text-green-700 rounded-md text-center"},ao={class:"text-center"},io={class:"inline-block p-4 bg-white border-2 border-gray-200 rounded-lg"},lo={key:1,class:"space-y-4"},co=["disabled"],uo={key:0,class:"flex items-center justify-center"},fo={key:1},go={key:0,class:"p-3 bg-green-100 border border-green-400 text-green-700 rounded-md"},mo={key:1,class:"p-3 bg-red-100 border border-red-400 text-red-700 rounded-md"},po={key:2,class:"space-y-4"},ho={class:"bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-lg p-4"},vo={class:"text-center"},yo={class:"text-3xl font-bold text-purple-600"},wo={class:"text-xs text-purple-600 mt-1"},bo={class:"grid grid-cols-2 gap-4"},xo={class:"bg-blue-50 border border-blue-200 rounded-lg p-4 text-center"},_o={class:"text-2xl font-bold text-blue-600"},Co={class:"bg-green-50 border border-green-200 rounded-lg p-4 text-center"},ko={class:"text-2xl font-bold text-green-600"},To={class:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center"},Eo={class:"text-2xl font-bold text-yellow-600"},So={key:0},Mo={class:"space-y-3"},Ao={class:"flex items-center justify-between mb-2"},$o={class:"flex-1"},Io={class:"text-sm font-medium text-gray-900"},Ro={class:"text-xs text-gray-500"},Do={class:"flex items-center justify-between text-xs"},Bo={class:"text-gray-500"},Po={key:0,class:"text-green-600"},No={key:1,class:"text-center py-8"},Lo={__name:"ReferralModal",props:{isOpen:{type:Boolean,default:!1}},emits:["close"],setup(a,{emit:o}){const s=a,t=o,e=Hn(),n=U(!1),i=U("share"),l=U({}),c=U({}),y=U({balance:0,total_earned:0,total_used:0,referral_earnings:0,admin_credits:0}),x=U(""),b=U(!1),m=U(!1),g=U(""),p=U(""),w=U(null),O=[{id:"share",name:"Share"},{id:"invite",name:"Invite"},{id:"stats",name:"Stats"}],I=()=>{t("close")},z=async()=>{try{const h=await te.get("/credits-balance");y.value=h.data}catch(h){console.error("Error loading credit balance:",h),y.value={balance:0,total_earned:0,total_used:0,referral_earnings:0,admin_credits:0}}},E=async()=>{n.value=!0;try{console.log("Loading referral data..."),await z(),l.value={referral_code:"DEMO123",referral_url:"https://medroid.ai/register?ref=DEMO123"},c.value={total_count:5,completed_count:3,pending_count:2,total_earnings:15,referrals:[{id:1,email:"<EMAIL>",status:"completed",created_at:new Date().toISOString(),earnings:3},{id:2,email:"<EMAIL>",status:"pending",created_at:new Date().toISOString(),earnings:0}]};try{const h=await te.get("/referrals-code");console.log("Referral code response:",h.data),h.data&&(l.value=h.data);const u=await te.get("/referrals-my");console.log("Referral stats response:",u.data),u.data&&(c.value=u.data)}catch(h){console.log("API call failed, using demo data:",h.message)}await Z(),l.value.referral_url&&(console.log("Calling generateQRCode from loadReferralData"),await R())}catch(h){console.error("Error in loadReferralData:",h)}finally{n.value=!1}},R=async()=>{if(console.log("generateQRCode called",{canvasExists:!!w.value,referralUrl:l.value.referral_url}),!l.value.referral_url){console.log("No referral URL available for QR code generation");return}if(await Z(),!w.value){console.log("Canvas element not available, retrying..."),setTimeout(async()=>{await R()},100);return}try{const h=w.value;console.log("Canvas element found, generating QR code..."),h.getContext("2d").clearRect(0,0,h.width,h.height),h.width=128,h.height=128,await jn.toCanvas(h,l.value.referral_url,{width:128,margin:1,color:{dark:"#000000",light:"#FFFFFF"}}),console.log("QR code generated successfully")}catch(h){console.error("Error generating QR code:",h);const u=w.value;if(u){const v=u.getContext("2d");u.width=128,u.height=128,v.fillStyle="#f3f4f6",v.fillRect(0,0,128,128),v.fillStyle="#6b7280",v.font="12px Arial",v.textAlign="center",v.fillText("QR Code",64,64),v.fillText("Error",64,80)}}},J=async(h,u)=>{if(h)try{if(await navigator.clipboard.writeText(h),u&&u.target){const v=u.target.closest("button");if(v){const A=v.innerHTML;v.innerHTML='<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>',setTimeout(()=>{v.innerHTML=A},1e3)}}p.value="Copied to clipboard!",setTimeout(()=>{p.value=""},3e3)}catch(v){console.error("Failed to copy:",v);const A=document.createElement("textarea");A.value=h,document.body.appendChild(A),A.select();try{document.execCommand("copy"),p.value="Copied to clipboard!",setTimeout(()=>{p.value=""},3e3)}catch(L){console.error("Fallback copy failed:",L),p.value="Failed to copy",setTimeout(()=>{p.value=""},3e3)}document.body.removeChild(A)}},S=()=>{const h=`🤖 Meet your new AI Doctor!
Get instant medical advice 24/7 with Medroid - powered by advanced medical AI that understands symptoms, suggests treatments, and provides evidence-based care guidance.
Download now with my code: ${l.value.referral_code} 👉 ${l.value.referral_url}
Your pocket-sized doctor is here! 👩‍⚕️📱`,u=`https://wa.me/?text=${encodeURIComponent(h)}`;window.open(u,"_blank")},P=()=>{const h="Meet your new AI Doctor - Medroid",u=`🤖 Meet your new AI Doctor!

Get instant medical advice 24/7 with Medroid - powered by advanced medical AI that understands symptoms, suggests treatments, and provides evidence-based care guidance.

Download now with my code: ${l.value.referral_code} 👉 ${l.value.referral_url}

Your pocket-sized doctor is here! 👩‍⚕️📱

Best regards!`,v=`mailto:?subject=${encodeURIComponent(h)}&body=${encodeURIComponent(u)}`;window.location.href=v},D=async h=>{if(h&&h.preventDefault(),!!x.value){b.value=!0,m.value=!1,g.value="";try{console.log("Sending invitation to:",x.value);const u=await e.sendInvitation(x.value);if(u){console.log("Invitation response:",u),m.value=!0,x.value="";try{const v=await e.getReferralStats();v&&(c.value=v),await z()}catch(v){console.log("Failed to reload stats:",v)}}else g.value=e.error.value||"Failed to send invitation. Please try again."}catch(u){console.error("Unexpected error sending invitation:",u),g.value="An unexpected error occurred. Please try again."}finally{b.value=!1}}},k=h=>{if(!h)return"";try{return new Date(h).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}catch{return h}};return Ce(()=>s.isOpen,async h=>{h&&(E(),setTimeout(async()=>{l.value.referral_url&&w.value&&w.value.getContext("2d").getImageData(0,0,w.value.width,w.value.height).data.every(L=>L===0)&&(console.log("Fallback QR code generation triggered - canvas is empty"),await R())},500))}),Ce(()=>l.value.referral_url,async h=>{h&&s.isOpen&&(console.log("Referral URL changed, regenerating QR code:",h),await Z(),await R())}),Ce(i,()=>{m.value=!1,g.value="",p.value=""}),(h,u)=>a.isOpen?(T(),M("div",zn,[r("div",{class:"fixed inset-0 bg-white bg-opacity-20 backdrop-blur-sm transition-opacity",onClick:Fe(I,["self"])}),r("div",{class:"flex min-h-full items-center justify-center p-4",onClick:Fe(I,["self"])},[r("div",{class:"relative w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",onClick:u[3]||(u[3]=Fe(()=>{},["stop"]))},[r("div",On,[r("div",Vn,[u[5]||(u[5]=r("div",{class:"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center"},[r("svg",{class:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])],-1)),r("div",null,[u[4]||(u[4]=r("h3",{class:"text-lg font-semibold text-gray-900"},"Refer & Earn",-1)),r("p",Kn," Total Balance: $"+K(parseFloat(y.value.balance||0).toFixed(2))+" | From Referrals: $"+K(parseFloat(y.value.referral_earnings||0).toFixed(2)),1)])]),r("button",{onClick:I,class:"text-gray-400 hover:text-gray-600"},u[6]||(u[6]=[r("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),n.value?(T(),M("div",Yn,u[7]||(u[7]=[r("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"},null,-1),r("p",{class:"text-gray-600"},"Loading your referral details...",-1)]))):(T(),M("div",Gn,[r("div",Jn,[(T(),M(ae,null,de(O,v=>r("button",{key:v.id,onClick:A=>i.value=v.id,class:Q(["flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors",i.value===v.id?"bg-white text-green-600 shadow-sm":"text-gray-600 hover:text-gray-900"])},K(v.name),11,Qn)),64))]),i.value==="share"?(T(),M("div",Wn,[r("div",Xn,[u[9]||(u[9]=r("label",{class:"block text-sm font-medium text-green-800 mb-2"},"Your Referral Code",-1)),r("div",Zn,[r("input",{value:l.value.referral_code||"Loading...",readonly:"",class:Q(["flex-1 px-3 py-2 bg-white border border-green-300 rounded-md text-lg font-mono text-center",{"text-gray-400":!l.value.referral_code}])},null,10,eo),r("button",{onClick:u[0]||(u[0]=v=>J(l.value.referral_code,v)),disabled:!l.value.referral_code,class:"px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Copy code"},u[8]||(u[8]=[r("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})],-1)]),8,to)])]),r("div",null,[u[11]||(u[11]=r("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Referral Link",-1)),r("div",no,[r("input",{value:l.value.referral_url||"Loading...",readonly:"",class:Q(["flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm",{"text-gray-400":!l.value.referral_url}])},null,10,oo),r("button",{onClick:u[1]||(u[1]=v=>J(l.value.referral_url,v)),disabled:!l.value.referral_url,class:"px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Copy link"},u[10]||(u[10]=[r("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})],-1)]),8,ro)])]),p.value?(T(),M("div",so,K(p.value),1)):ee("",!0),r("div",ao,[u[12]||(u[12]=r("label",{class:"block text-sm font-medium text-gray-700 mb-3"},"QR Code",-1)),r("div",io,[r("canvas",{ref_key:"qrCanvas",ref:w,class:"w-32 h-32"},null,512)]),u[13]||(u[13]=r("p",{class:"text-xs text-gray-500 mt-2"},"Share this QR code with friends",-1))]),r("div",{class:"grid grid-cols-2 gap-3"},[r("button",{onClick:S,class:"flex items-center justify-center space-x-2 px-4 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"},u[14]||(u[14]=[r("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24"},[r("path",{d:"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"})],-1),r("span",{class:"text-sm font-medium"},"WhatsApp",-1)])),r("button",{onClick:P,class:"flex items-center justify-center space-x-2 px-4 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"},u[15]||(u[15]=[r("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})],-1),r("span",{class:"text-sm font-medium"},"Email",-1)]))])])):ee("",!0),i.value==="invite"?(T(),M("div",lo,[r("form",{onSubmit:D,class:"space-y-4"},[r("div",null,[u[16]||(u[16]=r("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Friend's Email",-1)),ct(r("input",{"onUpdate:modelValue":u[2]||(u[2]=v=>x.value=v),type:"email",required:"",placeholder:"Enter your friend's email",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"},null,512),[[tn,x.value]])]),r("button",{type:"submit",disabled:b.value||!x.value,class:"w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"},[b.value?(T(),M("span",uo,u[17]||(u[17]=[r("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"},null,-1),Vt(" Sending... ")]))):(T(),M("span",fo,"Send Invitation"))],8,co)],32),m.value?(T(),M("div",go," Invitation sent successfully! ")):ee("",!0),g.value?(T(),M("div",mo,K(g.value),1)):ee("",!0)])):ee("",!0),i.value==="stats"?(T(),M("div",po,[r("div",ho,[r("div",vo,[r("div",yo,"$"+K(parseFloat(y.value.balance||0).toFixed(2)),1),u[18]||(u[18]=r("div",{class:"text-sm text-purple-800 font-medium"},"Available Credit Balance",-1)),r("div",wo," Total Earned: $"+K(parseFloat(y.value.total_earned||0).toFixed(2))+" | Used: $"+K(parseFloat(y.value.total_used||0).toFixed(2)),1)])]),r("div",bo,[r("div",xo,[r("div",_o,K(c.value.total_count||0),1),u[19]||(u[19]=r("div",{class:"text-sm text-blue-800"},"Total Referrals",-1))]),r("div",Co,[r("div",ko,K(c.value.completed_count||0),1),u[20]||(u[20]=r("div",{class:"text-sm text-green-800"},"Completed",-1))])]),r("div",To,[r("div",Eo,"$"+K(parseFloat(y.value.referral_earnings||0).toFixed(2)),1),u[21]||(u[21]=r("div",{class:"text-sm text-yellow-800"},"Referral Earnings",-1))]),c.value.referrals&&c.value.referrals.length>0?(T(),M("div",So,[u[22]||(u[22]=r("h4",{class:"text-sm font-medium text-gray-700 mb-3"},"Recent Referrals",-1)),r("div",Mo,[(T(!0),M(ae,null,de(c.value.referrals.slice(0,5),v=>(T(),M("div",{key:v.id,class:"p-3 bg-gray-50 rounded-lg border"},[r("div",Ao,[r("div",$o,[r("div",Io,K(v.email||"Anonymous Referral"),1),r("div",Ro,K(k(v.created_at)),1)]),r("span",{class:Q(["text-xs px-2 py-1 rounded-full font-medium",v.status==="completed"?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"])},K(v.status==="completed"?"Completed":"Pending"),3)]),r("div",Do,[r("span",Bo," Credit: $"+K(v.credit_amount||3),1),v.status==="completed"&&v.completed_at?(T(),M("span",Po," Completed "+K(k(v.completed_at)),1)):ee("",!0)])]))),128))])])):(T(),M("div",No,u[23]||(u[23]=[nn('<div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4"><svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path></svg></div><h4 class="text-sm font-medium text-gray-900 mb-1">No referrals yet</h4><p class="text-xs text-gray-500">Start sharing your referral code to earn rewards!</p>',3)])))])):ee("",!0)]))])])])):ee("",!0)}},Fo=ve({__name:"Collapsible",props:{defaultOpen:{type:Boolean},open:{type:Boolean},disabled:{type:Boolean},unmountOnHide:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["update:open"],setup(a,{emit:o}){const e=un(a,o);return(n,i)=>(T(),ke(G(pn),Be({"data-slot":"collapsible"},G(e)),{default:ie(({open:l})=>[Te(n.$slots,"default",{open:l})]),_:3},16))}}),qo=ve({__name:"CollapsibleContent",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{}},setup(a){const o=a;return(s,t)=>(T(),ke(G(hn),Be({"data-slot":"collapsible-content"},o),{default:ie(()=>[Te(s.$slots,"default")]),_:3},16))}}),jo=ve({__name:"CollapsibleTrigger",props:{asChild:{type:Boolean},as:{}},setup(a){const o=a;return(s,t)=>(T(),ke(G(vn),Be({"data-slot":"collapsible-trigger"},o),{default:ie(()=>[Te(s.$slots,"default")]),_:3},16))}}),Uo={class:"my-4"},Ho={class:"flex items-center space-x-3"},zo={class:"flex flex-col"},Oo={class:"font-semibold text-gray-800 text-sm"},Vo={class:"text-xs text-gray-600 opacity-75"},Ko={class:"flex items-center space-x-2"},Yo={class:"text-xs text-gray-500 opacity-0 group-hover:opacity-100 transition-opacity duration-200 font-medium"},Go={class:"border-l-2 border-r-2 border-b-2 border-gray-200 rounded-b-lg bg-gradient-to-b from-gray-50 to-white"},Jo={class:"px-5 py-4"},Qo={class:"prose prose-sm max-w-none text-gray-700 leading-relaxed"},Wo={key:0,class:"thinking-content space-y-3"},Xo={class:"latest-line bg-blue-50 p-3 rounded border-l-4 border-blue-400"},Zo={class:"text-sm font-medium text-gray-800"},er=["innerHTML"],tr={key:1,class:"text-gray-500 italic"},nr={key:2,class:"inline-block w-2 h-4 bg-blue-400 ml-1 animate-pulse"},or={key:0,class:"preview-scroll"},rr=["innerHTML"],sr={key:1,class:"flex items-center space-x-2 text-xs text-gray-500"},ar={key:1,class:"thinking-content space-y-3"},ir=["innerHTML"],lr={key:0,class:"mt-4 pt-3 border-t border-gray-200 animate-fade-in"},cr={class:"flex items-center justify-between"},dr={class:"flex items-center space-x-1"},ur={class:"text-xs text-gray-500"},fr=ve({__name:"ThinkingSection",props:{content:{},isThinking:{type:Boolean,default:!1},autoCollapse:{type:Boolean,default:!0}},setup(a){const o=a,s=U(!1),t=U(!1),e=b=>{const m=b.split(`
`).filter(w=>w.trim()!==""),g=m[0]||"🧠 Clinical Reasoning",p=m.slice(1).join(`
`).trim();return{title:g,body:p}},{title:n,body:i}=e(o.content),l=b=>b.replace(/#{1,6}\s*(.*?)(\n|$)/g,'<div class="mb-2"><strong>$1</strong></div>$2').replace(/Step\s+(\d+):\s*(.*?)(\n|$)/g,'<div class="mb-2"><strong>Step $1: $2</strong></div>$3').replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\n\n/g,'</p><p class="mb-2">').replace(/\n/g,"<br>").replace(/^/,'<p class="mb-2">').replace(/$/,"</p>"),c=$e(()=>{const b=i.split(`
`).filter(g=>g.trim()!=="");return b.length<=1?"":b.slice(-5,-1).join(`
`)}),y=$e(()=>{const b=i.split(`
`).filter(p=>p.trim()!=="");let g=(b[b.length-1]||"").trim();return g=g.replace(/^#{1,6}\s*/,""),g=g.replace(/\*\*(.*?)\*\*/g,"$1"),g||"Analyzing medical information..."}),x=$e(()=>c.value?l(c.value):"");return Ce(()=>o.isThinking,b=>{b?(t.value=!0,s.value=!0):o.autoCollapse&&setTimeout(()=>{s.value=!1,t.value=!1},3e3)}),Ce(()=>o.content,b=>{console.log("ThinkingSection content updated:",b),o.isThinking&&(t.value=!0)}),dt(()=>{o.isThinking&&(t.value=!0,s.value=!0)}),(b,m)=>(T(),M("div",Uo,[oe(G(Fo),{open:s.value,"onUpdate:open":m[0]||(m[0]=g=>s.value=g),class:"w-full"},{default:ie(()=>[oe(G(jo),{class:"flex w-full items-center justify-between rounded-lg border border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50 px-4 py-3 text-left hover:from-blue-100 hover:to-indigo-100 transition-all duration-200 group shadow-sm"},{default:ie(()=>[r("div",Ho,[m[1]||(m[1]=r("div",{class:"flex items-center justify-center w-7 h-7 rounded-full bg-gradient-to-r from-blue-500 to-indigo-500 group-hover:from-blue-600 group-hover:to-indigo-600 transition-all duration-200 shadow-sm"},[r("div",{class:"flex space-x-0.5"},[r("div",{class:"w-1 h-1 bg-white rounded-full animate-bounce",style:{"animation-delay":"0ms","animation-duration":"1.4s"}}),r("div",{class:"w-1 h-1 bg-white rounded-full animate-bounce",style:{"animation-delay":"200ms","animation-duration":"1.4s"}}),r("div",{class:"w-1 h-1 bg-white rounded-full animate-bounce",style:{"animation-delay":"400ms","animation-duration":"1.4s"}})])],-1)),r("div",zo,[r("span",Oo,K(G(n)),1),r("span",Vo,K(b.isThinking?"Thinking in progress...":"Clinical reasoning process"),1)])]),r("div",Ko,[r("span",Yo,K(s.value?"Hide reasoning":"Show reasoning"),1),(T(),M("svg",{class:Q(["w-4 h-4 text-gray-600 transition-transform duration-300 ease-in-out",{"rotate-180":s.value}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},m[2]||(m[2]=[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"},null,-1)]),2))])]),_:1}),oe(G(qo),{class:"overflow-hidden"},{default:ie(()=>[r("div",Go,[r("div",Jo,[r("div",Qo,[b.isThinking||t.value?(T(),M("div",Wo,[r("div",Xo,[m[3]||(m[3]=r("div",{class:"text-xs text-blue-600 font-medium mb-1"},"Currently analyzing:",-1)),r("div",Zo,[y.value&&y.value!=="Analyzing medical information..."?(T(),M("span",{key:0,innerHTML:y.value},null,8,er)):(T(),M("span",tr,"Processing medical information...")),b.isThinking?(T(),M("span",nr)):ee("",!0)])]),x.value&&c.value!==y.value?(T(),M("div",or,[m[4]||(m[4]=r("div",{class:"text-xs text-gray-500 mb-2"},"Recent analysis:",-1)),r("div",{class:"preview-content bg-gray-50 p-3 rounded text-sm",innerHTML:x.value},null,8,rr)])):ee("",!0),b.isThinking?(T(),M("div",sr,m[5]||(m[5]=[r("div",{class:"flex space-x-1"},[r("div",{class:"w-1 h-1 bg-blue-400 rounded-full animate-pulse"}),r("div",{class:"w-1 h-1 bg-blue-400 rounded-full animate-pulse",style:{"animation-delay":"200ms"}}),r("div",{class:"w-1 h-1 bg-blue-400 rounded-full animate-pulse",style:{"animation-delay":"400ms"}})],-1),r("span",null,"Continuing analysis...",-1)]))):ee("",!0)])):s.value?(T(),M("div",ar,[r("div",{innerHTML:l(G(i))},null,8,ir)])):ee("",!0)]),s.value?(T(),M("div",lr,[r("div",cr,[m[6]||(m[6]=r("span",{class:"text-xs text-gray-500 italic"},"This reasoning helps explain the medical decision-making process",-1)),r("div",dr,[r("div",{class:Q(["w-1.5 h-1.5 rounded-full",b.isThinking?"bg-blue-400 animate-pulse":"bg-green-400"])},null,2),r("span",ur,K(b.isThinking?"Analyzing...":"Analysis complete"),1)])])])):ee("",!0)])])]),_:1})]),_:1},8,["open"])]))}}),Ot=Kt(fr,[["__scopeId","data-v-39c0783d"]]),gr={class:"h-full flex flex-col bg-gray-50 relative overflow-hidden"},mr={class:"absolute top-4 left-4 z-20"},pr={class:"flex items-center space-x-3"},hr={class:"absolute top-4 right-4 z-20"},vr={class:"flex items-center space-x-2"},yr={key:0,class:"ml-2"},wr={key:0,class:"ml-2"},br={key:0,class:"flex-1 flex flex-col items-center justify-center px-6 py-8"},xr={key:1,class:"flex-1 flex flex-col items-center justify-center px-4 sm:px-6 py-8 pt-20 sm:pt-24"},_r={class:"text-center mb-8 sm:mb-12"},Cr={class:"mx-auto mb-4 sm:mb-6"},kr={class:"grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 gap-3 sm:gap-4 mb-8 sm:mb-12 max-w-2xl w-full"},Tr=["onClick"],Er=["innerHTML"],Sr={key:2,class:"flex-1 overflow-hidden pt-20 sm:pt-24"},Mr={class:"h-full flex flex-col"},Ar={class:"max-w-4xl mx-auto px-3 sm:px-4 space-y-4"},$r={key:0,class:"flex items-start max-w-[85%] sm:max-w-[80%]"},Ir={class:"flex-shrink-0 mr-3"},Rr={class:"w-8 h-8 flex items-center justify-center"},Dr={class:"bg-white text-gray-800 border border-gray-200 rounded-2xl rounded-bl-md message-bubble px-4 py-3 relative max-w-full"},Br={class:"text-sm leading-relaxed"},Pr={key:0,class:"flex items-center space-x-2"},Nr={key:1,class:"prose prose-sm max-w-none"},Lr={key:0,class:"mb-4"},Fr=["innerHTML"],qr={key:2,class:"prose prose-sm max-w-none"},jr=["innerHTML"],Ur=["innerHTML"],Hr={key:0,class:"mt-3 pt-3 border-t border-gray-200"},zr={class:"flex items-center justify-between"},Or={class:"text-xs text-gray-500"},Vr=["href"],Kr={class:"text-xs text-gray-400 mt-2"},Yr={key:1,class:"flex justify-end max-w-[85%] sm:max-w-[80%]"},Gr={class:"user-message-bubble text-white rounded-2xl rounded-br-md message-bubble px-4 py-3 relative max-w-full"},Jr={class:"text-sm leading-relaxed break-words whitespace-pre-wrap"},Qr={class:"text-teal-100 mt-2 text-right text-xs"},Wr={key:0,class:"max-w-4xl mx-auto px-3 sm:px-4"},Xr={class:"flex-1 min-w-0"},Zr={class:"mb-4"},es={class:"text-xs text-gray-500 mb-3"},ts={class:"flex flex-wrap gap-2 mb-3"},ns={class:"flex-1 min-w-0"},os=["value"],rs={class:"flex-1 min-w-0"},ss=["value"],as={key:0,class:"flex items-center justify-center py-6"},is={key:1,class:"text-center py-6"},ls={key:2},cs=["onClick"],ds={class:"flex items-center justify-between mb-1"},us={key:0,class:"text-center pt-2"},fs={class:"mt-3 pt-3 border-t border-gray-200 flex items-center justify-between"},gs={class:"text-xs text-gray-400"},ms={class:"text-xs text-gray-400 mt-1"},ps={class:"relative"},hs={class:"max-w-4xl mx-auto"},vs={__name:"Chat",setup(a){const o=U([]),s=U(""),t=U(!1),e=U(null),n=U(null),i=U(null),l=U(!1),c=U([]),y=U([]),x=U(!1),b=U(!1),m=U(""),g=U("all"),p=U([]),w=U([]),O=U(!1),I=U(!1),z=U(typeof window<"u"?window.innerWidth:1024),E=U(!1),R=()=>{typeof window<"u"&&(z.value=window.innerWidth,E.value=window.innerWidth<640)},J=U(null),S=U(null),P=[{title:"Chat",href:"/chat"}],D=[{icon:`<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>`,text:"I have a headache",color:"bg-blue-50 text-blue-700 border-blue-200"},{icon:`<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
    </svg>`,text:"My tummy hurts",color:"bg-green-50 text-green-700 border-green-200"},{icon:`<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z" />
    </svg>`,text:"I can't lose weight",color:"bg-purple-50 text-purple-700 border-purple-200"},{icon:`<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.5 12c0-1.232-.046-2.453-.138-3.662a4.006 4.006 0 00-3.7-3.7 48.678 48.678 0 00-7.324 0 4.006 4.006 0 00-3.7 3.7c-.017.22-.032.441-.046.662M19.5 12l3-3m-3 3l-3-3m-12 3c0 1.232.046 2.453.138 3.662a4.006 4.006 0 003.7 3.7 48.656 48.656 0 007.324 0 4.006 4.006 0 003.7-3.7c.017-.22.032-.441.046-.662M4.5 12l3 3m-3-3l-3 3" />
    </svg>`,text:"My urine burns",color:"bg-orange-50 text-orange-700 border-orange-200"},{icon:`<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5a2.25 2.25 0 002.25-2.25m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5" />
    </svg>`,text:"Book appointment",color:"bg-teal-50 text-teal-700 border-teal-200"},{icon:`<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.182 15.182a4.5 4.5 0 01-6.364 0M21 12a9 9 0 11-18 0 9 9 0 0118 0zM9.75 9.75c0 .414-.168.75-.375.75S9 10.164 9 9.75 9.168 9 9.375 9s.375.336.375.75zm-.375 0h.008v.015h-.008V9.75zm5.625 0c0 .414-.168.75-.375.75s-.375-.336-.375-.75.168-.75.375-.75.375.336.375.75zm-.375 0h.008v.015h-.008V9.75z" />
    </svg>`,text:"I have a skin rash",color:"bg-indigo-50 text-indigo-700 border-indigo-200"}],k=_=>{if(!_)return _;let d=_;const f=[];return d=d.replace(/\[THINKING_START\](.*?)\[THINKING_END\]/gs,(C,H)=>{const F=`thinking-${f.length}`;return f.push({id:F,content:H.trim()}),`[THINKING_PLACEHOLDER_${F}]`}),d=d.replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>"),d=d.replace(/^#{1,6}\s*(.*?)(\n|$)/gm,'<div class="font-semibold text-gray-900 mt-3 mb-2">$1</div>$2'),d=d.replace(/^## (.*?):/gm,'<h2 class="font-bold text-gray-900 mt-4 mb-3 text-lg">$1:</h2>'),d=d.replace(/^### (.*?):/gm,'<h3 class="font-semibold text-gray-900 mt-3 mb-2">$1:</h3>'),d=d.replace(/^#### (.*?):/gm,'<h4 class="font-semibold text-gray-900 mt-2 mb-1 text-sm">$1:</h4>'),d=d.replace(/(\d+)\.\s*\*\*(.*?)\*\*/g,'<div class="mb-2"><strong>$1. $2</strong></div>'),d=d.replace(/- Why I think so: (.*?)(?=\d+\.|$)/gs,'<div class="ml-4 text-gray-900 mb-2">Why I think so: $1</div>'),d=d.replace(/^• \*\*(.*?):\*\* (.*$)/gm,'<div class="mb-1"><strong>$1:</strong> $2</div>'),d=d.replace(/^- \*\*(.*?)\*\*/gm,'<div class="mb-2"><strong>• $1</strong></div>'),d=d.replace(/^- (.*$)/gm,'<div class="mb-1 ml-4">• $1</div>'),d=d.replace(/\*\*(.*?):\*\*/g,'<h4 class="font-semibold text-gray-900 mt-4 mb-2">$1:</h4>'),d=d.replace(/^\*\*(.*?):\*\* (.*$)/gm,'<div class="mb-1"><strong>$1:</strong> $2</div>'),d=d.replace(/\n\n/g,'</p><p class="mb-2">'),d=d.replace(/\n/g,"<br>"),d=`<p class="mb-2">${d}</p>`,d=d.replace(/<p class="mb-2"><\/p>/g,""),d=d.replace(/<br><br>/g,"<br>"),f.length>0&&(d={content:d,thinkingSections:f}),d},h=(_,d)=>{const f=o.value.find($=>$.id===_);if(!f)return;f.isStreaming=!0,f.displayedContent="",f.streamingLines=[],f.isThinking=!1,f.thinkingContent="",J.value=_;let C=!1,H=!1,F="";const B=d.split(`
`).filter($=>$.trim()!=="");let Y=0;const N=()=>{if(Y<B.length){const $=B[Y];$.includes("[THINKING_START]")?(C=!0,f.isThinking=!0,F=""):$.includes("[THINKING_END]")?(H=!0,f.isThinking=!1,f.thinkingContent=F):C&&!H&&$.trim()&&(F+=$+`
`,f.thinkingContent=F),!$.includes("[THINKING_START]")&&!$.includes("[THINKING_END]")&&(!C||H)&&(f.streamingLines.push({content:$,id:`line-${Y}`,fadeIn:!0}),f.displayedContent=f.streamingLines.map(V=>V.content).join(`
`)),Y++,Z(()=>ne());const j=$.length>100?800:500;S.value=setTimeout(N,j)}else q()},q=()=>{f.isStreaming=!1,f.displayedContent=d,f.formatted=k(d),f.streamingLines=[],J.value=null,S.value&&(clearTimeout(S.value),S.value=null),Z(()=>ne())};setTimeout(()=>{N()},200)},u=_=>{if(console.log("Processing backend slots:",_),!Array.isArray(_)){console.warn("backendSlots is not an array:",_),y.value=[],c.value=[];return}const d=[],f=new Date().toISOString().split("T")[0];_.forEach(C=>{if(!C||!C.provider){console.warn("Invalid provider slot:",C);return}const H=C.provider,F=C.service||null,B=C.date,Y=C.day_of_week;C.slots&&Array.isArray(C.slots)&&C.slots.forEach(N=>{const q={id:`${H.id}-${B}-${N.start_time}`,provider_id:H.id,service_id:(F==null?void 0:F.id)||1,provider:`Dr. ${H.name}`,time:N.start_time,end_time:N.end_time,date:`${Y}, ${new Date(B).toLocaleDateString()}`,full_date:B,price:(F==null?void 0:F.price)||50,datetime:new Date(`${B} ${N.start_time}`),isToday:B===f};d.push(q)})}),d.sort((C,H)=>C.isToday&&!H.isToday?-1:!C.isToday&&H.isToday?1:C.datetime-H.datetime),y.value=d,b.value=!1,v(d),ue(),console.log("Processed slots:",d.length,"Today slots:",Array.isArray(d)?d.filter(C=>C.isToday).length:0)},v=_=>{var F;if(!Array.isArray(_)){console.warn("slots is not an array in setupFilterOptions:",_),p.value=[{value:"",label:"All Days"}],w.value=[{value:"all",label:"All Providers"}],m.value="",g.value="all";return}const d=[...new Set(_.map(B=>B.full_date))],f=new Date().toISOString().split("T")[0];p.value=d.map(B=>{const Y=new Date(B),N=B===f,q=Y.toDateString()===new Date(Date.now()+24*60*60*1e3).toDateString();let $=Y.toLocaleDateString("en-US",{weekday:"short",month:"short",day:"numeric"});return N?$=`Today (${$})`:q&&($=`Tomorrow (${$})`),{value:B,label:$}}).sort((B,Y)=>new Date(B.value)-new Date(Y.value)),p.value.push({value:"",label:"All Days"});const C=[...new Set(_.map(B=>({id:B.provider_id,name:B.provider})))];w.value=[{value:"all",label:"All Providers"},...C.map(B=>({value:B.id.toString(),label:B.name}))],p.value.find(B=>B.value===f)?m.value=f:m.value=((F=p.value[0])==null?void 0:F.value)||""},A=async()=>{var _;x.value=!0,await Z(),ne();try{console.log("TEST: Loading providers...");const d=await te.get("/providers"),f=d.data.data||d.data.providers||d.data||[];if(console.log("TEST: Providers loaded:",f.length),f.length===0){o.value.push({id:Date.now(),type:"ai",content:"I apologize, but there are no available providers at the moment. Please try again later or contact our office directly.",timestamp:new Date});return}const C=new Date,H=[];let F=[];try{F=(await te.get("/appointments-list")).data.appointments||[],console.log("Existing appointments loaded:",F.length)}catch{console.log("Could not load existing appointments, proceeding without conflict check")}const B=[];for(let j=0;j<=14;j++){const V=new Date(C);V.setDate(V.getDate()+j);const W=V.toISOString().split("T")[0];B.push({value:W,label:V.toLocaleDateString("en-US",{weekday:"short",month:"short",day:"numeric"}),fullDate:V})}p.value=B;const Y=C.toISOString().split("T")[0];m.value||(m.value=Y),w.value=[{value:"all",label:"All Providers"},...f.map(j=>{var V;return{value:j.id.toString(),label:`Dr. ${((V=j.user)==null?void 0:V.name)||"Provider"}`}})];for(let j=0;j<=14;j++){const V=new Date(C);V.setDate(V.getDate()+j);const W=V.toISOString().split("T")[0];console.log(`TEST: Checking date ${W}`);for(const X of f){console.log(`TEST: Checking provider ${X.id} - ${(_=X.user)==null?void 0:_.name}`);try{const he=(await te.get(`/providers/${X.id}/available-slots?date=${W}`)).data.available_slots||[];if(console.log(`TEST: Provider ${X.id} has ${he.length} slots on ${W}`),he.length>0){const Le=he.filter(ce=>{const ge=`${W} ${ce.start_time}`;return!F.some(Me=>{var mt;return Me.status==="cancelled"?!1:`${Me.date||((mt=Me.scheduled_at)==null?void 0:mt.split("T")[0])} ${Me.time||Me.start_time}`===ge})});if(console.log(`TEST: Provider ${X.id} has ${Le.length} available slots after filtering`),Le.length===0){console.log(`TEST: All slots for provider ${X.id} on ${W} are already booked`);continue}let se=null;try{const ge=(await te.get(`/providers/${X.id}/services`)).data.services||[];console.log(`TEST: Services for provider ${X.id}:`,ge),ge.length>0&&(se=ge[0],console.log("TEST: Selected service:",se),console.log(`TEST: Service ID: ${se.id}, Name: ${se.name}, Price: ${se.price}`))}catch(ce){console.error(`Error loading services for provider ${X.id}:`,ce)}if(!se||!se.id){console.error(`No valid service found for provider ${X.id}, skipping slots`);continue}const Xt=Le.map(ce=>{var ge;return{id:`${X.id}-${W}-${ce.start_time}`,provider_id:X.id,service_id:se.id,provider:`Dr. ${((ge=X.user)==null?void 0:ge.name)||"Provider"}`,specialty:X.specialization||"General Practice",date:V.toLocaleDateString("en-US",{weekday:"long",month:"short",day:"numeric"}),time:ce.start_time,end_time:ce.end_time,full_date:W,datetime:new Date(`${W}T${ce.start_time}`),price:se.price||0,duration:se.duration||15,service_name:se.name||"Consultation"}});H.push(...Xt)}}catch(fe){console.error(`Error loading slots for provider ${X.id} on ${W}:`,fe)}}if(H.length>=50)break}if(console.log("TEST: Total slots found:",H.length),H.length===0){o.value.push({id:Date.now(),type:"ai",content:"I apologize, but there are no available appointment slots in the next week. Please contact our office directly to schedule an appointment.",timestamp:new Date});return}H.sort((j,V)=>j.datetime-V.datetime);const N={};H.forEach(j=>{N[j.provider_id]||(N[j.provider_id]=[]),N[j.provider_id].push(j)});const q=[],$=Math.ceil(8/Object.keys(N).length);Object.keys(N).forEach(j=>{const V=N[j].slice(0,$);q.push(...V)}),q.sort((j,V)=>j.datetime-V.datetime),y.value=q,b.value=!1,l.value=!0,ue(),await Z(),ne()}catch(d){console.error("Error loading appointment slots:",d),o.value.push({id:Date.now(),type:"ai",content:"I apologize, but I cannot access the appointment system right now. Please contact our office directly to schedule an appointment.",timestamp:new Date})}finally{x.value=!1,await Z(),ne()}},L=async _=>{var d,f,C,H,F;try{let B="Appointment booked through AI chat consultation",Y="General consultation";if(i.value)try{const V=await te.get(`/web-api/chat/conversation/${i.value}`);if(V.data&&V.data.referral_note){B=V.data.referral_note;const W=B.replace(/\*\*/g,"").replace(/###/g,"").replace(/\n+/g," ").trim();Y=W.length>150?W.substring(0,150)+"...":W}}catch{console.log("Could not fetch referral note, using default reason")}let N=_.service_id;if(_.provider_id===3&&(N=4,console.log("Fixed service ID for Provider 3 to Service ID 4")),!N)throw new Error("Invalid appointment slot: missing service ID");console.log("Using service ID:",N,"for provider:",_.provider_id);const q={provider_id:_.provider_id,service_id:N,date:_.full_date,time_slot:{start_time:_.time,end_time:_.end_time},reason:Y,notes:B,currency:"GBP"};console.log("Booking appointment with data (UPDATED):",q),console.log("Slot data:",_),console.log("Service ID being sent:",q.service_id),console.log("Provider ID being sent:",q.provider_id);const $=await fetch("/api/appointments/with-payment",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":((d=document.querySelector('meta[name="csrf-token"]'))==null?void 0:d.getAttribute("content"))||""},credentials:"include",body:JSON.stringify(q)});if(!$.ok){const V=await $.json();throw new Error(V.message||`HTTP ${$.status}`)}const j=await $.json();if(j.appointment){const V=j.appointment,W=j.payment;if(W&&W.client_secret){const X=`🏥 Appointment Created - Payment Required

Provider: ${_.provider}
Date: ${_.date}
Time: ${_.time}
Appointment ID: #${V.id}
Amount: $${W.amount}

Your appointment has been created and is pending payment. Please click the "Pay Now" button below to complete your booking.`,fe={id:Date.now(),type:"ai",content:X,timestamp:new Date,formatted:k(X),showPaymentButton:!0,appointmentId:V.id,paymentAmount:W.amount,paymentUrl:`/appointments/${V.id}/payment?conversation=${i.value}`};if(o.value.push(fe),i.value)try{await te.post("/web-api/chat/message",{conversation_id:i.value,message:fe.content,role:"assistant"}),console.log("Payment message saved to chat history")}catch(he){console.error("Error saving payment message to chat history:",he)}}else{const X=`✅ **Appointment Confirmed!**

**Provider:** ${_.provider}
**Date:** ${_.date}
**Time:** ${_.time}
**Appointment ID:** #${V.id}

Your appointment has been successfully booked. You'll receive a confirmation email shortly with all the details.

Is there anything else I can help you with regarding your upcoming appointment?`,fe={id:Date.now(),type:"ai",content:X,timestamp:new Date,formatted:k(X)};if(o.value.push(fe),i.value)try{await te.post("/web-api/chat/message",{conversation_id:i.value,message:fe.content,role:"assistant"}),console.log("Confirmation message saved to chat history")}catch(he){console.error("Error saving confirmation message to chat history:",he)}}}else throw new Error("No appointment data returned");l.value=!1,await Z(),ne()}catch(B){console.error("Error booking appointment:",B);let Y="Unknown error occurred";(C=(f=B.response)==null?void 0:f.data)!=null&&C.message?Y=B.response.data.message:(F=(H=B.response)==null?void 0:H.data)!=null&&F.errors?Y=Object.values(B.response.data.errors).flat().join(", "):B.message&&(Y=B.message),console.log("Detailed error:",Y);const N={id:Date.now(),type:"ai",content:`I apologize, but there was an error booking your appointment with ${_.provider}.

**Error Details:** ${Y}

This could be due to:
• The time slot may no longer be available
• A technical issue with our booking system
• Invalid service or provider information

Please try selecting a different time slot or contact our office directly at your convenience. I'm here to help with any other questions you might have.`,timestamp:new Date,formatted:k(`I apologize, but there was an error booking your appointment with ${_.provider}.

**Error Details:** ${Y}

This could be due to:
• The time slot may no longer be available
• A technical issue with our booking system
• Invalid service or provider information

Please try selecting a different time slot or contact our office directly at your convenience. I'm here to help with any other questions you might have.`)};if(o.value.push(N),i.value)try{await te.post("/web-api/chat/message",{conversation_id:i.value,message:N.content,role:"assistant"})}catch(q){console.error("Error saving error message to chat history:",q)}l.value=!1,await Z(),ne()}},re=async(_=null)=>{var H,F;const d=_||s.value.trim();if(!d)return;o.value.push({id:Date.now(),type:"user",content:d,timestamp:new Date}),_||(s.value=""),await Z(),ne();const C={id:Date.now()+1,type:"ai",content:"",timestamp:new Date,isStreaming:!1,displayedContent:"",isPlaceholder:!0};o.value.push(C),await Z(),ne(),t.value=!0;try{if(!i.value){const q=await fetch("/web-api/chat/start",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":((H=document.querySelector('meta[name="csrf-token"]'))==null?void 0:H.getAttribute("content"))||""},credentials:"include",body:JSON.stringify({})}),$=await q.json();if(!q.ok)throw console.error("Start conversation failed:",$),new Error($.message||"Failed to start conversation");if(!$.conversation_id)throw new Error("No conversation ID returned");i.value=String($.conversation_id)}const B={conversation_id:String(i.value),message:d,include_patient_context:!0,generate_title:!0,request_full_response:!1};console.log("Sending payload:",B);const Y=await fetch("/web-api/chat/message",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":((F=document.querySelector('meta[name="csrf-token"]'))==null?void 0:F.getAttribute("content"))||""},credentials:"include",body:JSON.stringify(B)}),N=await Y.json();if(Y.ok&&N.message){const q=o.value.find($=>$.isPlaceholder&&$.type==="ai");if(q)q.content=N.message,q.formatted=k(N.message),q.isPlaceholder=!1,q.isStreaming=!1,q.displayedContent="",h(q.id,N.message);else{const $=Date.now()+1,j={id:$,type:"ai",content:N.message,timestamp:new Date,formatted:k(N.message),isStreaming:!1,displayedContent:""};o.value.push(j),await Z(),ne(),h($,N.message)}if(N.available_slots&&Array.isArray(N.available_slots))console.log("Backend provided appointment slots directly:",N.available_slots),u(N.available_slots),setTimeout(()=>{l.value=!0,Z(()=>{ne()})},500);else if(N.appointment_options||N.show_appointments)setTimeout(()=>{A()},1e3);else{const $=N.message.toLowerCase();["would you like to see available appointment slots","shall i show you available appointments","would you like to book an appointment now","let me show you available slots","here are the available appointment times","perfect! i found available appointment slots","appointment options provided"].some(W=>$.includes(W))&&setTimeout(()=>{A()},1e3)}}else{console.error("Failed to get AI response:",N);const q=`Sorry, I encountered an error: ${N.message||"Please try again."}`,$=o.value.find(j=>j.isPlaceholder&&j.type==="ai");if($)$.content=q,$.isPlaceholder=!1,$.isStreaming=!1,$.displayedContent="",h($.id,q);else{const j=Date.now()+1,V={id:j,type:"ai",content:q,timestamp:new Date,isStreaming:!1,displayedContent:""};o.value.push(V),await Z(),h(j,q)}}}catch(B){console.error("An error occurred while sending your message:",B);const Y=`Sorry, I encountered an error: ${B.message||"Please try again."}`,N=o.value.find(q=>q.isPlaceholder&&q.type==="ai");if(N)N.content=Y,N.isPlaceholder=!1,N.isStreaming=!1,N.displayedContent="",h(N.id,Y);else{const q=Date.now()+1,$={id:q,type:"ai",content:Y,timestamp:new Date,isStreaming:!1,displayedContent:""};o.value.push($),await Z(),h(q,Y)}}finally{t.value=!1,await Z(),ne(),n.value&&n.value.focus&&setTimeout(()=>{n.value.focus()},100)}},pe=_=>{re(_.text)},Pe=_=>{_.key==="Enter"&&!_.shiftKey&&(_.preventDefault(),re())},ne=()=>{e.value&&(e.value.scrollTop=e.value.scrollHeight)},Ee=_=>new Date(_).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),Se=()=>{o.value=[],i.value=null,l.value=!1,s.value="",n.value&&n.value.focus&&setTimeout(()=>{n.value.focus()},100)},be=()=>{I.value=!0},Ie=()=>{I.value=!1},ue=()=>{if(!Array.isArray(y.value)){console.warn("allAvailableSlots.value is not an array:",y.value),y.value=[],c.value=[];return}let _=[...y.value];m.value&&m.value!==""&&m.value!=="all"&&(_=_.filter(d=>d.full_date===m.value)),g.value&&g.value!=="all"&&(_=_.filter(d=>d.provider_id.toString()===g.value)),c.value=_,b.value=!0},Ne=async _=>{if(_){O.value=!0;try{const f=(await te.get(`/web-api/chat/conversation/${_}`)).data;f&&f.id&&(i.value=_,f.messages&&Array.isArray(f.messages)&&(o.value=f.messages.map(C=>({id:C._id||C.id||Date.now()+Math.random(),type:C.role==="user"?"user":"ai",content:C.content||C.message,timestamp:new Date(C.timestamp||C.created_at||Date.now()),formatted:C.role!=="user"?k(C.content||C.message):void 0})),await Z(),ne()))}catch(d){console.error("Error loading conversation:",d),i.value=null,o.value=[]}finally{O.value=!1}}},xe=async()=>{var _,d,f;try{const C=localStorage.getItem("anonymous_conversation");if(!C)return null;const H=JSON.parse(C);if(!H.conversation_id||!H.anonymous_id)return null;console.log("Transferring anonymous conversation:",H.conversation_id),await new Promise(Y=>setTimeout(Y,500));const F={conversation_id:String(H.conversation_id),anonymous_id:String(H.anonymous_id)};if(console.log("Transfer payload:",F),(await te.post("/web-api/chat/transfer-anonymous",F,{headers:{Accept:"application/json","Content-Type":"application/json","X-CSRF-TOKEN":((_=document.querySelector('meta[name="csrf-token"]'))==null?void 0:_.getAttribute("content"))||"","X-Requested-With":"XMLHttpRequest"},withCredentials:!0})).data.success)return console.log("Anonymous conversation transferred successfully"),localStorage.removeItem("anonymous_conversation"),H.conversation_id}catch(C){if(console.error("Error transferring anonymous conversation:",C),C.response&&(console.error("Response status:",C.response.status),console.error("Response data:",C.response.data)),((d=C.response)==null?void 0:d.status)===401)return console.log("Authentication required for transfer, will retry later"),null;if(((f=C.response)==null?void 0:f.status)===422)return console.error("Validation errors:",C.response.data.errors),null;localStorage.removeItem("anonymous_conversation")}return null},Re=async _=>{var d,f,C;try{const F=(await te.get(`/api/appointments/${_}`)).data.appointment,B=$=>{try{return new Date($).toLocaleDateString("en-US",{weekday:"long",month:"long",day:"numeric",year:"numeric"})}catch{return $||"Date not available"}},N=`🎉 **Payment Successful - Appointment Confirmed!**

Excellent! Your payment has been processed successfully and your appointment is now confirmed.

**📅 Appointment Details:**
**Provider:** ${($=>{var j;return(j=$==null?void 0:$.user)!=null&&j.name?$.user.name:$!=null&&$.name?`Dr. ${$.name}`:"Your Doctor"})(F.provider)}
**Date:** ${B(F.date)}
**Time:** ${((d=F.time_slot)==null?void 0:d.start_time)||"Time TBD"} - ${((f=F.time_slot)==null?void 0:f.end_time)||"Time TBD"}
**Service:** ${((C=F.service)==null?void 0:C.name)||"Consultation"}
**Amount Paid:** $${F.amount||"0.00"}
**Appointment ID:** #${F.id||"N/A"}

**📧 What's Next:**
• You'll receive a confirmation email shortly
• A calendar invite will be sent to your email
• You can view and manage this appointment in your appointments section

Is there anything else I can help you with regarding your upcoming appointment or any other health concerns?`,q={id:Date.now(),type:"ai",content:N,timestamp:new Date,formatted:k(N)};if(o.value.push(q),i.value)try{await te.post("/web-api/chat/message",{conversation_id:i.value,message:q.content,role:"assistant"}),console.log("Payment confirmation message saved to chat history")}catch($){console.error("Error saving confirmation message to chat history:",$)}await Z(),ne()}catch(H){console.error("Error handling payment success:",H);const F=`🎉 **Payment Successful!**

Great news! Your payment has been processed successfully and your appointment is confirmed.

**Appointment ID:** #${_}

You'll receive a confirmation email shortly with all the details. You can also view your appointment in the appointments section.

Is there anything else I can help you with today?`,B={id:Date.now(),type:"ai",content:F,timestamp:new Date,formatted:k(F)};o.value.push(B),await Z(),ne()}};return dt(async()=>{R(),typeof window<"u"&&window.addEventListener("resize",R);const _=new URLSearchParams(window.location.search),d=_.get("conversation"),f=_.get("payment_success"),C=_.get("appointment_id");let H=await xe();!H&&localStorage.getItem("anonymous_conversation")&&(console.log("Retrying anonymous conversation transfer after authentication delay..."),await new Promise(B=>setTimeout(B,1500)),H=await xe());const F=d||H;F&&Ne(F),f==="true"&&C&&(await Re(C),window.history.replaceState({},document.title,window.location.pathname))}),on(()=>{S.value&&clearTimeout(S.value),typeof window<"u"&&window.removeEventListener("resize",R)}),(_,d)=>(T(),M(ae,null,[oe(G(rn),{title:"Chat - Medroid"}),oe(sn,{breadcrumbs:P},{default:ie(()=>[r("div",gr,[r("div",mr,[r("div",pr,[oe(qe,{size:32}),d[5]||(d[5]=r("div",{class:"hidden sm:block"},[r("h1",{class:"text-sm font-semibold text-gray-900"},"Medroid AI"),r("p",{class:"text-xs text-gray-500"},"Your AI Doctor")],-1))])]),r("div",hr,[r("div",vr,[r("button",{onClick:Se,class:Q(["flex items-center text-sm font-medium text-gray-700 bg-white/80 backdrop-blur-sm border border-white/50 rounded-full hover:bg-white/90 transition-all duration-200 shadow-lg",E.value?"p-2":"px-3 py-2 space-x-2"])},[d[6]||(d[6]=r("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1)),E.value?ee("",!0):(T(),M("span",yr,"New Chat"))],2),r("button",{onClick:be,class:Q(["flex items-center text-sm font-medium text-green-600 bg-green-50/80 backdrop-blur-sm border border-green-200/50 rounded-full hover:bg-green-100/80 transition-all duration-200 shadow-lg",E.value?"p-2":"px-3 py-2 space-x-2"])},[d[7]||(d[7]=r("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1)),E.value?ee("",!0):(T(),M("span",wr,"Refer & Earn"))],2)])]),O.value?(T(),M("div",br,d[8]||(d[8]=[r("div",{class:"text-center"},[r("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto mb-4"}),r("p",{class:"text-gray-600"},"Loading conversation...")],-1)]))):o.value.length===0?(T(),M("div",xr,[r("div",_r,[r("div",Cr,[oe(qe,{size:E.value?60:80,"show-shadow":!0},null,8,["size"])]),d[9]||(d[9]=r("h1",{class:"text-2xl sm:text-4xl font-bold text-gray-900 mb-3 sm:mb-4"},"Welcome to Medroid",-1)),d[10]||(d[10]=r("p",{class:"text-base sm:text-lg text-gray-600 max-w-md mx-auto px-4"}," Your personal AI Dr. Ask me anything about your symptoms. ",-1))]),r("div",kr,[(T(),M(ae,null,de(D,f=>r("button",{key:f.text,onClick:C=>pe(f),class:Q([f.color,"flex flex-col items-center rounded-xl border-2 hover:shadow-md transition-all duration-200 hover:scale-105",E.value?"p-3":"p-4"])},[r("div",{class:"mb-2",innerHTML:f.icon},null,8,Er),r("span",{class:Q(["font-medium text-center",E.value?"text-xs":"text-sm"])},K(f.text),3)],10,Tr)),64))])])):(T(),M("div",Sr,[r("div",Mr,[r("div",{ref_key:"chatContainer",ref:e,class:"flex-1 overflow-y-auto pt-4 pb-2"},[r("div",Ar,[(T(!0),M(ae,null,de(o.value,f=>(T(),M("div",{key:f.id,class:Q(["flex mb-4 transition-all duration-500 ease-out",f.type==="user"?"justify-end":"justify-start"])},[f.type==="ai"?(T(),M("div",$r,[r("div",Ir,[r("div",Rr,[oe(qe,{size:24})])]),r("div",Dr,[r("div",Br,[f.isPlaceholder?(T(),M("div",Pr,d[11]||(d[11]=[r("span",{class:"text-gray-600"},"AI is thinking",-1),r("div",{class:"flex space-x-1"},[r("div",{class:"w-1.5 h-1.5 bg-teal-500 rounded-full animate-modern-bounce"}),r("div",{class:"w-1.5 h-1.5 bg-teal-500 rounded-full animate-modern-bounce",style:{"animation-delay":"0.15s"}}),r("div",{class:"w-1.5 h-1.5 bg-teal-500 rounded-full animate-modern-bounce",style:{"animation-delay":"0.3s"}})],-1)]))):f.isStreaming&&f.streamingLines?(T(),M("div",Nr,[f.thinkingContent?(T(),M("div",Lr,[oe(Ot,{content:f.thinkingContent,"is-thinking":f.isThinking,"auto-collapse":!0},null,8,["content","is-thinking"])])):ee("",!0),(T(!0),M(ae,null,de(f.streamingLines,C=>(T(),M("div",{key:C.id,class:"animate-fade-in-line",innerHTML:k(C.content)},null,8,Fr))),128)),d[12]||(d[12]=r("span",{class:"inline-block w-2 h-4 bg-gray-400 ml-1 animate-pulse"},null,-1))])):(T(),M("div",qr,[typeof f.formatted=="object"&&f.formatted.thinkingSections?(T(),M(ae,{key:0},[(T(!0),M(ae,null,de(f.formatted.thinkingSections,C=>(T(),M("div",{key:C.id,class:"mb-4"},[oe(Ot,{content:C.content,"is-thinking":!1,"auto-collapse":!1},null,8,["content"])]))),128)),r("div",{innerHTML:f.formatted.content.replace(/\[THINKING_PLACEHOLDER_thinking-\d+\]/g,"")},null,8,jr)],64)):(T(),M("div",{key:1,innerHTML:f.formatted||f.content},null,8,Ur))]))]),f.showPaymentButton?(T(),M("div",Hr,[r("div",zr,[r("div",Or," Appointment #"+K(f.appointmentId)+" • £"+K(f.paymentAmount),1),r("a",{href:f.paymentUrl,class:"inline-flex items-center px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white text-sm font-medium rounded-lg transition-colors duration-200"},d[13]||(d[13]=[r("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})],-1),Vt(" Pay Now ")]),8,Vr)])])):ee("",!0),r("div",Kr,K(Ee(f.timestamp)),1)])])):(T(),M("div",Yr,[r("div",Gr,[r("div",Jr,K(f.content),1),r("div",Qr,K(Ee(f.timestamp)),1)])]))],2))),128))]),l.value?(T(),M("div",Wr,[r("div",{class:Q(["flex items-start",E.value?"space-x-2":"space-x-3"])},[r("div",{class:Q(["bg-teal-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1",E.value?"w-5 h-5":"w-6 h-6"])},[(T(),M("svg",{class:Q(["text-white",E.value?"w-2.5 h-2.5":"w-3 h-3"]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},d[14]||(d[14]=[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"},null,-1)]),2))],2),r("div",Xr,[r("div",{class:Q(["text-gray-800 leading-snug prose prose-sm max-w-none break-words",(E.value,"text-sm")])},[r("div",{class:Q(["bg-white border border-gray-200 rounded-xl shadow-sm",E.value?"p-3":"p-4"])},[r("div",Zr,[d[16]||(d[16]=r("h3",{class:"text-sm font-medium text-gray-900 mb-2"},"Available Appointments",-1)),r("p",es,K(c.value.length)+" slots available • Click any slot to book ",1),r("div",ts,[r("div",ns,[ct(r("select",{"onUpdate:modelValue":d[0]||(d[0]=f=>m.value=f),onChange:ue,class:"w-full text-xs border border-gray-300 rounded-md px-2 py-1 focus:ring-1 focus:ring-teal-500 focus:border-teal-500"},[d[15]||(d[15]=r("option",{value:""},"All Days",-1)),(T(!0),M(ae,null,de(p.value,f=>(T(),M("option",{key:f.value,value:f.value},K(f.label),9,os))),128))],544),[[pt,m.value]])]),r("div",rs,[ct(r("select",{"onUpdate:modelValue":d[1]||(d[1]=f=>g.value=f),onChange:ue,class:"w-full text-xs border border-gray-300 rounded-md px-2 py-1 focus:ring-1 focus:ring-teal-500 focus:border-teal-500"},[(T(!0),M(ae,null,de(w.value,f=>(T(),M("option",{key:f.value,value:f.value},K(f.label),9,ss))),128))],544),[[pt,g.value]])])])]),x.value?(T(),M("div",as,d[17]||(d[17]=[r("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-teal-500"},null,-1),r("span",{class:"ml-2 text-xs text-gray-600"},"Loading slots...",-1)]))):c.value.length===0?(T(),M("div",is,d[18]||(d[18]=[r("p",{class:"text-xs text-gray-500"},"No appointments available for selected filters",-1)]))):(T(),M("div",ls,[r("div",{class:Q(["grid gap-2 mb-3",E.value?"grid-cols-1":"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"])},[(T(!0),M(ae,null,de(b.value?c.value:c.value.slice(0,E.value?6:9),f=>(T(),M("button",{key:f.id,onClick:C=>L(f),class:Q(["flex flex-col bg-gray-50 hover:bg-teal-50 border border-gray-200 hover:border-teal-300 rounded-lg transition-all duration-200 text-left",E.value?"p-2.5":"p-3"])},[r("div",ds,[r("div",{class:Q(["font-medium text-gray-900",(E.value,"text-sm")])},K(f.time),3),r("div",{class:Q(["font-semibold text-teal-600",(E.value,"text-sm")])}," £"+K(f.price),3)]),r("div",{class:Q(["text-gray-500 mb-1",(E.value,"text-xs")])},K(f.provider.replace("Dr. ","")),3),m.value===""?(T(),M("div",{key:0,class:Q(["text-gray-400",(E.value,"text-xs")])},K(f.date.split(",")[0]),3)):ee("",!0)],10,cs))),128))],2),c.value.length>9?(T(),M("div",us,[r("button",{onClick:d[2]||(d[2]=f=>b.value=!b.value),class:"text-xs text-teal-600 hover:text-teal-700 font-medium px-3 py-1 rounded-md hover:bg-teal-50 transition-colors"},K(b.value?"Show less":`Show ${c.value.length-9} more slots`),1)])):ee("",!0)])),r("div",fs,[r("button",{onClick:d[3]||(d[3]=f=>l.value=!1),class:"text-xs text-gray-500 hover:text-gray-700 px-3 py-1 rounded-md hover:bg-gray-100 transition-colors duration-200"}," Close "),r("div",gs,K(m.value?"Filtered by date":"All available days"),1)])],2)],2),r("div",ms,K(Ee(new Date)),1)])],2)])):ee("",!0)],512)])])),r("div",ps,[d[19]||(d[19]=r("div",{class:"absolute inset-x-0 bottom-0 h-20 sm:h-24 bg-gray-50 -z-10"},null,-1)),r("div",{class:Q(["bg-gray-50 relative",E.value?"px-3 py-3":"px-6 py-4"])},[r("div",hs,[oe(an,{ref_key:"chatInputRef",ref:n,modelValue:s.value,"onUpdate:modelValue":d[4]||(d[4]=f=>s.value=f),placeholder:"Type your health question...","is-loading":t.value,"show-tools":!1,"show-version":!1,onSend:re,onKeydown:Pe},null,8,["modelValue","is-loading"])])],2)])]),oe(Lo,{"is-open":I.value,onClose:Ie},null,8,["is-open"])]),_:1})],64))}},As=Kt(vs,[["__scopeId","data-v-91ef519d"]]);export{As as default};
