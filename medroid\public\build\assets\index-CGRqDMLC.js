import{c as w,a1 as s,a3 as i,B as u,u as v,a4 as m,a5 as l,a6 as y,w as g,a7 as O,a8 as h,a2 as S,a9 as b,aa as A,Z as P,G as c}from"./vendor-BhKTHoN5.js";function E(e,n){var r;const t=S();return b(()=>{t.value=e()},{...n,flush:(r=void 0)!=null?r:"sync"}),A(t)}function f(e){return O()?(h(e),!0):!1}function G(e){let n=!1,r;const t=l(!0);return(...o)=>(n||(r=t.run(()=>e(...o)),n=!0),r)}function B(e){let n=0,r,t;const o=()=>{n-=1,t&&n<=0&&(t.stop(),r=void 0,t=void 0)};return(...a)=>(n+=1,t||(t=l(!0),r=t.run(()=>e(...a))),f(o),r)}function _(e){if(!i(e))return u(e);const n=new Proxy({},{get(r,t,o){return v(Reflect.get(e.value,t,o))},set(r,t,o){return i(e.value[t])&&!i(o)?e.value[t].value=o:e.value[t]=o,!0},deleteProperty(r,t){return Reflect.deleteProperty(e.value,t)},has(r,t){return Reflect.has(e.value,t)},ownKeys(){return Object.keys(e.value)},getOwnPropertyDescriptor(){return{enumerable:!0,configurable:!0}}});return u(n)}function C(e){return _(w(e))}function K(e,...n){const r=n.flat(),t=r[0];return C(()=>Object.fromEntries(typeof t=="function"?Object.entries(c(e)).filter(([o,a])=>!t(s(a),o)):Object.entries(c(e)).filter(o=>!r.includes(o[0]))))}const T=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const U=e=>typeof e<"u",I=Object.prototype.toString,W=e=>I.call(e)==="[object Object]",j=k();function k(){var e,n;return T&&((e=window==null?void 0:window.navigator)==null?void 0:e.userAgent)&&(/iP(?:ad|hone|od)/.test(window.navigator.userAgent)||((n=window==null?void 0:window.navigator)==null?void 0:n.maxTouchPoints)>2&&/iPad|Macintosh/.test(window==null?void 0:window.navigator.userAgent))}function x(e){return P()}function z(e){return Array.isArray(e)?e:[e]}function L(e,n=1e4){return m((r,t)=>{let o=s(e),a;const d=()=>setTimeout(()=>{o=s(e),t()},s(n));return f(()=>{clearTimeout(a)}),{get(){return r(),o},set(p){o=p,t(),clearTimeout(a),a=d()}}})}const M=s;function R(e,n){x()&&y(e,n)}function V(e,n,r){return g(e,n,{...r,immediate:!0})}export{U as a,f as b,W as c,M as d,L as e,E as f,G as g,B as h,T as i,R as j,j as k,K as r,z as t,V as w};
