import{_ as C}from"./AppLayout.vue_vue_type_script_setup_true_lang-DwGvFXKf.js";import{d as o,e as d,f as i,u as n,m as S,g as l,i as t,n as m,t as r,A as _,F as c,p as f,P as x,x as y,y as B}from"./vendor-BhKTHoN5.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const $={class:"flex items-center justify-between"},N={class:"flex mt-2","aria-label":"Breadcrumb"},P={class:"inline-flex items-center space-x-1 md:space-x-3"},V={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},D={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},A={class:"flex space-x-3"},E={class:"py-12"},I={class:"mx-auto max-w-4xl sm:px-6 lg:px-8"},O={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6"},j={class:"p-6 text-gray-900 dark:text-gray-100"},z={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},F={class:"text-sm text-gray-900 dark:text-gray-100"},L={class:"text-sm text-gray-900 dark:text-gray-100"},K={class:"text-sm text-gray-900 dark:text-gray-100"},M={class:"text-sm text-gray-900 dark:text-gray-100"},R={class:"text-sm text-gray-900 dark:text-gray-100"},T={key:0,class:"mt-6"},U={class:"text-sm text-gray-900 dark:text-gray-100"},q={key:0,class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6"},G={class:"p-6 text-gray-900 dark:text-gray-100"},H={class:"overflow-x-auto"},J={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Q={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},W={class:"px-6 py-4 whitespace-nowrap"},X={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},Y={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},Z={class:"px-6 py-4 whitespace-nowrap"},tt={class:"text-sm text-gray-900 dark:text-gray-100"},et={class:"px-6 py-4 whitespace-nowrap"},st={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},at={key:1,class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},rt={class:"p-6 text-gray-900 dark:text-gray-100"},dt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},ot={class:"font-medium text-gray-900 dark:text-gray-100"},lt={class:"text-sm text-gray-500 dark:text-gray-400"},it={class:"text-sm text-gray-900 dark:text-gray-100"},nt={class:"mt-2"},ut={__name:"Show",props:{category:Object},setup(a){var h,v;const b=a,u=[{title:"Dashboard",href:"/dashboard"},{title:"Categories",href:"/admin/categories"},{title:((h=b.category)==null?void 0:h.name)||"Category Details",href:`/admin/categories/${(v=b.category)==null?void 0:v.id}`}],k=p=>p?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";return(p,e)=>{var w;return d(),o(c,null,[i(n(S),{title:`Category: ${(w=a.category)==null?void 0:w.name}`},null,8,["title"]),i(C,null,{header:l(()=>[t("div",$,[t("div",null,[e[1]||(e[1]=t("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Category Details ",-1)),t("nav",N,[t("ol",P,[(d(),o(c,null,f(u,(g,s)=>t("li",{key:s,class:"inline-flex items-center"},[s<u.length-1?(d(),B(n(x),{key:0,href:g.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:l(()=>[y(r(g.title),1)]),_:2},1032,["href"])):(d(),o("span",V,r(g.title),1)),s<u.length-1?(d(),o("svg",D,e[0]||(e[0]=[t("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):m("",!0)])),64))])])]),t("div",A,[i(n(x),{href:"/admin/categories",class:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"},{default:l(()=>e[2]||(e[2]=[t("i",{class:"fas fa-arrow-left mr-2"},null,-1),y(" Back to Categories ")])),_:1}),i(n(x),{href:`/admin/categories/${a.category.id}/edit`,class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"},{default:l(()=>e[3]||(e[3]=[t("i",{class:"fas fa-edit mr-2"},null,-1),y(" Edit Category ")])),_:1},8,["href"])])])]),default:l(()=>{var g;return[t("div",E,[t("div",I,[t("div",O,[t("div",j,[e[11]||(e[11]=t("h3",{class:"text-lg font-semibold mb-4"},"Category Information",-1)),t("div",z,[t("div",null,[e[4]||(e[4]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Name ",-1)),t("p",F,r(a.category.name),1)]),t("div",null,[e[5]||(e[5]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Slug ",-1)),t("p",L,r(a.category.slug),1)]),t("div",null,[e[6]||(e[6]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Parent Category ",-1)),t("p",K,r(((g=a.category.parent)==null?void 0:g.name)||"Root Category"),1)]),t("div",null,[e[7]||(e[7]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Sort Order ",-1)),t("p",M,r(a.category.sort_order),1)]),t("div",null,[e[8]||(e[8]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Status ",-1)),t("span",{class:_([k(a.category.is_active),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},r(a.category.is_active?"Active":"Inactive"),3)]),t("div",null,[e[9]||(e[9]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Products Count ",-1)),t("p",R,r(a.category.products_count||0),1)])]),a.category.description?(d(),o("div",T,[e[10]||(e[10]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Description ",-1)),t("p",U,r(a.category.description),1)])):m("",!0)])]),a.category.children&&a.category.children.length>0?(d(),o("div",q,[t("div",G,[e[15]||(e[15]=t("h3",{class:"text-lg font-semibold mb-4"},"Subcategories",-1)),t("div",H,[t("table",J,[e[14]||(e[14]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Name "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Sort Order "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Status "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Actions ")])],-1)),t("tbody",Q,[(d(!0),o(c,null,f(a.category.children,s=>(d(),o("tr",{key:s.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[t("td",W,[t("div",X,r(s.name),1),s.description?(d(),o("div",Y,r(s.description),1)):m("",!0)]),t("td",Z,[t("span",tt,r(s.sort_order),1)]),t("td",et,[t("span",{class:_([k(s.is_active),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},r(s.is_active?"Active":"Inactive"),3)]),t("td",st,[i(n(x),{href:`/admin/categories/${s.id}`,class:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"},{default:l(()=>e[12]||(e[12]=[y(" View ")])),_:2},1032,["href"]),i(n(x),{href:`/admin/categories/${s.id}/edit`,class:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"},{default:l(()=>e[13]||(e[13]=[y(" Edit ")])),_:2},1032,["href"])])]))),128))])])])])])):m("",!0),a.category.products&&a.category.products.length>0?(d(),o("div",at,[t("div",rt,[e[17]||(e[17]=t("h3",{class:"text-lg font-semibold mb-4"},"Products in this Category",-1)),t("div",dt,[(d(!0),o(c,null,f(a.category.products,s=>(d(),o("div",{key:s.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4"},[t("h4",ot,r(s.name),1),t("p",lt,"SKU: "+r(s.sku),1),t("p",it,"$"+r(s.price),1),t("div",nt,[i(n(x),{href:`/admin/products/${s.id}`,class:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 text-sm"},{default:l(()=>e[16]||(e[16]=[y(" View Product ")])),_:2},1032,["href"])])]))),128))])])])):m("",!0)])])]}),_:1})],64)}}};export{ut as default};
