import{d as a,e as n,i as e,n as c,A as r,t as i,F as x,p as h,y as g,x as y,ab as f}from"./vendor-BhKTHoN5.js";const b={class:"max-w-md mx-auto text-center px-6"},v={class:"text-4xl font-bold text-gray-900 mb-4"},k={class:"text-lg text-gray-600 mb-8 leading-relaxed"},w={class:"bg-white rounded-2xl shadow-sm border border-gray-100 p-6 mb-8"},p={class:"space-y-3 text-left"},B={class:"text-gray-700"},C={key:0,class:"space-y-4"},S={key:1,class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},D={key:0,class:"text-sm text-gray-500"},H={__name:"ComingS<PERSON>",props:{title:{type:String,default:"Coming Soon"},description:{type:String,required:!0},features:{type:Array,required:!0},actionButton:{type:Object,default:()=>({text:"Go Back",action:null,icon:null})},actionDescription:{type:String,default:""},minHeight:{type:String,default:"min-h-[600px]"},iconGradient:{type:String,default:"from-teal-500 to-cyan-500"},buttonGradient:{type:String,default:"from-teal-500 to-cyan-500"},buttonHoverGradient:{type:String,default:"from-teal-600 to-cyan-600"},featureDotColor:{type:String,default:"bg-teal-500"}},emits:["action"],setup(t,{emit:d}){const s=t,u=d,m=()=>{s.actionButton.action&&u("action",s.actionButton.action)};return(G,o)=>(n(),a("div",{class:r([t.minHeight,"bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center"])},[e("div",b,[e("div",{class:r(["w-24 h-24 mx-auto mb-8 bg-gradient-to-br rounded-full flex items-center justify-center shadow-lg",t.iconGradient])},o[0]||(o[0]=[e("svg",{class:"w-12 h-12 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})],-1)]),2),e("h1",v,i(t.title),1),e("p",k,i(t.description),1),e("div",w,[o[1]||(o[1]=e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"What's Coming:",-1)),e("div",p,[(n(!0),a(x,null,h(t.features,l=>(n(),a("div",{key:l,class:"flex items-center space-x-3"},[e("div",{class:r(["w-2 h-2 rounded-full",t.featureDotColor])},null,2),e("span",B,i(l),1)]))),128))])]),t.actionButton.text?(n(),a("div",C,[e("button",{onClick:m,class:r(["inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5",`${t.buttonGradient} hover:${t.buttonHoverGradient}`])},[t.actionButton.icon?(n(),g(f(t.actionButton.icon),{key:0,class:"w-5 h-5 mr-2"})):(n(),a("svg",S,o[2]||(o[2]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"},null,-1)]))),y(" "+i(t.actionButton.text),1)],2),t.actionDescription?(n(),a("p",D,i(t.actionDescription),1)):c("",!0)])):c("",!0)])],2))}};export{H as _};
