import{r as f,c as k,o as O,d as l,e as n,f as M,u as j,m as $,g as A,i as t,n as u,t as o,l as X,q as G,x as V,F as y,p as C,N as K,A as D,y as Q,P as z,a as R}from"./vendor-BhKTHoN5.js";import{_ as W}from"./AppLayout.vue_vue_type_script_setup_true_lang-DwGvFXKf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const J={class:"p-6"},Y={class:"dashboard-container"},Z={class:"dashboard-header flex items-center justify-between"},tt={class:"header-content"},et={class:"dashboard-title"},st={class:"dashboard-subtitle"},ot={class:"header-actions"},rt=["disabled"],at={key:0,class:"fas fa-spinner fa-spin"},nt={key:1,class:"fas fa-sync-alt"},lt={class:"ml-2"},it={key:0,class:""},dt={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},ct={class:"bg-blue-50 rounded-lg p-4 border-l-4 border-blue-500"},ut={class:"flex justify-between"},mt={class:"text-2xl font-bold text-blue-600"},pt={class:"bg-green-50 rounded-lg p-4 border-l-4 border-green-500"},gt={class:"flex justify-between"},vt={class:"text-2xl font-bold text-green-600"},xt={class:"bg-purple-50 rounded-lg p-4 border-l-4 border-purple-500"},ht={class:"flex justify-between"},bt={class:"text-2xl font-bold text-purple-600"},ft={class:"bg-yellow-50 rounded-lg p-4 border-l-4 border-yellow-500"},yt={class:"flex justify-between"},_t={class:"text-2xl font-bold text-yellow-600"},wt={key:1,class:""},kt={key:0,class:"bg-white rounded-lg shadow p-6 mb-6"},Mt={key:1,class:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6"},jt={class:"flex"},At={class:"ml-3"},Ct={class:"mt-1 text-sm text-red-700"},Dt={key:2,class:"kpi-grid grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-6"},Pt={class:"bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl shadow-lg p-6 border border-blue-300 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"},Bt={class:"flex items-center justify-between"},Lt={class:"flex-1"},Vt={class:"text-3xl font-bold text-gray-800 mb-2"},zt={class:"flex items-center"},Rt={key:0,class:"text-sm text-green-600 flex items-center font-medium"},St={class:"bg-gradient-to-br from-green-100 to-green-200 rounded-xl shadow-lg p-6 border border-green-300 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"},Tt={class:"flex items-center justify-between"},Nt={class:"flex-1"},Et={class:"text-3xl font-bold text-gray-800 mb-2"},Ut={class:"flex items-center"},Ht={class:"text-sm text-gray-600 font-medium"},qt={class:"bg-gradient-to-br from-yellow-100 to-yellow-200 rounded-xl shadow-lg p-6 border border-yellow-300 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"},Ft={class:"flex items-center justify-between"},It={class:"flex-1"},Ot={class:"text-3xl font-bold text-gray-800 mb-2"},$t={class:"flex items-center"},Xt={class:"text-sm text-gray-600 font-medium"},Gt={class:"bg-gradient-to-br from-indigo-100 to-indigo-200 rounded-xl shadow-lg p-6 border border-indigo-300 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"},Kt={class:"flex items-center justify-between"},Qt={class:"flex-1"},Wt={class:"text-3xl font-bold text-gray-800 mb-2"},Jt={class:"flex items-center"},Yt={class:"text-sm text-gray-600 font-medium"},Zt={class:"bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl shadow-lg p-6 border border-purple-200 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"},te={class:"flex items-center justify-between"},ee={class:"flex-1"},se={class:"text-3xl font-bold text-gray-800 mb-2"},oe={class:"flex items-center"},re={class:"text-sm text-gray-600 font-medium"},ae={key:3,class:"secondary-metrics grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6"},ne={class:"bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-all duration-300"},le={class:"space-y-4"},ie={class:"flex justify-between items-center py-2 border-b border-gray-100"},de={class:"text-lg font-bold text-gray-800"},ce={class:"flex justify-between items-center py-2"},ue={class:"text-lg font-bold text-gray-800"},me={class:"bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-all duration-300"},pe={class:"space-y-4"},ge={class:"flex justify-between items-center py-2 border-b border-gray-100"},ve={class:"text-lg font-bold text-emerald-600"},xe={class:"flex justify-between items-center py-2"},he={class:"text-lg font-bold text-gray-800"},be={class:"bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-all duration-300"},fe={class:"space-y-4"},ye={class:"flex justify-between items-center py-2 border-b border-gray-100"},_e={class:"text-lg font-bold text-gray-800"},we={class:"flex justify-between items-center py-2"},ke={class:"text-lg font-bold text-gray-800"},Me={key:4,class:"location-analytics bg-white rounded-xl shadow-lg p-6 mb-6 border border-gray-100"},je={class:"space-y-4"},Ae={class:"flex justify-between items-center mb-3"},Ce={class:"text-base font-semibold text-gray-700"},De={class:"text-sm font-medium text-gray-600 bg-white px-3 py-1 rounded-full"},Pe={class:"w-full bg-gray-200 rounded-full h-3 overflow-hidden"},Be={key:5,class:"text-center py-4 border-t border-gray-100"},Le={class:"flex items-center justify-center text-sm text-gray-500"},Ve={key:2,class:"bg-white overflow-hidden shadow-sm rounded-lg mb-6"},ze={class:"p-6"},Re={class:"space-y-4"},Se={class:"flex justify-between items-start"},Te={class:"font-medium text-gray-900"},Ne={class:"text-sm text-gray-600"},Ee={class:"text-sm text-gray-500 mt-1"},Ue={key:3,class:"bg-white overflow-hidden shadow-sm rounded-lg"},He={class:"p-6"},qe={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Fe={class:"text-sm font-medium text-gray-900 text-center"},Ie={class:"text-xs text-gray-500 text-center mt-1"},Oe={key:4,class:"bg-white overflow-hidden shadow-sm rounded-lg"},$e={class:"p-6"},Xe={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Ye={__name:"Dashboard",props:{stats:{type:Object,default:()=>({upcomingAppointments:0,completedAppointments:0,healthRecords:0,totalPatients:0,monthlyEarnings:0,completionRate:0})},recentAppointments:{type:Array,default:()=>[]},quickActions:{type:Array,default:()=>[]},kpiData:{type:Object,default:()=>({})},user:{type:Object,default:()=>null},providerData:{type:Object,default:()=>({weeklyAvailability:[],nextAppointments:[],earnings:{},patientCount:0})}},setup(p){const c=p,S=[{title:"Dashboard",href:"/dashboard"}],d=f(!1),_=f("Never"),b=f("admin"),g=f(null),a=f({total_users:0,monthly_active_users:0,daily_active_users:0,user_growth_rate:0,total_appointments:0,monthly_appointments:0,daily_appointments:0,appointment_completion_rate:0,total_consults:0,monthly_consults:0,daily_consults:0,consult_to_appointment_ratio:0,repeat_consult_users:0,diagnostic_accuracy:0,diagnostic_feedback_count:0,top_accurate_diagnoses:[],top_inaccurate_diagnoses:[],user_locations:[],total_revenue:0,monthly_revenue:0,average_appointment_value:0,total_clinics:0,active_clinics:0,clinics_accepting_patients:0,telemedicine_clinics:0,clinic_activation_rate:0,telemedicine_adoption_rate:0,clinics_by_state:[],top_clinics_by_providers:[],top_clinics_by_patients:[],...c.kpiData}),x=k(()=>{var r,e;return((e=(r=c.user)==null?void 0:r.roles)==null?void 0:e.some(s=>s.name==="admin"))||!1}),h=k(()=>{var r,e;return((e=(r=c.user)==null?void 0:r.roles)==null?void 0:e.some(s=>s.name==="provider"))||!1}),P=k(()=>{var r,e;return((e=(r=c.user)==null?void 0:r.roles)==null?void 0:e.some(s=>s.name==="patient"))||!1}),T=r=>new Date(r).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),N=r=>({scheduled:"bg-blue-100 text-blue-800",confirmed:"bg-green-100 text-green-800",in_progress:"bg-yellow-100 text-yellow-800",completed:"bg-gray-100 text-gray-800",cancelled:"bg-red-100 text-red-800"})[r]||"bg-gray-100 text-gray-800",E=()=>h.value?"Provider Dashboard":P.value?"Patient Dashboard":x.value?{admin:"Admin Dashboard",provider:"Provider Dashboard",patient:"Patient Dashboard",care_manager:"Care Manager Dashboard"}[b.value]||"Admin Dashboard":"Dashboard",U=()=>h.value?"Manage your patients, availability, and appointments":P.value?"Track your health and appointments":x.value?{admin:"Manage your healthcare platform from one place",provider:"Manage your patients and appointments",patient:"Track your health and appointments",care_manager:"Monitor patient care and compliance"}[b.value]||"Manage your healthcare platform":"Manage your healthcare services",H=r=>{if(!a.value.user_locations||a.value.user_locations.length===0)return 0;const e=Math.max(...a.value.user_locations.map(s=>s.count));return Math.round(r/e*100)},B=r=>new Intl.NumberFormat("en-GB",{style:"currency",currency:"GBP",minimumFractionDigits:2}).format(r),i=r=>new Intl.NumberFormat("en-US").format(r),q=()=>{w(!0)},w=async(r=!1)=>{d.value=!0,g.value=null;try{const e=await R.get("/management/dashboard/kpi",{params:{refresh:r},headers:{Accept:"application/json","X-Requested-With":"XMLHttpRequest"}});a.value={...a.value,...e.data},_.value=new Date().toLocaleString()}catch(e){console.error("Error fetching KPI data:",e),g.value="Failed to load dashboard data. Please try again."}finally{d.value=!1}},F=async()=>{var r,e,s;if(h.value){d.value=!0;try{const m=(r=document.querySelector('meta[name="csrf-token"]'))==null?void 0:r.getAttribute("content"),v=await R.get("/provider/get-dashboard-data",{headers:{Accept:"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":m||""},withCredentials:!0});v.data&&(Object.assign(c.stats,v.data.stats),v.data.todayAppointments&&c.recentAppointments.splice(0,c.recentAppointments.length,...v.data.todayAppointments))}catch(m){console.error("Error fetching provider data:",m),(((e=m.response)==null?void 0:e.status)===401||((s=m.response)==null?void 0:s.status)===403)&&console.warn("User does not have provider access - this is expected for non-provider users")}finally{d.value=!1}}},I=()=>[{title:"Manage Availability",href:"/provider/availability",icon:"calendar-alt",description:"Set your working hours and time slots",color:"blue"},{title:"View Appointments",href:"/provider/appointments",icon:"calendar-check",description:"See your upcoming appointments",color:"green"},{title:"Patient List",href:"/provider/patients",icon:"users",description:"Manage your patient records",color:"purple"},{title:"Earnings",href:"/provider/earnings",icon:"dollar-sign",description:"Track your earnings and payments",color:"yellow"}];return O(()=>{c.user&&(b.value=c.user.role||"admin"),h.value?F():x.value&&w()}),(r,e)=>(n(),l(y,null,[M(j($),{title:"Dashboard"}),M(W,{breadcrumbs:S},{default:A(()=>[t("div",J,[t("div",Y,[t("div",Z,[t("div",tt,[t("h1",et,o(E()),1),t("p",st,o(U()),1)]),t("div",ot,[x.value?X((n(),l("select",{key:0,"onUpdate:modelValue":e[0]||(e[0]=s=>b.value=s),onChange:q,class:"role-selector"},e[2]||(e[2]=[t("option",{value:"admin"},"Admin View",-1),t("option",{value:"provider"},"Provider View",-1),t("option",{value:"patient"},"Patient View",-1),t("option",{value:"care_manager"},"Care Manager View",-1)]),544)),[[G,b.value]]):u("",!0),x.value?(n(),l("button",{key:1,onClick:e[1]||(e[1]=s=>w(!0)),class:"refresh-btn",disabled:d.value},[d.value?(n(),l("i",at)):(n(),l("i",nt)),t("span",lt,o(d.value?"Refreshing...":"Refresh Data"),1)],8,rt)):u("",!0)])]),h.value?(n(),l("div",it,[e[15]||(e[15]=t("h3",{class:"text-lg font-semibold text-gray-700 mb-4"},"Provider Overview",-1)),t("div",dt,[t("div",ct,[t("div",ut,[t("div",null,[e[3]||(e[3]=t("p",{class:"text-sm text-gray-500"},"Total Patients",-1)),t("p",mt,o(p.stats.totalPatients||0),1),e[4]||(e[4]=t("p",{class:"text-xs text-gray-400"},"Active patients",-1))]),e[5]||(e[5]=t("div",{class:"text-blue-500"},[t("i",{class:"fas fa-users text-2xl"})],-1))])]),t("div",pt,[t("div",gt,[t("div",null,[e[6]||(e[6]=t("p",{class:"text-sm text-gray-500"},"Upcoming Appointments",-1)),t("p",vt,o(p.stats.upcomingAppointments||0),1),e[7]||(e[7]=t("p",{class:"text-xs text-gray-400"},"Next 7 days",-1))]),e[8]||(e[8]=t("div",{class:"text-green-500"},[t("i",{class:"fas fa-calendar-check text-2xl"})],-1))])]),t("div",xt,[t("div",ht,[t("div",null,[e[9]||(e[9]=t("p",{class:"text-sm text-gray-500"},"Completion Rate",-1)),t("p",bt,o(p.stats.completionRate||0)+"%",1),e[10]||(e[10]=t("p",{class:"text-xs text-gray-400"},"This month",-1))]),e[11]||(e[11]=t("div",{class:"text-purple-500"},[t("i",{class:"fas fa-chart-line text-2xl"})],-1))])]),t("div",ft,[t("div",yt,[t("div",null,[e[12]||(e[12]=t("p",{class:"text-sm text-gray-500"},"Monthly Earnings",-1)),t("p",_t,"$"+o(p.stats.monthlyEarnings||0),1),e[13]||(e[13]=t("p",{class:"text-xs text-gray-400"},"Current month",-1))]),e[14]||(e[14]=t("div",{class:"text-yellow-500"},[t("i",{class:"fas fa-dollar-sign text-2xl"})],-1))])])])])):u("",!0),x.value?(n(),l("div",wt,[d.value?(n(),l("div",kt,e[16]||(e[16]=[t("div",{class:"flex items-center justify-center py-8"},[t("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),t("span",{class:"ml-3 text-gray-600"},"Loading KPI data...")],-1)]))):g.value?(n(),l("div",Mt,[t("div",jt,[e[18]||(e[18]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-5 w-5 text-red-400",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})])],-1)),t("div",At,[e[17]||(e[17]=t("h3",{class:"text-sm font-medium text-red-800"},"Error Loading Dashboard",-1)),t("p",Ct,o(g.value),1)])])])):(n(),l("div",Dt,[t("div",Pt,[t("div",Bt,[t("div",Lt,[e[21]||(e[21]=t("div",{class:"flex items-center mb-2"},[t("div",{class:"p-2 bg-blue-500 rounded-lg mr-3"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"})])]),t("h3",{class:"text-sm font-semibold text-gray-600 uppercase tracking-wide"},"Total Users")],-1)),t("p",Vt,o(i(a.value.total_users||0)),1),t("div",zt,[a.value.user_growth_rate>0?(n(),l("span",Rt,[e[19]||(e[19]=t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 10l7-7m0 0l7 7m-7-7v18"})],-1)),V(" +"+o(a.value.user_growth_rate||0)+"% ",1)])):u("",!0),e[20]||(e[20]=t("span",{class:"text-sm text-gray-500 ml-2"},"this month",-1))])])])]),t("div",St,[t("div",Tt,[t("div",Nt,[e[23]||(e[23]=t("div",{class:"flex items-center mb-2"},[t("div",{class:"p-2 bg-green-500 rounded-lg mr-3"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])]),t("h3",{class:"text-sm font-semibold text-gray-600 uppercase tracking-wide"},"Appointments")],-1)),t("p",Et,o(i(a.value.total_appointments||0)),1),t("div",Ut,[t("span",Ht,o(i(a.value.monthly_appointments||0)),1),e[22]||(e[22]=t("span",{class:"text-sm text-gray-500 ml-2"},"this month",-1))])])])]),t("div",qt,[t("div",Ft,[t("div",It,[e[25]||(e[25]=t("div",{class:"flex items-center mb-2"},[t("div",{class:"p-2 bg-yellow-500 rounded-lg mr-3"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])]),t("h3",{class:"text-sm font-semibold text-gray-600 uppercase tracking-wide"},"Total Revenue")],-1)),t("p",Ot,o(B(a.value.total_revenue||0)),1),t("div",$t,[t("span",Xt,o(B(a.value.monthly_revenue||0)),1),e[24]||(e[24]=t("span",{class:"text-sm text-gray-500 ml-2"},"this month",-1))])])])]),t("div",Gt,[t("div",Kt,[t("div",Qt,[e[27]||(e[27]=t("div",{class:"flex items-center mb-2"},[t("div",{class:"p-2 bg-indigo-500 rounded-lg mr-3"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})])]),t("h3",{class:"text-sm font-semibold text-gray-600 uppercase tracking-wide"},"Total Clinics")],-1)),t("p",Wt,o(i(a.value.total_clinics||0)),1),t("div",Jt,[t("span",Yt,o(i(a.value.active_clinics||0)),1),e[26]||(e[26]=t("span",{class:"text-sm text-gray-500 ml-2"},"active",-1))])])])]),t("div",Zt,[t("div",te,[t("div",ee,[e[29]||(e[29]=t("div",{class:"flex items-center mb-2"},[t("div",{class:"p-2 bg-purple-500 rounded-lg mr-3"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})])]),t("h3",{class:"text-sm font-semibold text-gray-600 uppercase tracking-wide"},"AI Accuracy")],-1)),t("p",se,o(a.value.diagnostic_accuracy||0)+"%",1),t("div",oe,[t("span",re,o(i(a.value.diagnostic_feedback_count||0)),1),e[28]||(e[28]=t("span",{class:"text-sm text-gray-500 ml-2"},"feedbacks",-1))])])])])])),!d.value&&!g.value?(n(),l("div",ae,[t("div",ne,[e[32]||(e[32]=t("div",{class:"flex items-center mb-4"},[t("div",{class:"p-2 bg-indigo-100 rounded-lg mr-3"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-indigo-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])]),t("h4",{class:"text-lg font-semibold text-gray-700"},"User Activity")],-1)),t("div",le,[t("div",ie,[e[30]||(e[30]=t("span",{class:"text-sm text-gray-600 font-medium"},"Daily Active",-1)),t("span",de,o(i(a.value.daily_active_users||0)),1)]),t("div",ce,[e[31]||(e[31]=t("span",{class:"text-sm text-gray-600 font-medium"},"Monthly Active",-1)),t("span",ue,o(i(a.value.monthly_active_users||0)),1)])])]),t("div",me,[e[35]||(e[35]=t("div",{class:"flex items-center mb-4"},[t("div",{class:"p-2 bg-emerald-100 rounded-lg mr-3"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-emerald-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])]),t("h4",{class:"text-lg font-semibold text-gray-700"},"Appointment Stats")],-1)),t("div",pe,[t("div",ge,[e[33]||(e[33]=t("span",{class:"text-sm text-gray-600 font-medium"},"Completion Rate",-1)),t("span",ve,o(a.value.appointment_completion_rate||0)+"%",1)]),t("div",xe,[e[34]||(e[34]=t("span",{class:"text-sm text-gray-600 font-medium"},"Daily Appointments",-1)),t("span",he,o(i(a.value.daily_appointments||0)),1)])])]),t("div",be,[e[38]||(e[38]=t("div",{class:"flex items-center mb-4"},[t("div",{class:"p-2 bg-violet-100 rounded-lg mr-3"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-violet-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})])]),t("h4",{class:"text-lg font-semibold text-gray-700"},"AI Consults")],-1)),t("div",fe,[t("div",ye,[e[36]||(e[36]=t("span",{class:"text-sm text-gray-600 font-medium"},"Total Consults",-1)),t("span",_e,o(i(a.value.total_consults||0)),1)]),t("div",we,[e[37]||(e[37]=t("span",{class:"text-sm text-gray-600 font-medium"},"Monthly Consults",-1)),t("span",ke,o(i(a.value.monthly_consults||0)),1)])])])])):u("",!0),!d.value&&!g.value&&a.value.user_locations&&a.value.user_locations.length>0?(n(),l("div",Me,[e[39]||(e[39]=t("div",{class:"flex items-center mb-6"},[t("div",{class:"p-2 bg-blue-100 rounded-lg mr-3"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6 text-blue-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})])]),t("h3",{class:"text-xl font-bold text-gray-800"},"Provider Locations")],-1)),t("div",je,[(n(!0),l(y,null,C(a.value.user_locations.slice(0,5),s=>(n(),l("div",{key:s.location,class:"bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors duration-200"},[t("div",Ae,[t("span",Ce,o(s.location),1),t("span",De,o(i(s.count))+" providers",1)]),t("div",Pe,[t("div",{class:"bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full transition-all duration-500 ease-out",style:K({width:H(s.count)+"%"})},null,4)])]))),128))])])):u("",!0),!d.value&&!g.value&&_.value?(n(),l("div",Be,[t("div",Le,[e[40]||(e[40]=t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),V(" Last updated: "+o(_.value),1)])])):u("",!0)])):u("",!0),p.recentAppointments.length>0?(n(),l("div",Ve,[t("div",ze,[e[41]||(e[41]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Recent Appointments",-1)),t("div",Re,[(n(!0),l(y,null,C(p.recentAppointments,s=>{var m,v,L;return n(),l("div",{key:s.id,class:"border border-gray-200 rounded-lg p-4"},[t("div",Se,[t("div",null,[t("h4",Te," Dr. "+o(((v=(m=s.provider)==null?void 0:m.user)==null?void 0:v.name)||"Unknown Provider"),1),t("p",Ne,o(((L=s.provider)==null?void 0:L.specialization)||"General Practice"),1),t("p",Ee,o(T(s.scheduled_at)),1)]),t("span",{class:D([N(s.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},o(s.status),3)])])}),128))])])])):u("",!0),h.value?(n(),l("div",Ue,[t("div",He,[e[42]||(e[42]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Quick Actions",-1)),t("div",qe,[(n(!0),l(y,null,C(I(),s=>(n(),Q(j(z),{key:s.title,href:s.href,class:"flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"},{default:A(()=>[t("div",{class:D(`w-12 h-12 bg-${s.color}-100 rounded-full flex items-center justify-center mb-3`)},[t("i",{class:D(`fas fa-${s.icon} text-${s.color}-600 text-xl`)},null,2)],2),t("span",Fe,o(s.title),1),t("span",Ie,o(s.description),1)]),_:2},1032,["href"]))),128))])])])):(n(),l("div",Oe,[t("div",$e,[e[44]||(e[44]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Quick Actions",-1)),t("div",Xe,[M(j(z),{href:"/chat",class:"flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"},{default:A(()=>e[43]||(e[43]=[t("div",{class:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3"},[t("svg",{class:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})])],-1),t("span",{class:"text-sm font-medium text-gray-900"},"AI Chat",-1)])),_:1})])])]))])])]),_:1})],64))}};export{Ye as default};
