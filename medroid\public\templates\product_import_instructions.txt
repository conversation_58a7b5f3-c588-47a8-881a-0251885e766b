PRODUCT IMPORT TEMPLATE INSTRUCTIONS
====================================

This template allows you to bulk import products into the system. Please follow these guidelines:

REQUIRED FIELDS:
- name: Product name (required)
- description: Detailed product description (required)
- type: Must be either "physical" or "digital" (required)
- category_id: Valid category ID number (required)
- price: Product price in decimal format (required)
- sku: Unique product identifier (required)

OPTIONAL FIELDS:
- short_description: Brief description for product cards
- sale_price: Discounted price (must be less than regular price)
- stock_quantity: Required for physical products, set to 0 for digital
- weight: Product weight in kg (for physical products)
- dimensions: Product dimensions in format "LxWxH"
- is_featured: Set to 1 for featured products, 0 for normal
- is_active: Set to 1 for active products, 0 for inactive
- download_limit: Number of downloads allowed (digital products only)
- download_expiry_days: Days until download expires (digital products only)

PRODUCT TYPES:
- physical: Tangible products that require shipping
- digital: Downloadable products (ebooks, software, courses)

CATEGORY IDs:
You need to use valid category IDs from your system. Common categories:
- 1: Health Supplements
- 2: Digital Products
- 3: Courses
- 4: Fitness Equipment
- 5: Wellness Products

FORMATTING RULES:
- Use CSV format with comma separators
- Enclose text fields in quotes if they contain commas
- Use decimal format for prices (e.g., 29.99)
- Use 1 for true/yes, 0 for false/no
- Leave optional fields empty if not applicable
- Ensure SKUs are unique across all products

SAMPLE DATA:
See the template file for examples of properly formatted product data.

VALIDATION:
- All required fields must be filled
- SKUs must be unique
- Category IDs must exist in the system
- Prices must be positive numbers
- Sale price must be less than regular price
- Stock quantity required for physical products

IMPORT PROCESS:
1. Download this template
2. Fill in your product data
3. Save as CSV format
4. Upload through the bulk import feature
5. Review validation results
6. Confirm import if validation passes
