import{_ as B}from"./AppLayout.vue_vue_type_script_setup_true_lang-DwGvFXKf.js";import{r as g,c as v,o as T,d as a,e as l,f as u,u as m,m as V,g as x,i as t,t as i,n as y,P as w,x as f,F as k,p as E,a as p,W as I}from"./vendor-BhKTHoN5.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const L={class:"py-12"},N={class:"max-w-7xl mx-auto sm:px-6 lg:px-8"},P={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6"},A={class:"p-6 bg-white border-b border-gray-200"},D={class:"flex items-center justify-between"},H={class:"text-gray-600 mt-1"},R={class:"flex items-center space-x-4"},W={key:0,class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},Y={key:1,class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},z={class:"p-12 text-center"},O={key:2,class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},G={class:"lg:col-span-2"},J={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},K={class:"p-6"},U={class:"space-y-4"},X={class:"flex-shrink-0"},Z=["src","alt"],Q={key:1,class:"h-20 w-20 bg-gray-200 rounded-lg flex items-center justify-center"},tt={class:"flex-1 min-w-0"},et={class:"text-lg font-semibold text-gray-900"},st={class:"text-gray-600 text-sm"},ot={class:"flex items-center mt-2"},rt={class:"text-lg font-bold text-blue-600"},at={key:0,class:"ml-2 bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded"},lt={class:"flex items-center space-x-2"},dt=["onClick","disabled"],it={class:"w-12 text-center font-medium"},nt=["onClick","disabled"],ct={class:"text-right"},ut={class:"text-lg font-bold text-gray-900"},pt=["onClick","disabled"],gt={class:"lg:col-span-1"},vt={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg sticky top-6"},mt={class:"p-6"},xt={class:"space-y-3 mb-6"},ft={class:"flex justify-between"},ht={class:"font-medium"},_t={class:"border-t pt-3"},bt={class:"flex justify-between"},yt={class:"text-lg font-bold text-blue-600"},$t={__name:"Cart",setup(wt){const C=[{title:"Shop",href:"/shop"},{title:"Shopping Cart",href:"/shop/cart"}],r=g([]),c=g(!1),n=g({}),F=v(()=>r.value.reduce((o,e)=>o+parseFloat(e.total_price||0),0)),h=v(()=>"$"+F.value.toFixed(2)),_=v(()=>r.value.reduce((o,e)=>o+e.quantity,0)),S=async()=>{c.value=!0;try{const o=await p.get("/shop/cart");r.value=o.data.cart_items||[]}catch(o){console.error("Error loading cart:",o),r.value=[]}finally{c.value=!1}},b=async(o,e)=>{if(!(e<0)){n.value[o.product_id]=!0;try{const s=await p.put(`/shop/cart/${o.product_id}`,{quantity:e});if(s.data.success)if(e===0)r.value=r.value.filter(d=>d.product_id!==o.product_id);else{const d=r.value.findIndex(M=>M.product_id===o.product_id);d!==-1&&(r.value[d].quantity=e,r.value[d].total_price=e*parseFloat(r.value[d].price||0))}else alert(s.data.message||"Failed to update cart")}catch(s){console.error("Error updating cart:",s),alert("Failed to update cart")}finally{n.value[o.product_id]=!1}}},j=async o=>{if(confirm("Remove this item from cart?")){n.value[o.product_id]=!0;try{const e=await p.delete(`/shop/cart/${o.product_id}`);e.data.success?r.value=r.value.filter(s=>s.product_id!==o.product_id):alert(e.data.message||"Failed to remove item")}catch(e){console.error("Error removing item:",e),alert("Failed to remove item")}finally{n.value[o.product_id]=!1}}},$=async()=>{if(confirm("Clear all items from cart?")){c.value=!0;try{const o=await p.delete("/shop/cart");o.data.success?r.value=[]:alert(o.data.message||"Failed to clear cart")}catch(o){console.error("Error clearing cart:",o),alert("Failed to clear cart")}finally{c.value=!1}}},q=()=>{if(r.value.length===0){alert("Your cart is empty");return}I.visit("/shop/checkout")};return T(()=>{S()}),(o,e)=>(l(),a(k,null,[u(m(V),{title:"Shopping Cart - Medroid"}),u(B,{breadcrumbs:C},{default:x(()=>[t("div",L,[t("div",N,[t("div",P,[t("div",A,[t("div",D,[t("div",null,[e[0]||(e[0]=t("h1",{class:"text-2xl font-bold text-gray-900"},"Shopping Cart",-1)),t("p",H,i(_.value)+" "+i(_.value===1?"item":"items")+" in your cart",1)]),t("div",R,[u(m(w),{href:"/shop",class:"inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"},{default:x(()=>e[1]||(e[1]=[t("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10 19l-7-7m0 0l7-7m-7 7h18"})],-1),f(" Continue Shopping ")])),_:1}),r.value.length>0?(l(),a("button",{key:0,onClick:$,class:"inline-flex items-center px-4 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors"},e[2]||(e[2]=[t("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),f(" Clear Cart ")]))):y("",!0)])])])]),c.value?(l(),a("div",W,e[3]||(e[3]=[t("div",{class:"p-12 text-center"},[t("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),t("p",{class:"mt-2 text-gray-600"},"Loading cart...")],-1)]))):r.value.length===0?(l(),a("div",Y,[t("div",z,[e[5]||(e[5]=t("div",{class:"text-6xl mb-4"},"🛒",-1)),e[6]||(e[6]=t("h2",{class:"text-2xl font-bold text-gray-900 mb-2"},"Your cart is empty",-1)),e[7]||(e[7]=t("p",{class:"text-gray-600 mb-6"},"Add some products to get started!",-1)),u(m(w),{href:"/shop",class:"inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"},{default:x(()=>e[4]||(e[4]=[t("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})],-1),f(" Start Shopping ")])),_:1})])])):(l(),a("div",O,[t("div",G,[t("div",J,[t("div",K,[e[9]||(e[9]=t("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Cart Items",-1)),t("div",U,[(l(!0),a(k,null,E(r.value,s=>(l(),a("div",{key:s.id,class:"flex items-center space-x-4 p-4 border border-gray-200 rounded-lg"},[t("div",X,[s.product.primary_image?(l(),a("img",{key:0,src:`/storage/${s.product.primary_image}`,alt:s.product.name,class:"h-20 w-20 object-cover rounded-lg"},null,8,Z)):(l(),a("div",Q,e[8]||(e[8]=[t("i",{class:"fas fa-image text-gray-400 text-2xl"},null,-1)])))]),t("div",tt,[t("h3",et,i(s.product.name),1),t("p",st,i(s.product.short_description),1),t("div",ot,[t("span",rt,"$"+i(parseFloat(s.price||0).toFixed(2)),1),s.product.type==="digital"?(l(),a("span",at," Digital ")):y("",!0)])]),t("div",lt,[t("button",{onClick:d=>b(s,s.quantity-1),disabled:n.value[s.product_id]||s.quantity<=1,class:"w-8 h-8 rounded-full bg-gray-100 text-gray-600 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"}," - ",8,dt),t("span",it,i(s.quantity),1),t("button",{onClick:d=>b(s,s.quantity+1),disabled:n.value[s.product_id],class:"w-8 h-8 rounded-full bg-gray-100 text-gray-600 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"}," + ",8,nt)]),t("div",ct,[t("div",ut,"$"+i(parseFloat(s.total_price||0).toFixed(2)),1),t("button",{onClick:d=>j(s),disabled:n.value[s.product_id],class:"text-red-600 hover:text-red-800 text-sm disabled:opacity-50 disabled:cursor-not-allowed"}," Remove ",8,pt)])]))),128))])])])]),t("div",gt,[t("div",vt,[t("div",mt,[e[14]||(e[14]=t("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Order Summary",-1)),t("div",xt,[t("div",ft,[e[10]||(e[10]=t("span",{class:"text-gray-600"},"Subtotal",-1)),t("span",ht,i(h.value),1)]),e[12]||(e[12]=t("div",{class:"flex justify-between"},[t("span",{class:"text-gray-600"},"Shipping"),t("span",{class:"font-medium"},"Calculated at checkout")],-1)),e[13]||(e[13]=t("div",{class:"flex justify-between"},[t("span",{class:"text-gray-600"},"Tax"),t("span",{class:"font-medium"},"Calculated at checkout")],-1)),t("div",_t,[t("div",bt,[e[11]||(e[11]=t("span",{class:"text-lg font-semibold"},"Total",-1)),t("span",yt,i(h.value),1)])])]),t("button",{onClick:q,class:"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium"}," Proceed to Checkout "),e[15]||(e[15]=t("div",{class:"mt-4 text-center"},[t("p",{class:"text-sm text-gray-500"}," Secure checkout powered by Stripe ")],-1))])])])]))])])]),_:1})],64))}};export{$t as default};
