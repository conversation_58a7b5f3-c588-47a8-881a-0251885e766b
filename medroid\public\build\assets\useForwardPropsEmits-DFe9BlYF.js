import{V as b,X as w,Y as $,r as h,w as P,C as S,H as x,c as g,J as j,G as U,Z as I,h as _,_ as F,$ as O,a0 as k}from"./vendor-BhKTHoN5.js";import{i as D}from"./index-CGRqDMLC.js";import{d as L,c as R}from"./useForwardExpose-DjhPD9_V.js";import{r as V}from"./Primitive-DSQomZit.js";function B(n,e){const t=typeof n=="string"?`${n}Context`:e,i=Symbol(t);return[a=>{const s=b(i,a);if(s||s===null)return s;throw new Error(`Injection \`${i.toString()}\` not found. Component must be used within ${Array.isArray(n)?`one of the following components: ${n.join(", ")}`:`\`${n}\``}`)},a=>(w(i,a),a)]}const[H,te]=B("ConfigProvider");let K=0;function ne(n,e="reka"){const t=H({useId:void 0});return $?`${e}-${$()}`:t.useId?`${e}-${t.useId()}`:`${e}-${++K}`}function Y(n,e){const t=h(n);function i(o){return e[t.value][o]??t.value}return{state:t,dispatch:o=>{t.value=i(o)}}}function q(n,e){var M;const t=h({}),i=h("none"),l=h(n),o=n.value?"mounted":"unmounted";let a;const s=((M=e.value)==null?void 0:M.ownerDocument.defaultView)??L,{state:d,dispatch:f}=Y(o,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}}),m=r=>{var u;if(D){const v=new CustomEvent(r,{bubbles:!1,cancelable:!1});(u=e.value)==null||u.dispatchEvent(v)}};P(n,async(r,u)=>{var y;const v=u!==r;if(await S(),v){const E=i.value,p=A(e.value);r?(f("MOUNT"),m("enter"),p==="none"&&m("after-enter")):p==="none"||p==="undefined"||((y=t.value)==null?void 0:y.display)==="none"?(f("UNMOUNT"),m("leave"),m("after-leave")):u&&E!==p?(f("ANIMATION_OUT"),m("leave")):(f("UNMOUNT"),m("after-leave"))}},{immediate:!0});const c=r=>{const u=A(e.value),v=u.includes(r.animationName),y=d.value==="mounted"?"enter":"leave";if(r.target===e.value&&v&&(m(`after-${y}`),f("ANIMATION_END"),!l.value)){const E=e.value.style.animationFillMode;e.value.style.animationFillMode="forwards",a=s==null?void 0:s.setTimeout(()=>{var p;((p=e.value)==null?void 0:p.style.animationFillMode)==="forwards"&&(e.value.style.animationFillMode=E)})}r.target===e.value&&u==="none"&&f("ANIMATION_END")},C=r=>{r.target===e.value&&(i.value=A(e.value))},T=P(e,(r,u)=>{r?(t.value=getComputedStyle(r),r.addEventListener("animationstart",C),r.addEventListener("animationcancel",c),r.addEventListener("animationend",c)):(f("ANIMATION_END"),a!==void 0&&(s==null||s.clearTimeout(a)),u==null||u.removeEventListener("animationstart",C),u==null||u.removeEventListener("animationcancel",c),u==null||u.removeEventListener("animationend",c))},{immediate:!0}),N=P(d,()=>{const r=A(e.value);i.value=d.value==="mounted"?r:"none"});return x(()=>{T(),N()}),{isPresent:g(()=>["mounted","unmountSuspended"].includes(d.value))}}function A(n){return n&&getComputedStyle(n).animationName||"none"}const ae=j({name:"Presence",props:{present:{type:Boolean,required:!0},forceMount:{type:Boolean}},slots:{},setup(n,{slots:e,expose:t}){var f;const{present:i,forceMount:l}=U(n),o=h(),{isPresent:a}=q(i,o);t({present:a});let s=e.default({present:a.value});s=V(s||[]);const d=I();if(s&&(s==null?void 0:s.length)>1){const m=(f=d==null?void 0:d.parent)!=null&&f.type.name?`<${d.parent.type.name} />`:"component";throw new Error([`Detected an invalid children for \`${m}\` for  \`Presence\` component.`,"","Note: Presence works similarly to `v-if` directly, but it waits for animation/transition to finished before unmounting. So it expect only one direct child of valid VNode type.","You can apply a few solutions:",["Provide a single child element so that `presence` directive attach correctly.","Ensure the first child is an actual element instead of a raw text node or comment node."].map(c=>`  - ${c}`).join(`
`)].join(`
`))}return()=>l.value||i.value||a.value?_(e.default({present:a.value})[0],{ref:m=>{const c=R(m);return typeof(c==null?void 0:c.hasAttribute)>"u"||(c!=null&&c.hasAttribute("data-reka-popper-content-wrapper")?o.value=c.firstElementChild:o.value=c),c}}):null}});function z(n){const e=I(),t=e==null?void 0:e.type.emits,i={};return t!=null&&t.length||console.warn(`No emitted event found. Please check component: ${e==null?void 0:e.type.__name}`),t==null||t.forEach(l=>{i[F(O(l))]=(...o)=>n(l,...o)}),i}function G(n){const e=I(),t=Object.keys((e==null?void 0:e.type.props)??{}).reduce((l,o)=>{const a=(e==null?void 0:e.type.props[o]).default;return a!==void 0&&(l[o]=a),l},{}),i=k(n);return g(()=>{const l={},o=(e==null?void 0:e.vnode.props)??{};return Object.keys(o).forEach(a=>{l[O(a)]=o[a]}),Object.keys({...t,...l}).reduce((a,s)=>(i.value[s]!==void 0&&(a[s]=i.value[s]),a),{})})}function se(n,e){const t=G(n),i=e?z(e):{};return g(()=>({...t.value,...i}))}export{ae as P,se as a,G as b,B as c,z as d,H as i,ne as u};
