const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/Create-DVj-Fr_E.js","assets/vendor-BhKTHoN5.js","assets/AppLayout.vue_vue_type_script_setup_true_lang-DwGvFXKf.js","assets/_plugin-vue_export-helper-DlAUqK2U.js","assets/Primitive-DSQomZit.js","assets/createLucideIcon-YxmScYOV.js","assets/AppLayout-B4Zax8Ug.css","assets/Edit-z5D0HmlA.js","assets/Index-yZs49uqc.js","assets/Show-CjeHg6A1.js","assets/Index-YMGgqGBS.js","assets/Show-DpFVRX9Q.js","assets/Create-DITUNbn2.js","assets/Edit-BPq7_och.js","assets/Index-cWb5kTvI.js","assets/BulkImportModal-BcLAcbfB.js","assets/SocialMediaManager-B9s4Zu_y.js","assets/SocialMediaManager-tn0RQdqM.css","assets/AppointmentDetail-DgUFnZrm.js","assets/AppointmentEdit-CCe0MLeb.js","assets/AppointmentPayment-t5LtUwzt.js","assets/Appointments-B3IU3no0.js","assets/Appointments-Dc5S3XBs.css","assets/Chat-CRykr34t.js","assets/ChatInput-DdW2-31K.js","assets/ChatInput-BmGnubPc.css","assets/useForwardPropsEmits-DFe9BlYF.js","assets/index-CGRqDMLC.js","assets/useForwardExpose-DjhPD9_V.js","assets/Chat-CSJ_LqY6.css","assets/ChatHistory-2swDB52r.js","assets/Chats-DkAdKpkK.js","assets/Clinics-TXXd7r8N.js","assets/Clubs-DFzMNNGQ.js","assets/CreditHistory-DUXZXg_L.js","assets/Credits--kXXZ_al.js","assets/Dashboard-BkjXtumA.js","assets/Dashboard_backup-BzurujvN.js","assets/Dashboard_backup-k8swJl3t.css","assets/Discover-CFKoefD1.js","assets/ComingSoon-Bg3W8jN1.js","assets/Discover-DZJ0wj3k.css","assets/EmailTemplates-CjG7Coc7.js","assets/Notifications-CfKi1zaS.js","assets/Patients-BiUEn-NZ.js","assets/Payments-DWUavcch.js","assets/Permissions-CSZFljAL.js","assets/Availability-CZCEEBVi.js","assets/Earnings-Ck_51ASy.js","assets/Patients-C-Y1Dbm6.js","assets/Products-umLN2rqz.js","assets/Schedule-BkjaS7Gh.js","assets/Services-DjEawkd7.js","assets/ProviderRegister-dXX45nKS.js","assets/InputError.vue_vue_type_script_setup_true_lang-B3hvUGHW.js","assets/Providers-D16RK9Da.js","assets/Referrals-cLlluFUu.js","assets/Services-CIf-C2Eq.js","assets/Shop-BixQvcWp.js","assets/Cart-BHz8Wbbr.js","assets/Checkout-DRbvUNJz.js","assets/OrderDetail-DG-KOp-w.js","assets/Orders-BC-lrqJD.js","assets/ProductDetail-DeTH5fnd.js","assets/SystemVerification-vwLmdmAc.js","assets/Users-GS3BeACR.js","assets/Waitlist-DjL9fBCA.js","assets/Welcome-hm7bqEyf.js","assets/Welcome-DrPWS9zi.css","assets/ConfirmPassword-DKo_Ebxu.js","assets/index-CFmBC9d8.js","assets/Label.vue_vue_type_script_setup_true_lang-dYaAjAby.js","assets/AuthLayout.vue_vue_type_script_setup_true_lang-BEoj-CWY.js","assets/ForgotPassword-DIxJmtBS.js","assets/TextLink.vue_vue_type_script_setup_true_lang-B2BUb5pV.js","assets/FounderSignup-Bm-Je2wP.js","assets/Register-BVpoNxlu.js","assets/Register-B2WbpIMy.css","assets/ResetPassword-DtF-gtz6.js","assets/VerifyEmail-BbFI8OfC.js","assets/Appearance-DiQpeJYs.js","assets/HeadingSmall.vue_vue_type_script_setup_true_lang-DVe5uQrS.js","assets/Layout.vue_vue_type_script_setup_true_lang-CjtMa_dt.js","assets/Appearance-CB0SEYXv.css","assets/AppointmentPreferences-DN8c2tWu.js","assets/useBodyScrollLock-CcBLBbmp.js","assets/Password-CRlbVUId.js","assets/Profile-5WCxFWcT.js"])))=>i.map(i=>d[i]);
import{r as h,o as I,c as E,w as D,a as v,L as S,W as y,b as w,k as V,h as C}from"./vendor-BhKTHoN5.js";const k="modulepreload",x=function(e){return"/build/"+e},R={},t=function(r,o,u){let d=Promise.resolve();if(o&&o.length>0){document.getElementsByTagName("link");const s=document.querySelector("meta[property=csp-nonce]"),_=(s==null?void 0:s.nonce)||(s==null?void 0:s.getAttribute("nonce"));d=Promise.allSettled(o.map(n=>{if(n=x(n),n in R)return;R[n]=!0;const p=n.endsWith(".css"),i=p?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${n}"]${i}`))return;const a=document.createElement("link");if(a.rel=p?"stylesheet":k,p||(a.as="script"),a.crossOrigin="",a.href=n,_&&a.setAttribute("nonce",_),document.head.appendChild(a),p)return new Promise((f,O)=>{a.addEventListener("load",f),a.addEventListener("error",()=>O(new Error(`Unable to preload CSS for ${n}`)))})}))}function c(s){const _=new Event("vite:preloadError",{cancelable:!0});if(_.payload=s,window.dispatchEvent(_),!_.defaultPrevented)throw s}return d.then(s=>{for(const _ of s||[])_.status==="rejected"&&c(_.reason);return r().catch(c)})};async function F(e,r){for(const o of Array.isArray(e)?e:[e]){const u=r[o];if(!(typeof u>"u"))return typeof u=="function"?u():u}throw new Error(`Page not found: ${e}`)}function P(e){if(!(typeof window>"u"))if(e==="system"){const o=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";document.documentElement.classList.toggle("dark",o==="dark")}else document.documentElement.classList.toggle("dark",e==="dark")}const b=(e,r,o=365)=>{if(typeof document>"u")return;const u=o*24*60*60;document.cookie=`${e}=${r};path=/;max-age=${u};SameSite=Lax`},z=()=>typeof window>"u"?null:window.matchMedia("(prefers-color-scheme: dark)"),L=()=>typeof window>"u"?null:localStorage.getItem("appearance"),$=()=>{const e=L();P(e||"system")};function j(){var r;if(typeof window>"u")return;const e=L();P(e||"system"),(r=z())==null||r.addEventListener("change",$)}function H(){const e=h("system");I(()=>{const o=localStorage.getItem("appearance");o&&(e.value=o)});function r(o){e.value=o,localStorage.setItem("appearance",o),b("appearance",o),P(o)}return{appearance:e,updateAppearance:r}}const m={small:{name:"Small",scale:.875,description:"Smaller text for better screen space"},normal:{name:"Normal",scale:1,description:"Default text size"},large:{name:"Large",scale:1.125,description:"Larger text for better readability"},xlarge:{name:"Extra Large",scale:1.25,description:"Extra large text for accessibility"}},l=h("normal"),N=()=>{const e=localStorage.getItem("medroid-font-size");e&&m[e]&&(l.value=e)},q=e=>{localStorage.setItem("medroid-font-size",e)};N();function W(){const e=E(()=>{var i;return((i=m[l.value])==null?void 0:i.scale)||1}),r=E(()=>{var i;return((i=m[l.value])==null?void 0:i.name)||"Normal"}),o=E(()=>{var i;return((i=m[l.value])==null?void 0:i.description)||""}),u=E(()=>Object.entries(m).map(([i,a])=>({key:i,...a}))),d=E(()=>({"--font-scale":e.value,"--text-xs":`${.75*e.value}rem`,"--text-sm":`${.875*e.value}rem`,"--text-base":`${1*e.value}rem`,"--text-lg":`${1.125*e.value}rem`,"--text-xl":`${1.25*e.value}rem`,"--text-2xl":`${1.5*e.value}rem`,"--text-3xl":`${1.875*e.value}rem`,"--text-4xl":`${2.25*e.value}rem`,"--text-5xl":`${3*e.value}rem`,"--text-6xl":`${3.75*e.value}rem`})),c=i=>{m[i]&&(l.value=i,q(i),p())},s=()=>{const i=Object.keys(m),a=i.indexOf(l.value);a<i.length-1&&c(i[a+1])},_=()=>{const i=Object.keys(m),a=i.indexOf(l.value);a>0&&c(i[a-1])},n=()=>{c("normal")},p=()=>{const i=document.documentElement;Object.entries(d.value).forEach(([a,f])=>{i.style.setProperty(a,f)}),document.body.className=document.body.className.replace(/font-size-\w+/g,""),document.body.classList.add(`font-size-${l.value}`)};return D(l,()=>{p()},{immediate:!0}),{currentFontSize:l,fontSizeScale:e,fontSizeName:r,fontSizeDescription:o,availableFontSizes:u,fontSizeStyles:d,setFontSize:c,increaseFontSize:s,decreaseFontSize:_,resetFontSize:n,applyFontSizeToDocument:p,FONT_SIZES:m}}const X="Medroid";v.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";v.defaults.withCredentials=!0;const g=()=>{const e=document.head.querySelector('meta[name="csrf-token"]');return e?e.content:null},A=async()=>{try{if((await fetch("/sanctum/csrf-cookie",{method:"GET",credentials:"same-origin",headers:{Accept:"application/json"}})).ok)return await new Promise(r=>setTimeout(r,100)),g()}catch(e){console.error("Failed to refresh CSRF token:",e)}return null},T=g();T&&(v.defaults.headers.common["X-CSRF-TOKEN"]=T);const U=async()=>{let e=g();return e||(console.log("Initializing CSRF token..."),e=await A(),e?console.log("CSRF token initialized successfully"):console.warn("Failed to initialize CSRF token")),e};v.interceptors.request.use(async e=>{let r=g();return r||(console.warn("No CSRF token found, attempting to refresh..."),r=await A()),r?e.headers["X-CSRF-TOKEN"]=r:console.error("Unable to obtain CSRF token"),e.headers.Accept=e.headers.Accept||"application/json",!e.headers["Content-Type"]&&!(e.data instanceof FormData)&&(e.headers["Content-Type"]="application/json"),e},e=>Promise.reject(e));v.interceptors.response.use(e=>e,async e=>{var o,u,d,c,s,_;const r=e.config;if(((o=e.response)==null?void 0:o.status)===419&&!r._retry){console.warn("CSRF token mismatch detected, attempting to refresh and retry..."),r._retry=!0;try{const n=await A();if(n)return r.headers["X-CSRF-TOKEN"]=n,v.defaults.headers.common["X-CSRF-TOKEN"]=n,await new Promise(p=>setTimeout(p,50)),v(r);console.error("Failed to refresh CSRF token, reloading page..."),setTimeout(()=>{window.confirm("Session expired. Reload page to continue?")&&window.location.reload()},1e3)}catch(n){console.error("Error during token refresh:",n)}}if(((u=e.response)==null?void 0:u.status)===500&&!r._serverRetry&&!((d=r.url)!=null&&d.includes("/logout"))){console.warn("Server error detected, attempting retry..."),r._serverRetry=!0,await new Promise(n=>setTimeout(n,1e3));try{return v(r)}catch(n){console.error("Server error retry failed:",n)}}return(c=r.url)!=null&&c.includes("/logout")?(console.log("Logout request - not intercepting"),Promise.reject(e)):(((s=e.response)==null?void 0:s.status)===401&&((_=r.url)!=null&&_.includes("/logout")||(console.warn("Authentication failed, redirecting to login..."),window.location.href="/login")),Promise.reject(e))});window.axios=v;S({title:e=>`${e} - ${X}`,resolve:e=>F(`./pages/${e}.vue`,Object.assign({"./pages/Admin/Categories/Create.vue":()=>t(()=>import("./Create-DVj-Fr_E.js"),__vite__mapDeps([0,1,2,3,4,5,6])),"./pages/Admin/Categories/Edit.vue":()=>t(()=>import("./Edit-z5D0HmlA.js"),__vite__mapDeps([7,1,2,3,4,5,6])),"./pages/Admin/Categories/Index.vue":()=>t(()=>import("./Index-yZs49uqc.js"),__vite__mapDeps([8,1,2,3,4,5,6])),"./pages/Admin/Categories/Show.vue":()=>t(()=>import("./Show-CjeHg6A1.js"),__vite__mapDeps([9,2,1,3,4,5,6])),"./pages/Admin/Orders/Index.vue":()=>t(()=>import("./Index-YMGgqGBS.js"),__vite__mapDeps([10,1,2,3,4,5,6])),"./pages/Admin/Orders/Show.vue":()=>t(()=>import("./Show-DpFVRX9Q.js"),__vite__mapDeps([11,2,1,3,4,5,6])),"./pages/Admin/Products/Create.vue":()=>t(()=>import("./Create-DITUNbn2.js"),__vite__mapDeps([12,1,2,3,4,5,6])),"./pages/Admin/Products/Edit.vue":()=>t(()=>import("./Edit-BPq7_och.js"),__vite__mapDeps([13,1,2,3,4,5,6])),"./pages/Admin/Products/Index.vue":()=>t(()=>import("./Index-cWb5kTvI.js"),__vite__mapDeps([14,1,2,3,4,5,6,15])),"./pages/Admin/SocialMediaManager.vue":()=>t(()=>import("./SocialMediaManager-B9s4Zu_y.js"),__vite__mapDeps([16,1,2,3,4,5,6,17])),"./pages/AppointmentDetail.vue":()=>t(()=>import("./AppointmentDetail-DgUFnZrm.js"),__vite__mapDeps([18,2,1,3,4,5,6])),"./pages/AppointmentEdit.vue":()=>t(()=>import("./AppointmentEdit-CCe0MLeb.js"),__vite__mapDeps([19,1,2,3,4,5,6])),"./pages/AppointmentPayment.vue":()=>t(()=>import("./AppointmentPayment-t5LtUwzt.js"),__vite__mapDeps([20,1,2,3,4,5,6])),"./pages/Appointments.vue":()=>t(()=>import("./Appointments-B3IU3no0.js"),__vite__mapDeps([21,2,1,3,4,5,6,22])),"./pages/Chat.vue":()=>t(()=>import("./Chat-CRykr34t.js"),__vite__mapDeps([23,1,2,3,4,5,6,24,25,26,27,28,29])),"./pages/ChatHistory.vue":()=>t(()=>import("./ChatHistory-2swDB52r.js"),__vite__mapDeps([30,2,1,3,4,5,6])),"./pages/Chats.vue":()=>t(()=>import("./Chats-DkAdKpkK.js"),__vite__mapDeps([31,1,2,3,4,5,6])),"./pages/Clinics.vue":()=>t(()=>import("./Clinics-TXXd7r8N.js"),__vite__mapDeps([32,1,2,3,4,5,6])),"./pages/Clubs.vue":()=>t(()=>import("./Clubs-DFzMNNGQ.js"),__vite__mapDeps([33,1,2,3,4,5,6])),"./pages/CreditHistory.vue":()=>t(()=>import("./CreditHistory-DUXZXg_L.js"),__vite__mapDeps([34,1,2,3,4,5,6])),"./pages/Credits.vue":()=>t(()=>import("./Credits--kXXZ_al.js"),__vite__mapDeps([35,1,2,3,4,5,6])),"./pages/Dashboard.vue":()=>t(()=>import("./Dashboard-BkjXtumA.js"),__vite__mapDeps([36,1,2,3,4,5,6])),"./pages/Dashboard_backup.vue":()=>t(()=>import("./Dashboard_backup-BzurujvN.js"),__vite__mapDeps([37,1,2,3,4,5,6,38])),"./pages/Discover.vue":()=>t(()=>import("./Discover-CFKoefD1.js"),__vite__mapDeps([39,1,2,3,4,5,6,40,41])),"./pages/EmailTemplates.vue":()=>t(()=>import("./EmailTemplates-CjG7Coc7.js"),__vite__mapDeps([42,1,2,3,4,5,6])),"./pages/Notifications.vue":()=>t(()=>import("./Notifications-CfKi1zaS.js"),__vite__mapDeps([43,2,1,3,4,5,6])),"./pages/Patients.vue":()=>t(()=>import("./Patients-BiUEn-NZ.js"),__vite__mapDeps([44,1,2,3,4,5,6])),"./pages/Payments.vue":()=>t(()=>import("./Payments-DWUavcch.js"),__vite__mapDeps([45,1,2,3,4,5,6])),"./pages/Permissions.vue":()=>t(()=>import("./Permissions-CSZFljAL.js"),__vite__mapDeps([46,2,1,3,4,5,6])),"./pages/Provider/Availability.vue":()=>t(()=>import("./Availability-CZCEEBVi.js"),__vite__mapDeps([47,1,2,3,4,5,6])),"./pages/Provider/Earnings.vue":()=>t(()=>import("./Earnings-Ck_51ASy.js"),__vite__mapDeps([48,2,1,3,4,5,6])),"./pages/Provider/Patients.vue":()=>t(()=>import("./Patients-C-Y1Dbm6.js"),__vite__mapDeps([49,1,2,3,4,5,6])),"./pages/Provider/Products.vue":()=>t(()=>import("./Products-umLN2rqz.js"),__vite__mapDeps([50,1,2,3,4,5,6,15])),"./pages/Provider/Schedule.vue":()=>t(()=>import("./Schedule-BkjaS7Gh.js"),__vite__mapDeps([51,1,2,3,4,5,6])),"./pages/Provider/Services.vue":()=>t(()=>import("./Services-DjEawkd7.js"),__vite__mapDeps([52,2,1,3,4,5,6])),"./pages/ProviderRegister.vue":()=>t(()=>import("./ProviderRegister-dXX45nKS.js"),__vite__mapDeps([53,1,54])),"./pages/Providers.vue":()=>t(()=>import("./Providers-D16RK9Da.js"),__vite__mapDeps([55,1,2,3,4,5,6])),"./pages/Referrals.vue":()=>t(()=>import("./Referrals-cLlluFUu.js"),__vite__mapDeps([56,2,1,3,4,5,6])),"./pages/Services.vue":()=>t(()=>import("./Services-CIf-C2Eq.js"),__vite__mapDeps([57,1,2,3,4,5,6])),"./pages/Shop.vue":()=>t(()=>import("./Shop-BixQvcWp.js"),__vite__mapDeps([58,1,2,3,4,5,6,40])),"./pages/Shop/Cart.vue":()=>t(()=>import("./Cart-BHz8Wbbr.js"),__vite__mapDeps([59,2,1,3,4,5,6])),"./pages/Shop/Checkout.vue":()=>t(()=>import("./Checkout-DRbvUNJz.js"),__vite__mapDeps([60,1,2,3,4,5,6])),"./pages/Shop/OrderDetail.vue":()=>t(()=>import("./OrderDetail-DG-KOp-w.js"),__vite__mapDeps([61,2,1,3,4,5,6])),"./pages/Shop/Orders.vue":()=>t(()=>import("./Orders-BC-lrqJD.js"),__vite__mapDeps([62,1,2,3,4,5,6])),"./pages/Shop/ProductDetail.vue":()=>t(()=>import("./ProductDetail-DeTH5fnd.js"),__vite__mapDeps([63,2,1,3,4,5,6])),"./pages/SystemVerification.vue":()=>t(()=>import("./SystemVerification-vwLmdmAc.js"),__vite__mapDeps([64,1,2,3,4,5,6])),"./pages/Users.vue":()=>t(()=>import("./Users-GS3BeACR.js"),__vite__mapDeps([65,1,2,3,4,5,6])),"./pages/Waitlist.vue":()=>t(()=>import("./Waitlist-DjL9fBCA.js"),__vite__mapDeps([66,1,2,3,4,5,6])),"./pages/Welcome.vue":()=>t(()=>import("./Welcome-hm7bqEyf.js"),__vite__mapDeps([67,1,24,3,25,68])),"./pages/auth/ConfirmPassword.vue":()=>t(()=>import("./ConfirmPassword-DKo_Ebxu.js"),__vite__mapDeps([69,1,54,70,4,71,28,27,72,5])),"./pages/auth/ForgotPassword.vue":()=>t(()=>import("./ForgotPassword-DIxJmtBS.js"),__vite__mapDeps([73,1,54,74,70,4,71,28,27,72,5])),"./pages/auth/FounderSignup.vue":()=>t(()=>import("./FounderSignup-Bm-Je2wP.js"),__vite__mapDeps([75,1,54])),"./pages/auth/Register.vue":()=>t(()=>import("./Register-BVpoNxlu.js"),__vite__mapDeps([76,1,54,3,77])),"./pages/auth/ResetPassword.vue":()=>t(()=>import("./ResetPassword-DtF-gtz6.js"),__vite__mapDeps([78,1,54,70,4,71,28,27,72,5])),"./pages/auth/VerifyEmail.vue":()=>t(()=>import("./VerifyEmail-BbFI8OfC.js"),__vite__mapDeps([79,1,74,70,4,72,5])),"./pages/settings/Appearance.vue":()=>t(()=>import("./Appearance-DiQpeJYs.js"),__vite__mapDeps([80,1,3,5,81,2,4,6,82,70,27,83])),"./pages/settings/AppointmentPreferences.vue":()=>t(()=>import("./AppointmentPreferences-DN8c2tWu.js"),__vite__mapDeps([84,1,82,70,4,27,71,28,26,85])),"./pages/settings/Password.vue":()=>t(()=>import("./Password-CRlbVUId.js"),__vite__mapDeps([86,1,54,2,3,4,5,6,82,70,27,81,71,28])),"./pages/settings/Profile.vue":()=>t(()=>import("./Profile-5WCxFWcT.js"),__vite__mapDeps([87,1,81,54,70,4,26,27,28,85,5,71,2,3,6,82]))})),setup({el:e,App:r,props:o,plugin:u}){w({render:()=>C(r,o)}).use(u).use(V).mount(e)},progress:{color:"#4B5563"}}).then(()=>{y.on("error",e=>{var r,o,u,d,c,s;if(console.log("Inertia request error:",e),(o=(r=e.response)==null?void 0:r.url)!=null&&o.includes("/logout")||(c=(d=(u=e.response)==null?void 0:u.config)==null?void 0:d.url)!=null&&c.includes("/logout")){console.log("Logout error handled gracefully, redirecting to home"),window.location.href="/";return}if(((s=e.response)==null?void 0:s.status)===419){console.warn("CSRF error on Inertia request, reloading page"),window.location.reload();return}})}).catch(e=>{console.error("Error initializing Inertia app:",e)});j();const{applyFontSizeToDocument:K}=W();K();"serviceWorker"in navigator&&navigator.serviceWorker.getRegistrations().then(function(e){for(const r of e)(r.scope.includes("datadog")||r.scope.includes("sw.js"))&&r.unregister()}).catch(function(e){});U().catch(e=>{console.warn("Failed to initialize CSRF token on startup:",e)});export{H as a,W as u};
