import{z as H,c as y,r as b,o as K,d as o,e as a,f as w,u as _,m as O,g as f,i as t,j as Q,n as i,l as c,v as p,t as u,F as P,p as I,q as j,s as E,P as D,x as B,y as Y}from"./vendor-BhKTHoN5.js";import{_ as X}from"./AppLayout.vue_vue_type_script_setup_true_lang-DwGvFXKf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const Z={class:"flex items-center justify-between"},ee={class:"flex mt-2","aria-label":"Breadcrumb"},te={class:"inline-flex items-center space-x-1 md:space-x-3"},re={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},le={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},se={class:"py-12"},ae={class:"mx-auto max-w-4xl sm:px-6 lg:px-8"},oe={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},de={class:"p-6 text-gray-900 dark:text-gray-100"},ue={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ie={key:0,class:"mt-1 text-sm text-red-600"},ne={key:0,class:"mt-1 text-sm text-red-600"},me=["value"],ce={key:0,class:"mt-1 text-sm text-red-600"},ge={key:0,class:"mt-1 text-sm text-red-600"},pe={key:0,class:"mt-1 text-sm text-red-600"},be={key:0,class:"mt-1 text-sm text-red-600"},ye={key:0,class:"mt-1 text-sm text-red-600"},fe={key:0,class:"mt-1 text-sm text-red-600"},xe={class:"space-y-6"},ve={class:"flex items-start space-x-4"},ke={class:"flex-1"},he={key:0,class:"mt-1 text-sm text-red-600"},we={key:0,class:"relative"},_e=["src"],Pe={class:"space-y-4"},Ce={key:0,class:"text-sm text-red-600"},Ue={key:1,class:"grid grid-cols-2 md:grid-cols-4 gap-4"},Ve=["src","alt"],qe=["onClick"],Fe={class:"mt-1 text-xs text-gray-500 truncate"},Ie={key:0,class:"grid grid-cols-1 md:grid-cols-3 gap-6"},De={key:0,class:"mt-1 text-sm text-red-600"},Be={key:0,class:"mt-1 text-sm text-red-600"},Ge={key:0,class:"mt-1 text-sm text-red-600"},Me={class:"flex items-center space-x-6"},Se={class:"flex items-center"},je={class:"flex items-center"},Ee={class:"flex justify-end space-x-3"},Ne=["disabled"],Je={__name:"Create",setup($e){const N=H(),$=y(()=>{var d;return(d=N.props.auth)==null?void 0:d.user}),C=y(()=>{var d;return((d=$.value)==null?void 0:d.role)==="provider"}),U=y(()=>C.value?[{title:"Dashboard",href:"/dashboard"},{title:"My Products",href:"/provider/products"},{title:"Create Product",href:"/provider/products/create"}]:[{title:"Dashboard",href:"/dashboard"},{title:"Products",href:"/admin/products"},{title:"Create Product",href:"/admin/products/create"}]),G=y(()=>C.value?"/provider":"/admin"),V=y(()=>C.value?"/provider/products":"/admin/products"),x=b(!1),M=b([]),l=b({name:"",description:"",short_description:"",type:"physical",category_id:"",price:"",sale_price:"",sku:"",stock_quantity:"",manage_stock:!0,weight:"",dimensions:"",is_featured:!1,is_active:!0,digital_files:[],download_limit:"",download_expiry_days:""}),v=b(null),k=b(null),q=b([]),h=b([]),s=b({}),A=async()=>{try{const d=await window.axios.get(`${G.value}/products/create`);M.value=d.data.categories||[]}catch(d){console.error("Error fetching categories:",d)}},L=d=>{const e=d.target.files[0];if(e){v.value=e;const r=new FileReader;r.onload=n=>{k.value=n.target.result},r.readAsDataURL(e)}},R=()=>{v.value=null,k.value=null;const d=document.querySelector('input[type="file"][name="featured_image"]');d&&(d.value="")},T=d=>{Array.from(d.target.files).forEach(r=>{q.value.push(r);const n=new FileReader;n.onload=m=>{h.value.push({file:r,preview:m.target.result,name:r.name})},n.readAsDataURL(r)})},z=d=>{q.value.splice(d,1),h.value.splice(d,1)},J=async()=>{var d,e,r,n;x.value=!0,s.value={};try{const m=new FormData;Object.keys(l.value).forEach(g=>{l.value[g]!==null&&l.value[g]!==""&&(Array.isArray(l.value[g])?l.value[g].forEach((F,W)=>{m.append(`${g}[${W}]`,F)}):m.append(g,l.value[g]))}),v.value&&m.append("featured_image",v.value),q.value.forEach((g,F)=>{m.append(`gallery_images[${F}]`,g)});const S=await window.axios.post(`${G.value}/save-product`,m,{headers:{"Content-Type":"multipart/form-data"}});S.data.success?window.location.href=V.value:alert("Error creating product: "+S.data.message)}catch(m){(e=(d=m.response)==null?void 0:d.data)!=null&&e.errors?s.value=m.response.data.errors:alert("Error creating product: "+(((n=(r=m.response)==null?void 0:r.data)==null?void 0:n.message)||m.message))}finally{x.value=!1}};return K(()=>{A()}),(d,e)=>(a(),o(P,null,[w(_(O),{title:"Create Product"}),w(X,null,{header:f(()=>[t("div",Z,[t("div",null,[e[14]||(e[14]=t("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Create Product ",-1)),t("nav",ee,[t("ol",te,[(a(!0),o(P,null,I(U.value,(r,n)=>(a(),o("li",{key:n,class:"inline-flex items-center"},[n<U.value.length-1?(a(),Y(_(D),{key:0,href:r.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:f(()=>[B(u(r.title),1)]),_:2},1032,["href"])):(a(),o("span",re,u(r.title),1)),n<U.value.length-1?(a(),o("svg",le,e[13]||(e[13]=[t("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):i("",!0)]))),128))])])]),w(_(D),{href:V.value,class:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"},{default:f(()=>e[15]||(e[15]=[B(" Back to Products ")])),_:1},8,["href"])])]),default:f(()=>[t("div",se,[t("div",ae,[t("div",oe,[t("div",de,[t("form",{onSubmit:Q(J,["prevent"]),class:"space-y-6"},[t("div",ue,[t("div",null,[e[16]||(e[16]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Product Name *",-1)),c(t("input",{"onUpdate:modelValue":e[0]||(e[0]=r=>l.value.name=r),type:"text",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[p,l.value.name]]),s.value.name?(a(),o("p",ie,u(s.value.name[0]),1)):i("",!0)]),t("div",null,[e[17]||(e[17]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"SKU *",-1)),c(t("input",{"onUpdate:modelValue":e[1]||(e[1]=r=>l.value.sku=r),type:"text",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[p,l.value.sku]]),s.value.sku?(a(),o("p",ne,u(s.value.sku[0]),1)):i("",!0)]),t("div",null,[e[19]||(e[19]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Category *",-1)),c(t("select",{"onUpdate:modelValue":e[2]||(e[2]=r=>l.value.category_id=r),required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},[e[18]||(e[18]=t("option",{value:""},"Select Category",-1)),(a(!0),o(P,null,I(M.value,r=>(a(),o("option",{key:r.id,value:r.id},u(r.name),9,me))),128))],512),[[j,l.value.category_id]]),s.value.category_id?(a(),o("p",ce,u(s.value.category_id[0]),1)):i("",!0)]),t("div",null,[e[21]||(e[21]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Type *",-1)),c(t("select",{"onUpdate:modelValue":e[3]||(e[3]=r=>l.value.type=r),required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},e[20]||(e[20]=[t("option",{value:"physical"},"Physical",-1),t("option",{value:"digital"},"Digital",-1)]),512),[[j,l.value.type]]),s.value.type?(a(),o("p",ge,u(s.value.type[0]),1)):i("",!0)]),t("div",null,[e[22]||(e[22]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Price *",-1)),c(t("input",{"onUpdate:modelValue":e[4]||(e[4]=r=>l.value.price=r),type:"number",step:"0.01",min:"0",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[p,l.value.price]]),s.value.price?(a(),o("p",pe,u(s.value.price[0]),1)):i("",!0)]),t("div",null,[e[23]||(e[23]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Sale Price",-1)),c(t("input",{"onUpdate:modelValue":e[5]||(e[5]=r=>l.value.sale_price=r),type:"number",step:"0.01",min:"0",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[p,l.value.sale_price]]),s.value.sale_price?(a(),o("p",be,u(s.value.sale_price[0]),1)):i("",!0)])]),t("div",null,[e[24]||(e[24]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Description *",-1)),c(t("textarea",{"onUpdate:modelValue":e[6]||(e[6]=r=>l.value.description=r),rows:"4",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[p,l.value.description]]),s.value.description?(a(),o("p",ye,u(s.value.description[0]),1)):i("",!0)]),t("div",null,[e[25]||(e[25]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Short Description",-1)),c(t("textarea",{"onUpdate:modelValue":e[7]||(e[7]=r=>l.value.short_description=r),rows:"2",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[p,l.value.short_description]]),s.value.short_description?(a(),o("p",fe,u(s.value.short_description[0]),1)):i("",!0)]),t("div",xe,[e[30]||(e[30]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-gray-100"},"Product Images",-1)),t("div",null,[e[27]||(e[27]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Featured Image",-1)),t("div",ve,[t("div",ke,[t("input",{type:"file",name:"featured_image",accept:"image/*",onChange:L,class:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"},null,32),e[26]||(e[26]=t("p",{class:"mt-1 text-sm text-gray-500"},"PNG, JPG, GIF up to 10MB",-1)),s.value.featured_image?(a(),o("p",he,u(s.value.featured_image[0]),1)):i("",!0)]),k.value?(a(),o("div",we,[t("img",{src:k.value,alt:"Featured image preview",class:"w-24 h-24 object-cover rounded-lg border border-gray-300"},null,8,_e),t("button",{type:"button",onClick:R,class:"absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"}," × ")])):i("",!0)])]),t("div",null,[e[29]||(e[29]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Gallery Images",-1)),t("div",Pe,[t("input",{type:"file",multiple:"",accept:"image/*",onChange:T,class:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"},null,32),e[28]||(e[28]=t("p",{class:"text-sm text-gray-500"},"PNG, JPG, GIF up to 10MB each. You can select multiple images.",-1)),s.value.gallery_images?(a(),o("p",Ce,u(s.value.gallery_images[0]),1)):i("",!0),h.value.length>0?(a(),o("div",Ue,[(a(!0),o(P,null,I(h.value,(r,n)=>(a(),o("div",{key:n,class:"relative"},[t("img",{src:r.preview,alt:r.name,class:"w-full h-24 object-cover rounded-lg border border-gray-300"},null,8,Ve),t("button",{type:"button",onClick:m=>z(n),class:"absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"}," × ",8,qe),t("p",Fe,u(r.name),1)]))),128))])):i("",!0)])])]),l.value.type==="physical"?(a(),o("div",Ie,[t("div",null,[e[31]||(e[31]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Stock Quantity",-1)),c(t("input",{"onUpdate:modelValue":e[8]||(e[8]=r=>l.value.stock_quantity=r),type:"number",min:"0",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[p,l.value.stock_quantity]]),s.value.stock_quantity?(a(),o("p",De,u(s.value.stock_quantity[0]),1)):i("",!0)]),t("div",null,[e[32]||(e[32]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Weight (kg)",-1)),c(t("input",{"onUpdate:modelValue":e[9]||(e[9]=r=>l.value.weight=r),type:"number",step:"0.01",min:"0",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[p,l.value.weight]]),s.value.weight?(a(),o("p",Be,u(s.value.weight[0]),1)):i("",!0)]),t("div",null,[e[33]||(e[33]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Dimensions",-1)),c(t("input",{"onUpdate:modelValue":e[10]||(e[10]=r=>l.value.dimensions=r),type:"text",placeholder:"L x W x H",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[p,l.value.dimensions]]),s.value.dimensions?(a(),o("p",Ge,u(s.value.dimensions[0]),1)):i("",!0)])])):i("",!0),t("div",Me,[t("label",Se,[c(t("input",{"onUpdate:modelValue":e[11]||(e[11]=r=>l.value.is_featured=r),type:"checkbox",class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[E,l.value.is_featured]]),e[34]||(e[34]=t("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"},"Featured Product",-1))]),t("label",je,[c(t("input",{"onUpdate:modelValue":e[12]||(e[12]=r=>l.value.is_active=r),type:"checkbox",class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[E,l.value.is_active]]),e[35]||(e[35]=t("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"},"Active",-1))])]),t("div",Ee,[w(_(D),{href:V.value,class:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"},{default:f(()=>e[36]||(e[36]=[B(" Cancel ")])),_:1},8,["href"]),t("button",{type:"submit",disabled:x.value,class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"},u(x.value?"Creating...":"Create Product"),9,Ne)])],32)])])])])]),_:1})],64))}};export{Je as default};
